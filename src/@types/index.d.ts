import nodeFs from 'fs'
import Path from 'node:path'
import http from 'http'
import https from 'node:https'
import net from 'net'
import Buffer from 'node:buffer';
import { UmamiBaseClient } from "@/utils/umami";

declare global {
  interface Window {
    api: Record<string, any>
    fs: typeof nodeFs
    path: typeof Path
    http: typeof http
    https: typeof https
    net: typeof net
    nodeRequire: NodeRequire
    nodeBuffer: Buffer
  }
}
