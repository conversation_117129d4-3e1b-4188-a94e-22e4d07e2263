import dayjs, { type Dayjs } from 'dayjs';
const typeMap: Record<string, string> = {
  '[object Number]': 'number',
  '[object Boolean]': 'boolean',
  '[object String]': 'string',
  '[object Array]': 'array',
  '[object Object]': 'object',
  '[object Function]': 'function',
  '[object Undefined]': 'undefined',
  '[object Null]': 'null',
};

function isType(value: any, type: string) {
  const toString = Object.prototype.toString;
  return typeMap[toString.call(value)] === type;
}

export function isObject(value: any): value is object {
  return isType(value, 'object');
}

export function isArray<T>(value: any[T]): value is Array<T> {
  return isType(value, 'array');
}

export function isString(value: any): value is string {
  return isType(value, 'string');
}

export function isNumber(value: any): value is number {
  return isType(value, 'number');
}

/**
 * 动态加载脚本
 * @param url 脚本路径
 * @param callback onload 回调
 */
function loadScript(url: string, callback?: () => void) {
  const script = document.createElement('script');
  script.type = 'text/javascript';
  script.src = url;
  script.async = true;

  script.onload = function () {
    if (callback) {
      callback();
    }
  };

  document.head.appendChild(script);
}

function camelToSnake(camelCase: string) {
  return camelCase
    .replace(/([A-Z])/g, '_$1') // 在大写字母前加上下划线
    .toLowerCase(); // 转换为小写字母
}

function snakeToCamel(snakeCase: string) {
  return snakeCase
    .toLowerCase() // 将字符串转为小写
    .replace(/_./g, (match) => match.charAt(1).toUpperCase()); // 替换下划线及其后面的字符
}

function dateToConvertDayjs(date?: string, time?: string) {
  return dayjs(date + ' ' + (time || ''));
}
function dateToConvertStr(date?: string, time?: string) {
  if (!date && !time) {
    return '';
  }
  const dayjs = dateToConvertDayjs(date, time);
  if (date && time) {
    return dayjs.format('YYYY-MM-DD HH:mm');
  }
  return dayjs.format('YYYY-MM-DD');
}

function getDayjsStartWeek(time: Dayjs) {
  const weekday = time.day();
  const we = weekday === 0 ? -1 : 0
  return  time.add(we, 'week').startOf('week').add(1, 'day')
}

function getDayjsEndWeek(time: Dayjs) {
  const weekday = time.day();
  const we = weekday === 0 ? -1 : 0;
  return time.add(we, 'week').endOf('week').add(1, 'day')
}


function dateFormat(dateStr: string) {
  const date = dayjs(dateStr).startOf('day');
  const now = dayjs().startOf('day');
  if (dayjs(date).year() === now.year()) {
    return dayjs(date).format('MM/DD');
  } else {
    return dayjs(date).format('YY/MM/DD');
  }
}

function dateFormatLabel(dateStr: string) {
  const date = dayjs(dateStr).startOf('day');
  const now = dayjs().startOf('day');
  if (date.isSame(now, 'day')) {
    return '今天';
  } else if (now.subtract(1, 'day').isSame(date, 'day')) {
    return '昨天';
  } else if (now.add(1, 'day').isSame(date, 'day')) {
    return '明天';
  }

  if (date.year() === now.year()) {
    return date.format('MM/DD');
  } else {
    return date.format('YY/MM/DD');
  }
}

/**
 * 判断出 今天/昨天/明天
 * @param dateRange
 */
function dateRangeFormatLabel(dateRange?: number[]): string {
  if (!dateRange || dateRange.filter((item) => item).length != 2) {
    return '';
  }

  const [start, end] = dateRange;
  const startDate = dayjs(start);
  const endDate = dayjs(end);
  const now = dayjs();

  // 先检查是否是完整的一周（周一到周日）
  const isFullWeek = startDate.day() === 1 && endDate.day() === 0;

  if (isFullWeek) {
    // 获取当前日期是周几（0-6，0是周日）
    const currentDay = now.day();

    // 计算本周一
    let currentWeekMonday;
    if (currentDay === 0) {
      // 如果是周日，本周一是6天前
      currentWeekMonday = now.subtract(6, 'day').startOf('day');
    } else {
      // 其他情况，本周一是 currentDay - 1 天前
      currentWeekMonday = now.subtract(currentDay - 1, 'day').startOf('day');
    }

    // 计算本周日
    const currentWeekSunday = currentWeekMonday.add(6, 'day');

    // 判断是否是本周
    if (startDate.isSame(currentWeekMonday, 'day') && endDate.isSame(currentWeekSunday, 'day')) {
      return '本周';
    }

    // 判断是否是上周
    const lastWeekMonday = currentWeekMonday.subtract(7, 'day');
    const lastWeekSunday = currentWeekSunday.subtract(7, 'day');
    if (startDate.isSame(lastWeekMonday, 'day') && endDate.isSame(lastWeekSunday, 'day')) {
      return '上周';
    }
  }

  // 判断是否是上月
  const lastMonthStart = now.subtract(1, 'month').startOf('month');
  const lastMonthEnd = now.subtract(1, 'month').endOf('month');
  if (startDate.isSame(lastMonthStart, 'day') && endDate.isSame(lastMonthEnd, 'day')) {
    return '上月';
  }

  // 如果开始和结束是同一天
  if (startDate.isSame(endDate, 'day')) {
    return dateFormatLabel(startDate.format());
  }

  // 其他情况直接返回日期格式
  const startLabel =
    startDate.year() === now.year() ? startDate.format('MM/DD') : startDate.format('YY/MM/DD');

  const endLabel =
    endDate.year() === now.year() ? endDate.format('MM/DD') : endDate.format('YY/MM/DD');

  return `${startLabel}-${endLabel}`;
}

/**
 * 随机数
 * @param min 最小值
 * @param max 最大值
 */
function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 将十六进制颜色代码转换为 RGB 数组或字符串
 * @param {string} hex - 十六进制颜色代码（可以带#或不带#）
 * @param {boolean} [asString=false] - 是否返回字符串格式，例如 "136, 144, 156"
 * @returns {Array|String} - RGB 数组或字符串
 */
function hexToRgb(hex: string, asString = false) {
  // 移除前缀 '#'（如果有的话）
  hex = hex.replace(/^#/, '');

  // 处理三位十六进制（例如 #abc）
  if (hex.length === 3) {
    hex = hex
      .split('')
      .map((c) => c + c)
      .join('');
  }

  // 确保长度为6
  if (hex.length !== 6) {
    throw new Error('无效的十六进制颜色代码');
  }

  // 解析红、绿、蓝
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  if (asString) {
    return `rgb(${r}, ${g}, ${b})`;
  }

  return `${r}, ${g}, ${b}`;
}

/**
 * 计算输入框光标位置
 * @param value 输入框值
 * @param e 输入框事件对象
 */
export function calcInputPosition(
  value: string,
  e: InputEvent,
): { left: number; top: number } | undefined {
  const input = e.target as HTMLInputElement;
  const rect = input.getBoundingClientRect();
  const cursorPosition = input.selectionStart;
  if (!cursorPosition) {
    return;
  }
  // 创建一个临时的 span 元素来计算文本宽度
  const span = document.createElement('span');
  span.style.visibility = 'hidden';
  span.style.position = 'absolute';
  span.style.whiteSpace = 'pre';
  span.style.top = '0px';
  span.style.left = '0px';
  span.style.font = window.getComputedStyle(input).font;
  span.textContent = value.substring(0, cursorPosition);
  document.body.appendChild(span);

  // 计算光标位置的像素坐标
  const cursorX = rect.left + span.offsetWidth;
  const cursorY = rect.top;

  return { left: cursorX, top: cursorY };
}

export default {
  isString,
  isNumber,
  isArray,
  isObject,
  loadScript,
  camelToSnake,
  snakeToCamel,
  getRandomInt,
  dateFormat,
  dateFormatLabel,
  dateToConvertDayjs,
  dateToConvertStr,
  getDayjsStartWeek,
  getDayjsEndWeek,
  hexToRgb,
  calcInputPosition,
  dateRangeFormatLabel,
};
