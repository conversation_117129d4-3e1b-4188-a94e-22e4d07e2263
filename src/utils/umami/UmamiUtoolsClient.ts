import { UmamiBaseClient} from "./UmamiBaseClient.ts";
import type { IUmamiConfig, IOptions, ILocation } from "./UmamiBaseClient.ts";
import { utoolsApi } from '@/api/ucloud';
/**
 * Umami 客户端类
 * 用于向 Umami 发送页面浏览和事件数据
 */
export class UmamiUtoolsClient extends UmamiBaseClient {

  constructor(config: IUmamiConfig) {
    super(config);
    this.trackChain().then(() => {});
  }

  /**
   * 发送 HTTP 请求到 Umami
   * @param path - API 路径
   * @param data - 要发送的数据
   * @private
   */
  private async sendRequest(path: string, data: any): Promise<void> {
    console.log(this.config.baseUrl)
    return new Promise((resolve, reject) => {
      const options = {
        hostname: new URL(this.config.baseUrl).hostname,
        port: 443,
        path: path,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      };
      console.log('options', options)
      const req = window.https.request(options, (res: any) => {
        let responseData = '';

        res.on('data', (chunk: any) => {
          responseData += chunk;
        });

        res.on('end', () => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve();
          } else {
            reject(new Error(`请求失败，状态码 ${res.statusCode}: ${responseData}`));
          }
        });
      });

      req.on('error', (error: Error) => {
        reject(error);
      });

      req.write(JSON.stringify(data));
      req.end();
    });
  }

  async sendData(payload: any, eventType: 'event' | 'identify' = "event") {
    // 开发环境打印
    utools.isDev() && console.log('trackEvent', payload);
    await this.sendRequest(new URL(this.config.baseUrl).pathname, {
      type: eventType,
      payload: payload
    });
  }

  /**
   * 链路事件
   */
  async trackChain() {
    const payload = {
      ...await this.getBaseData(),
      data: {
        utools: utools.getAppVersion(),
      }
    };
    await this.sendData(payload, 'event');
  }

  /**
   * 追踪自定义事件
   * @param eventName - 事件名称
   * @param eventData - 事件的附加数据（可选）
   * @param options
   */
  async trackEvent(eventName: string, eventData?: Record<string, any>, options: IOptions = {}): Promise<void> {
    try {
      const payload = {
        ...await this.getBaseData(),
        name: eventName,
        data: eventData || {},
        ...options
      };
      await this.sendData(payload, 'event');
    }catch (e) {
      console.log('出现异常', e);
    }
  }

  async identifyUser() {
    try {
      const user = utools.getUser();
      const payload = {
        ...await this.getBaseData(),
        data: {
          username: user?.nickname || '未登录',
          avatar: user?.avatar || '',
          utoolsVip: user?.type === 'member',
          utools: utools.getAppVersion(),
        }
      };
      await this.sendData(payload, 'identify');
    }catch (e) {
      console.log('出现异常', e);
    }
  }

  private async getBaseData() {
    return {
      id: await this.getUserId(),
      website: this.config.websiteId,
      url: window.location.hash,
      userAgent: navigator.userAgent,
      device: 'desktop',
      browser: 'utools',
      hostname: this.config.hostname || window.location.hostname,
    }
  }

  private async getUserId() {
    let userOpenId = utools.dbCryptoStorage.getItem<string>('u');
    if (userOpenId) {
      return utools.getUser()?.nickname + "|" + userOpenId;
    }
    try {
      userOpenId = await utoolsApi.getUserOpenId();
      if (userOpenId) {
        utools.dbCryptoStorage.setItem('u', userOpenId);
        return utools.getUser()?.nickname + "|" + userOpenId;
      }
      return (utools.getUser()?.nickname || '未登录') + "|" +  utools.getNativeId();
    }catch (e) {
      return (utools.getUser()?.nickname || '未登录') + "|" +  utools.getNativeId();
    }
  }
}
