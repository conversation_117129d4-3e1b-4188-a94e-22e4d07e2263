/**
 * Umami 配置接口
 * @interface IUmamiConfig
 */
export interface IUmamiConfig {
  /** Umami 实例的基础 URL */
  baseUrl: string;
  /** 网站 ID */
  websiteId: string;
  /** 主机名称（可选） */
  hostname?: string;
}

export interface ILocation {
  country: string;
  region: string;
  city: string;
}

export interface IOptions {
  referrer?: string;
  language?: string;
  screen?: string;
  title?: string;
  url?: string;
  /**
   * 用户唯一 id
   */
  id?: string;
  location?: ILocation
}
export interface IEventData {
  /**
   * 其他字段
   */
  [key: string]: any;
}
export abstract class UmamiBaseClient {
  protected config: IUmamiConfig;

  /**
   * 创建 Umami 客户端实例
   * @param config - Umami 配置对象
   */
  constructor(config: IUmamiConfig) {
    this.config = config;
  }

  /**
   * 追踪自定义事件
   * @param eventName - 事件名称
   * @param eventData - 事件的附加数据（可选）
   * @param options
   */
  abstract trackEvent(eventName: string, eventData?: IEventData, options?: IOptions): Promise<void>;

  abstract identifyUser(): Promise<void>;
}
