import type { Driver, DriveStep } from "driver.js";

export function registerCollectScene(driverObj: Driver): DriveStep[] {
  return [
    {
      element: '#collectScene',
      popover: {
        title: '场景',
        description: '根据不同场景便捷设置任务内的标识',
        side: "right",
        align: 'start',
        onPrevClick() {
          (document.querySelector('#fourQuadrant') as any)?.click();
        }
      },
      onHighlightStarted() {
        (document.querySelector('#collectScene') as any)?.click();
      }
    },
    {
      element: '#collect-scene-main',
      popover: {
        description: '默认三种创建任务的场景, 可以在 uTools 输入框中直接输入 dxxx 直接创建今天的任务',
        side: "over",
      },
    },
    {
      element: '#collect-scene-save',
      popover: {
        description: '在修改完场景配置时一定单击保存按钮',
        side: "left",
      },
    },
  ]
}
