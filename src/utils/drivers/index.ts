import { type Config, type Driver, driver, type DriveStep, type State } from 'driver.js';
import { registerFourQuadrant } from '@/utils/drivers/fourQuadrant.ts';
import { registerTaskList } from '@/utils/drivers/taskList.ts';
import { registerCalendar } from '@/utils/drivers/calendar.ts';
import {registerCollectScene} from "@/utils/drivers/collectScene.ts";
export function startDrivers() {
  const driverObj = driver({
    popoverClass: 'driver-class',
    allowClose: true,
    doneBtnText: '完成',
    prevBtnText: '上一步',
    nextBtnText: '下一步',
    onPrevClick: (element: Element | undefined, step: DriveStep, opts: {
      config: Config;
      state: State;
      driver: Driver;
    }) => {
      console.log(element, step, opts);
      driverObj.movePrevious();
    }
  });
  const driveSteps: DriveStep[] = [];
  driveSteps.push(...registerTaskList(driverObj));
  driveSteps.push(...registerCalendar(driverObj));
  driveSteps.push(...registerFourQuadrant(driverObj));
  driveSteps.push(...registerCollectScene(driverObj));
  driverObj.setSteps(driveSteps);
  driverObj.drive();
}
