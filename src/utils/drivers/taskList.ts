import type { Driver, DriveStep } from 'driver.js';

export function registerTaskList(driverObj: Driver): DriveStep[] {
  return  [
    {
      element: '#taskList',
      popover: {
        title: '任务列表',
        description: '可以根据各种视图对任务进行管理',
        side: "right",
        align: 'start'
      },
      onHighlightStarted() {
        (document.querySelector('#taskList') as any)?.click();
      }
    },
    {
      element: '#toDay',
      popover: {
        description: '开始时间为今日的任务列表',
      },
    },
    {
      element: '.task-create-input',
      popover: {
        title: '任务输入框',
        description: `输入任务标题, 按下回车即可完成添加<br/>[${utools.isMacOS() ? 'command+n' : 'ctrl+n'}输入框可快速获得输入状态]`,
      }
    },
    {
      element: '.task-create-input .task-level',
      popover: {
        description: '选择创建任务优先级',
      }
    },
    {
      element: '.task-create-input .task-select-group',
      popover: {
        description: '可以对任务进行分组',
      }
    },
    {
      element: '.task-create-input .calendar',
      popover: {
        description: '设置任务时间',
      },
    },
    {
      element: '#ai-summary',
      popover: {
        description: 'AI 总结功能',
        onNextClick() {
          (document.querySelector('.sort-group-button') as any)?.click();
          driverObj.moveNext();
        }
      },
    },
    {
      element: '.sort-group-button',
      popover: {
        description: '单击按钮',
      },
      onHighlighted: () => {
        setTimeout(() => {
          driverObj.moveNext();
        }, 230)
      }
    },
    {
      element: '.sort-group',
      popover: {
        description: '任务显示分组和排序方式',
        onPrevClick: () => {
          setTimeout(() => {
            (document.querySelector('.sort-group-button') as any)?.click();
            setTimeout(() => {
              driverObj.movePrevious();
              driverObj.movePrevious();
            })
          })
        },
        onNextClick: () => {
          (document.querySelector('.sort-group-button') as any)?.click();
          (document.querySelector('.view-more-button') as any)?.click();
          setTimeout(() => {
            driverObj.moveNext();
          })
        }
      },
    },
    {
      element: '.view-more-button',
      popover: {
        description: '单击按钮',
      },
      onHighlighted: () => {
        setTimeout(() => {
          driverObj.moveNext();
        }, 230)
      }
    },
    {
      element: '.view-more',
      popover: {
        description: '可以选择不同任务列表显示方式',
        onPrevClick() {
          (document.querySelector('.sort-group-button') as any)?.click();
          (document.querySelector('.view-more-button') as any)?.click();
          setTimeout(() => {
            driverObj.movePrevious();
            driverObj.movePrevious();
          })
        },
        onNextClick() {
          (document.querySelector('.view-more-button') as any)?.click();
          setTimeout(() => {
            driverObj.moveNext();
          })
        }
      },
    },
    {
      element: '#threeDays',
      popover: {
        description: '前三天和后三天任务列表',
        onPrevClick() {
          (document.querySelector('#toDay') as any)?.click();
          setTimeout(() => {
            (document.querySelector('.view-more-button') as any)?.click();
            setTimeout(() => {
              driverObj.movePrevious();
            })
          })
        },
      },
      onHighlighted() {
        (document.querySelector('#threeDays') as any)?.click();
      }
    },
    {
      element: '#collectBox',
      popover: {
        description: '没有分组的任务列表',
      },
      onHighlighted() {
        (document.querySelector('#collectBox') as any)?.click();
      }
    },
    {
      element: '#finish',
      popover: {
        description: '完成后任务都会显示在这里',
      },
      onHighlighted() {
        (document.querySelector('#finish') as any)?.click();
      }
    },
    {
      element: '#delete',
      onHighlightStarted() {
        (document.querySelector('#delete') as any)?.click();
      },
      popover: {
        description: '删除后的任务都会显示在这里',
      },
    },
    {
      element: '#clean-all-delete-task',
      popover: {
        description: '可以将删除的任务进行清空',
      },
    },
  ]
}
