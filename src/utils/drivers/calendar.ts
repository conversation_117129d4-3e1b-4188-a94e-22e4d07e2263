import type { Driver, DriveStep } from 'driver.js';

export function registerCalendar(driverObj: Driver): DriveStep[] {
  return [
    {
      element: '#calendarView',
      popover: {
        title: '日历',
        description: '可以通过日历方式管理任务',
        side: "right",
        align: 'start',
        onPrevClick() {
          (document.querySelector('#taskList') as any)?.click();
          setTimeout(() => {
            (document.querySelector('#delete') as any)?.click();
            setTimeout(() => {
              driverObj.movePrevious();
            })
          })
        }
      },
      onHighlightStarted() {
        (document.querySelector('#calendarView') as any)?.click();
      }
    },
    {
      element: '.calendar-tool>div:first-child',
      popover: {
        description: '上月/今天/下月',
      },
    },
    {
      element: '.calendar',
      popover: {
        description: '可以通过拖拽创建任务',
        side: "over",
      },
    },
  ];
}
