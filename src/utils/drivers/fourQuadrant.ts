import type { Driver, DriveStep } from 'driver.js';

export function registerFourQuadrant(driverObj: Driver): DriveStep[] {
  return [
    {
      element: '#fourQuadrant',
      popover: {
        title: '四象限',
        description: '根据任务优先级划分四个区域',
        side: "right",
        align: 'start',
        onPrevClick() {
          (document.querySelector('#calendarView') as any)?.click();
        }
      },
      onHighlightStarted() {
        (document.querySelector('#fourQuadrant') as any)?.click();
      }
    },
    {
      element: '.four-quadrant',
      popover: {
        description: '各个优先级象限内任务支持拖拽设置任务优先级',
        side: "over",
      },
    },
  ]
}
