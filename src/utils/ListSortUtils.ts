import type { ITaskItem } from '@xiaou66/todo-plugin'
import { cloneDeep } from 'es-toolkit'

/**
 * 获取指定范围数据
 * @taskId 任务 id
 * @preCount 往前取
 * @nextCount 往后取
 */
type GetRangeData = (taskId: ITaskItem, preCount: number, nextCount: number) => Promise<ITaskItem[]>;


export class ListSortUtils {
  // 定义一些常量
  /**
   * 初始值设为 1 亿
   * @private
   */
  public static readonly INITIAL_VALUE = 10;

  /**
   * 每次增量为 100 万
   * @private
   */
  public static readonly INCREMENT = 10_000_000

  /**
   * 最小间隔值
   * 要触发重建
   */
  public static readonly MIN_INTERVAL_VALUE = 100;

  private static readonly REBALANCE_THRESHOLD = Number.MAX_SAFE_INTEGER / 2

  /**
   * 异步添加一个新项目，并计算其排序值。
   * @param {() => Promise<number | undefined>} getMaxCount 一个返回当前最大排序值的异步函数。如果当前没有项目，该函数可能返回 undefined。
   * @return {Promise<number>} 返回新项目的排序值。如果没有现有项目（即 getMaxCount 返回 undefined），则返回 ListSortUtils.INIMAL_VALUE 加上 ListSortUtils.INCREMENT 的结果；否则，返回 maxCount + ListSortUtils.INCREMENT。
   */
  static async addItem(getMaxCount: () => Promise<number | undefined>): Promise<number> {
    const maxCount = await getMaxCount()
    if (!maxCount) {
      return ListSortUtils.INITIAL_VALUE
    }
    // 将任务按排序值排序
    return maxCount + ListSortUtils.INCREMENT
  }


  isSort(sort: number): boolean {
    return sort > Number.MAX_SAFE_INTEGER / 2 || sort < Number.MIN_SAFE_INTEGER / 2;
  }

  /**
   * 获取最大容量
   * @param size
   */
  getMaxCapacity(size: number): number {
    return Number.MAX_SAFE_INTEGER / size;
  }


  /**
   * 插入指定位置
   * @param beforeTask 前一个任务（排序靠前），如果为null则表示插入到列表开头
   * @param afterTask 后一个任务（排序靠后），如果为null则表示插入到列表末尾
   * @param getRangeData 获取指定范围任务的函数，只在需要重平衡时才会被调用
   * @param total
   * @returns 计算出的排序值
   */
  static async getInsertSort(
    beforeTask: ITaskItem | null,
    afterTask: ITaskItem | null,
    getRangeData?: GetRangeData,
    total?: number,
  ): Promise<[number, ITaskItem[] | undefined]> {
    beforeTask = cloneDeep(beforeTask);
    afterTask = cloneDeep(afterTask);

    if (!beforeTask && !afterTask) {
      return [ListSortUtils.INITIAL_VALUE, undefined];
    }

    if (!beforeTask && afterTask) {
      // 往前插
      return [afterTask.sort + ListSortUtils.INCREMENT, undefined];
    }
    if (beforeTask && !afterTask) {
      // 往后插
      return [beforeTask.sort - ListSortUtils.INCREMENT, undefined];
    }
    if (afterTask && beforeTask) {
      // 确保计算的新值在两个值之间
      const newSort = Math.round(Math.min(beforeTask.sort, afterTask.sort) +
        Math.abs(beforeTask.sort - afterTask.sort) / 2);

      const afterDiff = Math.abs(newSort - afterTask.sort);
      const beforeDiff = Math.abs(beforeTask.sort - newSort);
      if (afterTask.sort < newSort
        && afterDiff >= ListSortUtils.MIN_INTERVAL_VALUE
        && beforeDiff >= ListSortUtils.MIN_INTERVAL_VALUE) {
        return [newSort, undefined];
      }
      let preCount = 2;
      let tryCount = 0;
      do {
        if (tryCount >= 50) {
          throw Error("需要手动重新整理数据了");
        }

        const taskItems = cloneDeep(await getRangeData!(beforeTask, preCount, preCount));
        const totalInterval = taskItems[0].sort - taskItems[taskItems.length - 1].sort;
        const interval = total! === taskItems.length ? this.INCREMENT : Math.round(totalInterval / (taskItems.length + 1))
        if (interval < this.MIN_INTERVAL_VALUE) {
          // 目前还不行已经不行了
          tryCount++;
          preCount += 2;
          continue;
        }
        const minSort = taskItems[taskItems.length - 1].sort;
        let newSort = -1;
        let setCurrentStatus = -1;
        for (let i = taskItems.length; i >= 0; i--) {
          if (setCurrentStatus === 0) {
            newSort = minSort + interval * (taskItems.length - i - 1);
            setCurrentStatus = 1;
            continue;
          }
          const cur = taskItems[setCurrentStatus === -1 ? i - 1 : i];
          cur.sort = minSort + interval * (taskItems.length - i - 1);

          if (beforeTask.taskId === cur.taskId) {
            beforeTask.sort = cur.sort;
            if (setCurrentStatus != 1) {
              setCurrentStatus = 0;
            }
          }

          if (afterTask.taskId === cur.taskId) {
            afterTask.sort = cur.sort;
            if (setCurrentStatus != 1) {
              setCurrentStatus = 0;
            }
          }
        }
        return [newSort, taskItems];
      } while (true)
    }

    return [0, undefined];
  }


  /**
   * 重建 sort
   * @param taskList
   */
  static rebuildSort(taskList: ITaskItem[]) {
    for (let i =  taskList.length - 1; i >= 0; i--) {
      taskList[i].sort = (taskList.length - i - 1) * this.INCREMENT;
    }
  }
}
