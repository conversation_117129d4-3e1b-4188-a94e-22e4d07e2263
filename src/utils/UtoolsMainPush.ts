import { ExtensionManager } from '@/extension';
import { isString } from 'es-toolkit';
import { useAiTaskDrawer } from '@/components/ai';
import { useTaskDetailDrawer, useUtoolsSearchContext } from '@/components/Tasks';
import { Message } from '@arco-design/web-vue';
interface MatchFile {
  isFile: boolean;
  isDirectory: boolean;
  name: string;
  path: string;
}
interface MatchWindow {
  id: number;
  class: string;
  title: string;
  x: number;
  y: number;
  width: number;
  height: number;
  appPath: string;
  pid: number;
  app: string;
}

interface MainPushAction {
  code: string;
  type: "text" | "img" | "file" | "regex" | "over" | "window";
  payload: string | MatchFile[] | MatchWindow;
}

async function callback({ code, type, payload }: MainPushAction) {
  if (isString(payload)) {
    const result = await ExtensionManager.getTaskInstance().listTask({
      keyword: payload,
    });
    const taskList = result.list;
    const taskItems = taskList.slice(0, 5);
     const list = taskItems.map(taskItem => {
      return {
        title: `${taskItem.taskGroupId}/${taskItem.taskId}`,
        text: taskItem.taskTitle,
      }
    })

    if (taskList.length > 6) {
      list.push({
        text: `更多 ${taskList.length - 5} 条记录, 选择此项进入插件搜索`,
        title: 'more'
      })
    }
    return list;
  }
  return [];
}

function selectCallback({ code, type, payload, option}: {code: string, type: string, payload: string, option: { title: string }}) {
  console.log(payload);
  if (option.title === 'more') {
    useUtoolsSearchContext().setSubInput(payload);
  } else {
    const [groupId, taskId] = option.title.split("/");
    ExtensionManager.getTaskInstance().getTask(groupId, taskId)
      .then((taskItem) => {
        if (taskItem) {
          useTaskDetailDrawer().show(taskItem)
          return;
        }
        utools.showNotification('任务打开失败');
      })
  }
  return true
}

utools.onMainPush(callback, selectCallback);
