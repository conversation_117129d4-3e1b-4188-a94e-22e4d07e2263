import { expect, test } from 'vitest'
import { ListSortUtils } from '@/utils/ListSortUtils.ts'
import type { TaskStatus, TaskItem } from '@xiaou66/todo-plugin'
test('首次任务添加', async () => {
  const result = await ListSortUtils.addItem(async () => {
    return undefined
  })
  expect(result).toBe(ListSortUtils.INITIAL_VALUE)
})

test('添加任务', async () => {
  const result = await ListSortUtils.addItem(async () => {
    return ListSortUtils.INITIAL_VALUE
  })
  expect(result).toBe(ListSortUtils.INITIAL_VALUE + ListSortUtils.INITIAL_VALUE)
})

test(
  '测试批量添加任务',
  async () => {
    let maxCount = ListSortUtils.INITIAL_VALUE
    const count = 100_000_000
    for (let i = 0; i < count; i++) {
      const result = await ListSortUtils.addItem(async () => {
        return maxCount
      })
      maxCount = result
    }
    expect(maxCount).toBe(ListSortUtils.INITIAL_VALUE + ListSortUtils.INITIAL_VALUE * count)
  },
  {
    timeout: 100000000000,
  },
)

test('不进行局部重排次数', async () => {
  let maxCount = ListSortUtils.INITIAL_VALUE
  let count = 0
  while (maxCount > ListSortUtils.MIN_INTERVAL_VALUE) {
    maxCount /= 2
    count += 1
  }
  console.log('不进行局部重排次数: ', count)
})

test('中间插入任务', async () => {
  const list: TaskItem[] = [
    {
      taskId: '1000',
      sort: 2_000_000_000,
    },
    {
      taskId: '2000',
      sort: 1_000_000_000,
    },
  ] as TaskItem[]
  const number = await ListSortUtils.getInsertSort(list[0], list[1], async () => list, list.length)

  expect(number[0]).toBe(list[0].sort - (list[0].sort - list[1].sort) / 2)
})

test('触发全部重排', async () => {
  let list: TaskItem[] = [
    {
      taskId: '8',
      sort: 1_000_106_000,
    },
    {
      taskId: '7',
      sort: 1_000_105_000,
    },
    {
      taskId: '6',
      sort: 1_000_104_000,
    },
    {
      taskId: '5',
      sort: 1_000_103_000,
    },
    {
      taskId: '4',
      sort: 1_000_102_000,
    },
    {
      taskId: '3',
      sort: 1_000_101_000,
    },
    {
      taskId: '2',
      sort: 1_000_100_000,
    },
    {
      taskId: '1',
      sort: 1_000_000_000,
    },
  ] as TaskItem[]
  const cursor = list[4]
  const [sort, resList] = await ListSortUtils.getInsertSort(
    cursor,
    list[5],
    async (item, pre, next) => {
      const index = list.findIndex((item) => item.taskId === cursor.taskId)
      return list.slice(Math.max(index - pre, 0), Math.min(index + next + 1, list.length))
    },
    list.length,
  )
  list.push({
    taskId: 'new',
    sort,
  } as TaskItem)

  if (resList) {
    const resSortList = resList.sort((a, b) => b.sort - a.sort)
    console.log('resSortList', resSortList)
    const index = list.findIndex((item) => item.taskId === resList[0].taskId)
    for (let i = 0; i < resSortList.length; i++) {
      list[index + i].sort = resSortList[i].sort
    }
  }
  list = list.sort((a, b) => b.sort - a.sort)
  console.log(list)
})

test('排到最后面', async () => {
  let list: TaskItem[] = [
    {
      taskId: '8',
      sort: 1_000_106_000,
    },
    {
      taskId: '7',
      sort: 1_000_105_000,
    },
    {
      taskId: '6',
      sort: 1_000_104_000,
    },
    {
      taskId: '5',
      sort: 1_000_103_000,
    },
    {
      taskId: '4',
      sort: 1_000_102_000,
    },
    {
      taskId: '3',
      sort: 1_000_101_000,
    },
    {
      taskId: '2',
      sort: 1_000_100_000,
    },
    {
      taskId: '1',
      sort: 1_000_000_000,
    },
  ] as TaskItem[]
  const [sort, resList] = await ListSortUtils.getInsertSort(
    list[7],
    null,
    async (item, pre, next) => {
      const index = list.findIndex((item) => item.taskId === list[7].taskId)
      return list.slice(Math.max(index - pre, 0), Math.min(index + next + 1, list.length))
    },
    list.length,
  )
  list.push({
    taskId: 'new',
    sort,
  } as TaskItem)

  if (resList) {
    const resSortList = resList.sort((a, b) => b.sort - a.sort)
    console.log('resSortList', resSortList)
    const index = list.findIndex((item) => item.taskId === resList[0].taskId)
    for (let i = 0; i < resSortList.length; i++) {
      list[index + i].sort = resSortList[i].sort
    }
  }
  list = list.sort((a, b) => b.sort - a.sort)
  console.log(list)
})

test('排到最后面2', async () => {
  let list: TaskItem[] = [
    {
      taskId: '8',
      sort: 1_000_106_000,
    },
    {
      taskId: '7',
      sort: 1_000_105_000,
    },
    {
      taskId: '6',
      sort: 1_000_104_000,
    },
    {
      taskId: '5',
      sort: 1_000_103_000,
    },
    {
      taskId: '4',
      sort: 1_000_102_000,
    },
    {
      taskId: '3',
      sort: 1_000_101_000,
    },
    {
      taskId: '2',
      sort: 1_000_100_000,
    },
    {
      taskId: '1',
      sort: 1000000000,
    },
    {
      taskId: '0',
      sort: 0,
    },
    {
      taskId: '-1',
      sort: -1000000000,
    },
    {
      taskId: '-2',
      sort: -1000001000,
    },
  ] as TaskItem[]
  const [sort, resList] = await ListSortUtils.getInsertSort(
    list[list.length - 2],
    list[list.length - 1],
    async (item, pre, next) => {
      const index = list.findIndex((item) => item.taskId === list[list.length - 2].taskId)
      return list.slice(Math.max(index - pre, 0), Math.min(index + next + 1, list.length))
    },
    list.length,
  )
  list.push({
    taskId: 'new',
    sort,
  } as TaskItem)

  if (resList) {
    const resSortList = resList.sort((a, b) => b.sort - a.sort)
    console.log('resSortList', resSortList)
    const index = list.findIndex((item) => item.taskId === resList[0].taskId)
    for (let i = 0; i < resSortList.length; i++) {
      list[index + i].sort = resSortList[i].sort
    }
  }
  list = list.sort((a, b) => b.sort - a.sort)
  console.log(list)
})
