import "tdesign-vue-next/es/style/index.css";
import './assets/less/main.less';
import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import { useTheme } from '@/hooks/useTheme.ts'
import { dispatchUtoolsEvent } from '@/events'
import 'dayjs/locale/zh-cn'
import dayjs from 'dayjs'
import '@arco-design/web-vue/lib/message/style/index.css';
import '@arco-design/web-vue/lib/notification/style/index.css';
import { ExtensionManager } from '@/extension/ExtensionManager.ts'
import utoolsImage from '@/directives/utoolsImage'
import 'virtual:svg-icons-register';
import 'tippy.js/animations/scale.css';
import { initUmami, umami } from '@/utils/umami';
import 'driver.js/dist/driver.css';
import { useTaskSetting } from '@/views/Setting/TaskSetting.ts';
import { useRuntimeContent } from '@/views/runtime.ts';
import '@/utils/UtoolsMainPush.ts';
import type { UtoolsTaskExtension } from '@/extension/utools/UtoolsTasksExtension.ts';

(() => {
  import('virtual:uno.css')
})()

ExtensionManager.init();
dayjs.locale('zh-cn') // 设置为中文

initUmami({
  baseUrl: import.meta.env.VITE_UMAMI_BASE_URL,
  websiteId: import.meta.env.VITE_UMAMI_WEBSITE_ID,
});
ExtensionManager.getTaskInstance().rebuildSort();
// 主题
useTheme({
  setDarkTheme: () => {
    // document.body.setAttribute('arco-theme', 'dark')
  },
  setDefaultTheme: () => {
    // document.body.removeAttribute('arco-theme');
  }
});


const app = createApp(App)

app.use(createPinia())
app.use(router)

app.use(utoolsImage);

app.mount('#app')

umami().identifyUser();

utools.onPluginDetach(() => {
  const runtimeContent = useRuntimeContent();
  runtimeContent.pluginDetach.value = true;
  document.body.setAttribute('plugin-detach', '1');
});

if (utools) {
  (ExtensionManager.getTaskInstance() as UtoolsTaskExtension).cleanErrorTask();
}
utools.onPluginEnter((data) => {
  const { pluginHeight } = useTaskSetting();
  setTimeout(() => {
    utools.setExpendHeight(pluginHeight.value || 700);
  }, 100);
  // 将 utools 事件派发根据不同的 code 进行派发出去
  dispatchUtoolsEvent(data, router);
});
