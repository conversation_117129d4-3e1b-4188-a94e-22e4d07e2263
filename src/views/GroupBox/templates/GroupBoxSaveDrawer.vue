<script setup lang="ts">
import { nextTick, ref, toRaw, useTemplateRef } from 'vue'
import type { GroupBoxSaveDrawerInstance } from './GroupBoxSaveDrawerInstance.ts'
import  { FormInstanceFunctions } from 'tdesign-vue-next';
import type { IGroupCreateParams, IGroupInfo } from '@xiaou66/todo-plugin'
import { cloneDeep, merge } from 'es-toolkit';
import { ExtensionManager } from '@/extension'

const visible = ref(false);


const defaultValue: IGroupCreateParams | IGroupInfo = {
  groupName: '',
  code: 'utools',
  utoolsKey: false,
};
const form = ref<IGroupCreateParams | IGroupInfo>(cloneDeep(defaultValue) as IGroupCreateParams)
const formRef = useTemplateRef<FormInstanceFunctions>('formRef');
const inputRef = useTemplateRef('inputRef');
// function handleUploadImage() {
//   const paths = utools.showOpenDialog({
//     title: '选择封面文件',
//     filters: [{ 'name': 'image', extensions: ['png', 'jpeg', 'jpg'] }]
//   });
//   if (!paths || paths.length === 0) {
//     return;
//   }
//
//   const path = paths[0];
//   const stats = window.fs.statSync(path);
//   if (stats.size >= 1024 * 1024 * 2) {
//     Notification.warning({
//       title: '图片限制温馨提示',
//       content: '为了加载速度请选择图片不能超过 2MB'
//     });
//     return;
//   }
//   imageCropperModalInstance.value?.show('file://' + path);
// }



const emits = defineEmits<{
  saveAfter: [groupId: string];
}>();

async function handleSaveGroup(close?: boolean) {
  const res = await formRef.value.validate();
  if (res !== true) {
    return false;
  }

  const instance = ExtensionManager.getGroupInstance();
  let groupId = (form.value as IGroupInfo).groupId;
  if (groupId) {
    console.log(form.value);
    await instance.saveGroup(toRaw(form.value) as IGroupInfo)
  } else {
    groupId = await instance.createGroup(toRaw(form.value));
  }
  console.log('groupId', groupId);
  emits('saveAfter', groupId);
  if (close) {
    visible.value = false;
  }
  return true;
}

function show(groupInfo?: IGroupInfo) {
  form.value = merge(cloneDeep(defaultValue), groupInfo || {}) as IGroupInfo;
  console.log('show', form.value);
  visible.value = true
  nextTick(() => {
    inputRef.value.focus()
  })
}
defineExpose<GroupBoxSaveDrawerInstance>({
  show,
});

// const imageCropperModalInstance = useTemplateRef<ImageCropperInstance>('imageCropperModalRef');
</script>

<template>
<!--  <ImageCropper ref="imageCropperModalRef" @saveOk="handleSaveImage" />-->
  <t-dialog header="保存分组"
            v-model:visible="visible"
            :width="500"
            :top="100"
            destroyOnClose
            confirm-btn="保存"
            @confirm="handleSaveGroup">
    <t-form ref="formRef" :data="form">
      <t-form-item label="分组名称"
                   name="groupName"
                   :rules="[{ required: true, message: '请输入分组名称' }]">
        <t-input v-model:value="form.groupName"
                 ref="inputRef"
                 :maxlength="10" />
      </t-form-item>
      <t-form-item style="display: none"
                   :status-icon="false" />
    </t-form>
  </t-dialog>
</template>
