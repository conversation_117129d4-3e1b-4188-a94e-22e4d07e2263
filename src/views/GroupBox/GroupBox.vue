<script setup lang="ts">
import { onMounted, ref, useTemplateRef } from 'vue'
import GroupBoxSaveDrawer from '@/views/GroupBox/templates/GroupBoxSaveDrawer.vue'
import type { GroupBoxSaveDrawerInstance } from '@/views/GroupBox/templates/GroupBoxSaveDrawerInstance.ts'
import { GroupBoxCard } from '@/components/card'
import type { IGroupListResult } from '@xiaou66/todo-plugin'
import { ExtensionManager } from '@/extension'

const activeCode = ref<string>('utools');

const groupBoxSaveDrawerRef = useTemplateRef<GroupBoxSaveDrawerInstance>('groupBoxSaveDrawerRef');
const groupData = ref<IGroupListResult>({
  total: 0,
  list: []
});


async function request(page: number = 1) {
  const groupInstance = ExtensionManager.getGroupInstance();
  groupData.value = await groupInstance.listGroup({});
}

onMounted( () => {
  request().then( (res) => {});
});
</script>

<template>
  <GroupBoxSaveDrawer ref="groupBoxSaveDrawerRef"
                      @saveAfter="request" />
  <div class="u-main-content u-pos-rel">
    <div class="u-fx group-top-action">
      <a-button-group shape="round" size="mini">
        <a-button>
          <template #icon>
            <iconpark-icon name="config"></iconpark-icon>
          </template>
        </a-button>
        <a-radio-group v-model:model-value="activeCode"
                       type="button"
                       size="mini">
          <a-radio value="utools">uTools</a-radio>
        </a-radio-group>
        <a-button @click="groupBoxSaveDrawerRef?.show()">
          <template #icon>
            <iconpark-icon name="plus" style="padding-top: 6px"></iconpark-icon>
          </template>
          添加分组
        </a-button>
      </a-button-group>
    </div>
    <a-scrollbar style="height: 100%; overflow: auto">
      <div class="u-fx u-gap10 group-box">
        <GroupBoxCard v-for="groupInfo in groupData.list"
                      :key="groupInfo.groupId"
                      :group-info="groupInfo" />
      </div>
    </a-scrollbar>
<!--   <div class="u-fx u-gap10">
     <a-table :data="data" style="width: 100%;" :bordered="false" :pagination="false">
       <template #columns>
         <a-table-column title="分组名称"
                         data-index="groupName" :width="200" />
         <a-table-column title="进度" :width="200">
           <template #cell>
             <div class="dot-group">
               <div class="u-fx u-fac">
                 <div class="dot" style="background:#000;">
                 </div>
                 <div>
                   总数: 10
                 </div>
               </div>
               <div class="u-fx u-fac">
                 <div class="dot" style="background: #73d13d;">
                 </div>
                 <div>
                   完成: 100
                 </div>
               </div>
               <div class="u-fx u-fac">
                 <div class="dot" style="background: #ff4d4f;">
                 </div>
                 <div>
                   逾期: 100
                 </div>
               </div>
               <div class="u-fx u-fac">
                 <div class="dot" style="background: #40a9ff;">
                 </div>
                 <div>
                   未做: 100
                 </div>
               </div>
             </div>
           </template>
         </a-table-column>
         <a-table-column title="操作" :width="100">
           <template #cell>
             <a-link>编辑</a-link>
             <a-link status="danger">删除</a-link>
           </template>
         </a-table-column>
       </template>
     </a-table>
   </div>-->
  </div>
</template>

<style scoped lang="less">
.dot-group {
  display: grid;
  grid-template-columns: 50% 50%;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 5px;
  > div {
    width: 100px;
  }
  .dot {
    width: 5px;
    height: 5px;
    background:#000;
    border-radius: 50%;
    margin-right: 4px;
  }
}
.group-box {
  height: calc(100vh - 110px);
  flex-wrap: wrap;
}
.group-top-action {
  justify-content: flex-end;
  padding-bottom: 10px;
  padding-right: 10px;
}
</style>
