<script setup lang="ts">
import { onMounted, provide, ref, useTemplateRef } from 'vue'
import { useRouter } from 'vue-router'
import type { TaskSaveParams, IGroupInfo, ITaskItem } from '@xiaou66/todo-plugin'
import {
  GroupDetailType,
  type TaskViewConfig,
  type ITaskViewProvide,
} from '@/views/GroupDetail/GroupDetail.ts'
import {
  TaskDetailDrawer,
  type TaskDetailDrawerInstance,
} from '@/components/Tasks/TaskDetailDrawer'
import type { TaskListItem } from '@xiaou66/todo-plugin'
import { TaskViewLayer } from '@/views/GroupDetail/templates/TaskViewLayer'
import TaskSortDropdown from '@/components/Tasks/TaskSortDropdown/TaskSortDropdown.vue'
import { ExtensionManager } from '@/extension'
import { ListSortUtils } from '@/utils/ListSortUtils.ts'

const props = defineProps<{
  groupId: string
  code: string
}>()

const groupInfo = ref<IGroupInfo>()
onMounted(async () => {
  groupInfo.value = await ExtensionManager.getGroupInstance().getGroup(props.groupId)
})
const viewMode = ref<'list' | 'grid'>('list')
const router = useRouter()
function handleBack() {
  router.back()
}
// const moveCard = (dragIndex: number, hoverIndex: number) => {
//   const item = cards.value[dragIndex]
//   cards.value.splice(dragIndex, 1)
//   cards.value.splice(hoverIndex, 0, item)
// }
//
//
// const moveTask = (dragIndex: number, hoverIndex: number) => {
//   const item = cards.value[dragIndex]
//   cards.value.splice(dragIndex, 1)
//   cards.value.splice(hoverIndex, 0, item)
// }
async function handleSaveTask(task: TaskSaveParams) {
  const taskInstance = ExtensionManager.getTaskInstance();
  await taskInstance.saveTask(props.groupId, task)
  refreshData().then(() => {}).catch(e => {
    console.log(e);
  })
}

const viewConfig = ref<TaskViewConfig>({
  groupType: 'taskLevel',
  sortType: 'custom',
})


const data = ref<TaskListItem[]>([]);

provide<ITaskViewProvide>(GroupDetailType.TaskViewProvide, {
  saveTask: handleSaveTask,
  // 开始拖拽时的处理, 需要复制拖拽的副本
  dragStart(moveTask) {
  },
  async dragEnd(sourceTask: TaskListItem,
                targetTask: TaskListItem,
                newTask: Partial<TaskListItem> = {},
                type: 'top' | 'bottom') {
    const taskList: TaskListItem[] = data.value;
    const sourceTaskIndex = taskList.findIndex(item => item.taskId === sourceTask.taskId);
    const targetTaskIndex = taskList.findIndex(item => item.taskId === targetTask.taskId);
    console.log('dragEnd', sourceTaskIndex, targetTaskIndex,data.value.length)
    const taskInstance = ExtensionManager.getTaskInstance();
    if (Math.abs(sourceTaskIndex - targetTaskIndex) === 1) {
      // 邻居互换直接互换 sort 即可
      const temp = taskList[sourceTaskIndex].sort;
      taskList[sourceTaskIndex].sort = taskList[targetTaskIndex].sort;
      taskList[targetTaskIndex].sort = temp;
      await taskInstance.saveTask(props.groupId, {
        ...taskList[sourceTaskIndex],
        ...newTask
      });
      await taskInstance.saveTask(props.groupId, {
        ...taskList[sourceTaskIndex - (sourceTaskIndex - targetTaskIndex)],
      });
    } else {
      debugger
      // 不是邻居了
      console.log(targetTaskIndex, type);
      console.log('targetTaskIndex === data.value.length - 1 && type === \'bottom\'', targetTaskIndex === data.value.length - 1 && type === 'bottom')
      if (targetTaskIndex === 1 && type === 'top') {
        // 插入头部
        const [sort] = await ListSortUtils.getInsertSort(null, taskList[targetTaskIndex]);
        await taskInstance.saveTask(props.groupId, {
          ...taskList[sourceTaskIndex],
          ...newTask,
          sort
        });
      } else if (targetTaskIndex === data.value.length - 1 && type === 'bottom') {
        // 插入尾部
        const [sort] = await ListSortUtils.getInsertSort(taskList[targetTaskIndex], null);
        await taskInstance.saveTask(props.groupId, {
          ...taskList[sourceTaskIndex],
          ...newTask,
          sort
        });
      } else {
        // 中间
        const preIndex = type === 'top' ? targetTaskIndex - 1 : targetTaskIndex;
        const [sort, taskItemList] = await ListSortUtils.getInsertSort(taskList[preIndex], taskList[preIndex + 1],
          async (taskItem: ITaskItem, pre: number, next: number) => {
            const taskInstance = ExtensionManager.getTaskInstance();
            const pageResult = await taskInstance.listTask({});
            const list = pageResult.list;
            const index = list.findIndex((task) => task.taskId === taskItem.taskId)
            return pageResult.list.slice(Math.max(index - pre, 0), Math.min(index + next + 1, list.length));
          });

        if (taskItemList) {
          for (const taskItem of taskItemList) {
            await taskInstance.saveTask(props.groupId, {
              ...taskItem
            });
          }
        }

        await taskInstance.saveTask(props.groupId, {
          ...taskList[sourceTaskIndex],
          ...newTask,
          sort
        });
      }
    }
    refreshData();
  }
})



async function refreshData() {
  const taskInstance = ExtensionManager.getTaskInstance();
  console.log('refreshData-group')
  taskInstance
    .listTask({
      groupId: props.groupId,
    })
    .then((res) => {
      data.value = res.list
    })
}

onMounted(() => {
  refreshData()
})
</script>

<template>
  <div v-if="groupInfo" style="height: 100%">
    <a-page-header
      :title="groupInfo.groupName"
      @back="handleBack"
    >
      <template #extra>
        <div>
          <TaskSortDropdown
            v-model:group-type="viewConfig.groupType"
            v-model:sort-type="viewConfig.sortType"
          />
          <a-dropdown position="tr">
            <a-button size="small">
              <template #icon>
                <iconpark-icon size="14" name="more"></iconpark-icon>
              </template>
            </a-button>
            <template #content>
              <div class="min-dropdown-select-options">
                <div>
                  <div class="u-tips" style="padding-bottom: 4px">视图</div>
                  <a-radio-group v-model:model-value="viewMode" size="mini" type="button">
                    <a-tooltip mini>
                      <template #content>看板模式</template>
                      <a-radio value="grid">
                        <iconpark-icon
                          name="column"
                          size="14"
                          style="padding-top: 4px"
                        ></iconpark-icon>
                      </a-radio>
                    </a-tooltip>
                    <a-tooltip mini>
                      <template #content>列表模式</template>
                      <a-radio value="list">
                        <iconpark-icon
                          name="mindmap-list"
                          size="14"
                          style="padding-top: 4px"
                        ></iconpark-icon>
                      </a-radio>
                    </a-tooltip>
                  </a-radio-group>
                </div>
              </div>
            </template>
          </a-dropdown>
        </div>
      </template>
      <div class="group-detail">
        <TaskViewLayer :viewMode="viewMode" :viewConfig="viewConfig" :data="data" />
      </div>
    </a-page-header>
  </div>
</template>

<style scoped lang="less">
:deep(.arco-page-header) {
  box-sizing: border-box;
  padding: 9px 0;
}
:deep(.arco-page-header-content) {
  box-sizing: border-box;
  padding: 0;
}
:deep(.arco-page-header-with-content .arco-page-header-wrapper) {
  padding-bottom: 9px;
}

.group-info {
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-second-color);
  margin-bottom: 4px;
  .title {
    color: var(--text-color);
    font-size: 14px;
    font-weight: 600;
  }
}
:deep(.arco-btn-secondary, .arco-btn-secondary[type='button'], .arco-btn-secondary[type='submit']) {
  background-color: transparent;
}
:deep(.arco-btn-group .arco-btn-secondary:not(:last-child)) {
  border-right: none;
}
</style>
