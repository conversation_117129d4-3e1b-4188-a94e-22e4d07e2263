import { inject, type Ref } from 'vue'
import type { TaskDetailDrawerInstance } from '@/components/Tasks/TaskDetailDrawer'
import type { TaskSaveParams, TaskListItem } from '@xiaou66/todo-plugin'

export interface ITaskViewProvide {
  /**
   * 添加任务
   * @param task 任务
   */
  saveTask(task: TaskSaveParams): void;
  dragStart(moveTask: TaskListItem): void;
  dragEnd(sourceTask: TaskListItem, targetTask: TaskListItem, newTask: Partial<TaskListItem> | undefined, type: 'top' | 'bottom'): void;
}


export enum GroupDetailType {
  TaskViewProvide = "TaskViewProvide",
}


export interface TaskViewConfig {
  groupType: "taskLevel" | "taskStartDate";
  sortType: 'custom';
}



export function useTaskViewContainer(): ITaskViewProvide  {
  return inject<ITaskViewProvide>(GroupDetailType.TaskViewProvide)!;
}
