<script setup lang="ts">
import TaskItem from '@/components/Tasks/TaskItem/TaskItem.vue'
import type { ViewData } from '../index.ts';
import { nextTick, ref, useTemplateRef, onMounted, onBeforeUnmount } from 'vue';
import type { TaskListItem,  TaskSaveParams } from '@xiaou66/todo-plugin'
import { useTaskDetailDrawer, useTaskViewProvide } from '@/components/Tasks';
import TaskCreateInput from '@/components/Tasks/TaskCreateInput/TaskCreateInput.vue'
import type { TaskCreateInputInstance } from '@/components/Tasks/TaskCreateInput/TaskCreateInput.ts'
import {combine} from "@atlaskit/pragmatic-drag-and-drop/combine";
import {
  dropTargetForElements,
} from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import {useTaskDragContext} from "@/components/Tasks/TaskItem/TaskDragWrapper.ts";
import {getGroupItemData} from "@/components/Tasks/TaskItem/TaskGroup.ts";
const props = defineProps<{
  viewData: ViewData;
}>();

const currentAddTypeId = ref('');
const addTaskInputRef = useTemplateRef<TaskCreateInputInstance>('addTaskInputRef');

function setCurrentAddTypeId(id: string) {
  if (currentAddTypeId.value === id) {
    return;
  }
  currentAddTypeId.value = id;
  nextTick(() => {
    if (addTaskInputRef.value) {
      addTaskInputRef.value.focus();
      const viewData =  props.viewData;
      if (props.viewData.groupField === 'taskLevel') {
        addTaskInputRef.value.setData({
          [viewData.groupField]: viewData.groupFieldValue
        });
      }
    }
  });
}

const taskViewProvide = useTaskViewProvide();

function handleAddTask(createTask: TaskSaveParams) {
  taskViewProvide.saveTask(createTask);
  addTaskInputRef.value?.setData({
    taskLevel: createTask.taskLevel,
  })
}


const taskDetailDrawerInstance = useTaskDetailDrawer();
// 处理任务点击事件
function handleTaskClick(item: TaskListItem) {
  taskDetailDrawerInstance.show(item);
}

let cleanup = () => { };
const [taskDragContext, setTaskDragContext] = useTaskDragContext();
const groupContainerRef = useTemplateRef<HTMLDivElement>('groupContainerRef');
const dragHover = ref<boolean>(false);
onMounted(() => {
  if (!groupContainerRef.value) {
    return;
  }
  cleanup = combine(
    dropTargetForElements({
      element: groupContainerRef.value,
      getData(args) {
        return getGroupItemData({});
      },
      onDragEnter() {
        const viewData = props.viewData;
        if (viewData.groupField === 'finish') {
          setTaskDragContext({
            newTask: {
              finish: true,
            }
          });
        } else {
          setTaskDragContext({
            newTask: {
              [viewData.groupField]: viewData.groupFieldValue,
              finish: false,
            }
          });
        }
        dragHover.value = true;
      },
      onDragLeave() {
        dragHover.value = false;
      },
      onDrop() {
        dragHover.value = false;
      }
    })
  )
});
onBeforeUnmount(() => {
  cleanup();
})


</script>

<template>
  <div ref="groupContainerRef"
       class="group-container">
    <div class="u-fx u-fac group-info">
      <div class="u-fx u-fac u-gap5" style="padding-left: 4px;">
        <div class="title" :style="{ color: viewData.color }">
          {{viewData.label}}
        </div>
        <div>({{viewData.taskList.length}})</div>
      </div>
      <div>
        <a-button-group size="mini">
          <a-button @click="setCurrentAddTypeId(viewData.groupFieldValue)">
            <template #icon>
              <iconpark-icon name="plus"></iconpark-icon>
            </template>
          </a-button>
<!--          <a-dropdown class="min-select-options">-->
<!--            <a-button>-->
<!--              <template #icon>-->
<!--                <iconpark-icon name="more"></iconpark-icon>-->
<!--              </template>-->
<!--            </a-button>-->
<!--            <template #content>-->
<!--              <a-doption>-->
<!--                <template #icon>-->
<!--                  <iconpark-icon name="write"></iconpark-icon>-->
<!--                </template>-->
<!--                编辑-->
<!--              </a-doption>-->
<!--            </template>-->
<!--          </a-dropdown>-->
        </a-button-group>
      </div>
    </div>
    <div ref="scrollbarRef"
         class="scrollbar"
         :class="{ 'drag-hover': dragHover }">
      <TaskCreateInput v-if="currentAddTypeId === viewData.groupFieldValue"
                       ref="addTaskInputRef"
                       size="small"
                       :disable-module="['groupSelect', 'calendar']"
                       blur-save
                       @create="handleAddTask"
                       @blur="setCurrentAddTypeId('')" />
      <div v-if="viewData.taskList.length">
        <TaskItem
          v-for="(task) in viewData.taskList"
          :key="task.taskId"
          :taskItem="task"
          show-type="card"
          @click="handleTaskClick"
          @save="taskViewProvide.saveTask"
          @dragStart="taskViewProvide.dragStart"
          @dragEnd="taskViewProvide.dragEnd"
          :disable-drag="!!viewData.disableTaskDrag"
        />
      </div>
      <div v-else>
        <a-empty>
          无任务
        </a-empty>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.group-container {
  min-width: 235px;
  width: 235px;
  .scrollbar {
    transition: border 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 4px;
    height: calc(100vh - 142px);
    overflow-y: auto;
    border-radius: var(--border-radius-small);
    &::-webkit-scrollbar {
      display: none;
    }
    &.drag-hover {
      border: 2px solid rgb(var(--arcoblue-5));
    }
  }
}
.group-info {
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-second-color);
  margin-bottom: 4px;
  .title {
    color: var(--text-color);
    font-size: 14px;
    font-weight: 600;
  }
}
</style>
