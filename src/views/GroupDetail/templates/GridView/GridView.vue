<script setup lang="ts">
import { CardItem } from '../index.ts';
import type { ViewData } from '../index.ts';

defineProps<{
  viewDataList: ViewData[];
  finishView: ViewData;
}>();
</script>

<template>
    <a-scrollbar class="scrollbar-width">
      <div class="group-view-grid">
        <card-item v-for="(viewData, index) in viewDataList"
                   :key="index"
                   :viewData="viewData" />
        <card-item v-if="finishView.taskList && finishView.taskList.length"
                   :view-data="finishView"></card-item>
      </div>
    </a-scrollbar>
</template>

<style scoped lang="less">
:deep(.scrollbar-width) {
  max-width: 100%;
  overflow-x: auto;
}
.group-view-grid {
  display: flex;
  gap: 16px;
  .group-info {
    padding: 0 6px;
  }
}
</style>
