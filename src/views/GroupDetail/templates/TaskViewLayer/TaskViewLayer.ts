import type { ViewData } from '@/views/GroupDetail/templates/TaskViewLayer/index.ts';
import TaskConstants from '@/constants/tasks/TaskConstants.ts';
import type { TaskListItem, ViewGroupType } from '@xiaou66/todo-plugin';
import { groupBy, keyBy } from 'es-toolkit';
import { toRaw, markRaw } from 'vue';
import { ExtensionManager } from '@/extension';
import dayjs from 'dayjs';
import { ExpiredRightAction } from './custom/ExpiredRightAction.tsx';

/**
 * 根据任务列表生成视图数据(优先级)
 *
 * @param taskList 任务列表，类型为 TaskListItem 数组
 * @returns 返回类型为 ViewData 数组的 Promise 对象
 */
async function taskLevelBuilder(taskList: TaskListItem[]): Promise<ViewData[]> {
  const groupMap = groupBy(toRaw(taskList), (item) => item.taskLevel);
  console.log('groupMap', groupMap);
  return TaskConstants.PRIORITY_SELECT_OPTIONS_TAG.map((item) => {
    return {
      label: item.label as string,
      color: item.color,
      groupFieldValue: item.value as string,
      groupField: 'taskLevel',
      taskList: groupMap[Number(item.value)] || [],
    };
  });
}

/**
 * 根据任务列表生成视图数据(分组)
 *
 * @param taskList 任务列表，类型为 TaskListItem 数组
 * @returns 返回类型为 ViewData 数组的 Promise 对象
 */
async function taskGroupIdBuilder(taskList: TaskListItem[]): Promise<ViewData[]> {
  const groupMap = groupBy(toRaw(taskList), (item) => item.taskGroupId);
  console.log('groupMap', groupMap);
  const { list: groupList = [] } = await ExtensionManager.getGroupInstance().listGroup();
  const groupId2Group = keyBy(groupList, (item) => item.groupId);

  return Object.keys(groupMap)
    .map((groupId) => {
      return {
        label: groupId2Group[groupId]?.groupName || '未分组',
        groupFieldValue: groupId as string,
        groupField: 'taskGroupId',
        taskList: groupMap[groupId] || [],
        sort: groupList.findIndex((item) => item.groupId === groupId),
      };
    })
    .sort((a, b) => a.sort - b.sort);
}
/**
 * 根据任务列表生成视图数据(开始时间)
 *
 * @param taskList 任务列表，类型为 TaskListItem 数组
 * @param field
 * @returns 返回类型为 ViewData 数组的 Promise 对象
 */
async function taskStartDateBuilder(
  taskList: TaskListItem[],
  field: 'taskStartDate' | 'finishTime' | 'deleteTime',
): Promise<ViewData[]> {
  const groupMap = groupBy(toRaw(taskList), (item) => item[field as string] || '');
  return Object.keys(groupMap)
    .map((taskStartDate) => {
      let label = '无时间';
      if (taskStartDate) {
        const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        const date = dayjs(taskStartDate);
        let template = `MM/DD (${weeks[date.day()]})`;
        if (date.year() !== dayjs().year()) {
          template = 'YY' + template;
        }
        label = dayjs(taskStartDate).format(template);
      }
      return {
        label,
        groupFieldValue: taskStartDate || '',
        groupField: 'taskStartDate',
        taskList: groupMap[taskStartDate] || [],
        sort: label === '无时间' ? Number.MAX_SAFE_INTEGER : dayjs(taskStartDate).valueOf(),
      };
    })
    .sort((a, b) => b.sort - a.sort);
}

/**
 * 根据任务列表生成视图数据(开始时间)
 *
 * @param taskList 任务列表，类型为 TaskListItem 数组
 * @param field
 * @returns 返回类型为 ViewData 数组的 Promise 对象
 */
async function taskTaskTimeBuilder(taskList: TaskListItem[]): Promise<ViewData[]> {
  const now = dayjs();
  const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const groupMap = groupBy(toRaw(taskList), (taskItem) => {
    let label = taskItem.taskStartDate!;
    if (!taskItem.taskStartDate) {
      label = '无时间';
    }
    if (taskItem.taskDateType === 'date') {
      const dateDayJs = dayjs(taskItem!.taskStartDate);
      if (now.isAfter(dateDayJs) && !now.isSame(dateDayJs, 'day')) {
        label = '已过期';
      }
    } else if (taskItem.taskDateType === 'dateSegment') {
      const dateDayJs = dayjs(taskItem.taskEndDate);
      if (now.isAfter(dateDayJs) && !now.isSame(dateDayJs, 'day')) {
        label = '已过期';
      }
    }
    return label;
  });
  return Object.keys(groupMap)
    .map((taskStartDate) => {
      let label = taskStartDate;
      let groupFieldValue = '';
      if (taskStartDate !== '已过期' && taskStartDate !== '无时间') {
        const date = dayjs(taskStartDate);
        let template = `MM/DD (${weeks[date.day()]})`;
        if (date.year() !== dayjs().year()) {
          template = 'YY' + template;
        }
        label = date.format(template);
      }
      groupFieldValue = taskStartDate;
      return {
        label,
        groupFieldValue,
        groupField: 'taskStartDate',
        taskList: groupMap[taskStartDate] || [],
        sort:
          label === '无时间'
            ? Number.MAX_SAFE_INTEGER - 100
            : label === '已过期'
              ? Number.MAX_SAFE_INTEGER
              : dayjs(taskStartDate).valueOf(),
        rightAction: label === '已过期' ? ExpiredRightAction : undefined,
        disableViewDrop: label === '已过期',
        disableAutoCollapse: label === '已过期',
      };
    })
    .sort((a, b) => b.sort - a.sort);
}

export const viewDataBuilder: Record<
  ViewGroupType,
  (taskList: TaskListItem[]) => Promise<ViewData[]>
> = {
  taskLevel: taskLevelBuilder,
  taskGroupId: taskGroupIdBuilder,
  taskStartDate: (taskList: TaskListItem[]) => taskTaskTimeBuilder(taskList),
  finishTime: (taskList: TaskListItem[]) => taskStartDateBuilder(taskList, 'finishTime'),
  deleteTime: (taskList: TaskListItem[]) => taskStartDateBuilder(taskList, 'deleteTime'),
};
