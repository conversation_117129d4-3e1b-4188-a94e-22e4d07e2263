import type { FunctionalComponent } from 'vue';
import { Link, Modal } from '@arco-design/web-vue';
import type { ViewData } from '@/views/GroupDetail/templates';
import { taskItemGoOnToDay } from '@/components/Tasks/TaskItem/TaskItemUtils.ts';
function handleClick(e: MouseEvent, viewData: ViewData) {
  e.stopPropagation();
  e.preventDefault();
  Modal.confirm({
    title: '顺延任务提示',
    content: () => (
      <div>
        确认将当前列表 {viewData.taskList.length} 个任务进行顺延到今天?
      </div>
    ),
    cancelText: '取消',
    okText: '顺延',
    okButtonProps: {
    },
    onOk: async () => {
      const promise = viewData.taskList.map(((taskItem) => {
        return taskItemGoOnToDay(taskItem);
      }));
      const res = await Promise.all(promise);
      console.log(res);
      window.dispatchEvent(new CustomEvent('taskList::refreshAll'));
    }
  });
}


export const ExpiredRightAction: FunctionalComponent<{ viewData: ViewData }> = (
  props,
  context
) => {
  return (
    <Link style={{ fontSize: '12px' }}
          onClick={(e) => handleClick(e, props.viewData)}>
      顺延
    </Link>
  );
}


// export const ExpiredRightAction = markRaw(defineComponent<{
//   viewData: ViewData;
// }>({
//   name: 'ExpiredRightAction',
//   props: ['viewData'],
//   setup(props) {
//     return () => (
//       <Link style={{ fontSize: '12px' }}
//             onClick={(e) => handleClick(e, props.viewData)}>
//         顺延
//       </Link>
//     );
//   },
// }));
