import type { TaskListItem } from '@xiaou66/todo-plugin'
import type { DefineComponent, Component } from "vue";

export { default as TaskViewLayer } from './TaskViewLayer.vue';
export * from './TaskViewLayer';

/**
 * 视图构建参数
 */
export interface ViewData {
  /**
   * 分组字段名称
   */
  groupField: string;
  /**
   * 分组字段值
   */
  groupFieldValue: string;
  /**
   * 类型名称
   */
  label: string;
  /**
   * 颜色
   */
  color?: string;
  /**
   * 任务列表
   */
  taskList: TaskListItem[];
  /**
   * 排序值
   */
  sort?: number;
  /**
   * 禁用任务拖拽
   */
  disableTaskDrag?: boolean;
  /**
   * 禁止任务视图防止任务
   */
  disableViewDrop?: boolean;

  /**
   * 右边操作
   */
  rightAction?:Component | undefined;

  /**
   * 禁止自动折叠
   */
  disableAutoCollapse?: boolean;
}
