<script setup lang="ts">
import { GridView } from '@/views/GroupDetail/templates';
import type { TaskListItem, IViewConfig } from '@xiaou66/todo-plugin'
import {computed, onMounted, ref, watch} from 'vue'
import type { ViewData } from './index.ts'
import { viewDataBuilder } from './TaskViewLayer.ts'
import ListView from "../ListView/ListView.vue";
import { TaskDragPreview } from '@/components/Tasks'

const props = withDefaults(defineProps<{
  viewMode: 'grid' | 'list';
  data: TaskListItem[];
  viewConfig: IViewConfig;
  finishData?: TaskListItem[];
  scene?: 'finish' | 'delete' | 'default',
}>(), {
  data: () => [],
  scene: 'default',
});

watch(() => props.data, () => {
  buildParams();
});
watch(() => props.viewConfig.groupType, () => {
  buildParams();
});

const viewDataList = ref<ViewData[]>([]);
function buildParams() {
  viewDataBuilder[props.viewConfig.groupType](props.data)
    .then((data) => {
      viewDataList.value = data;
  })
}
onMounted(() => {
  buildParams();
});

const finishView = computed<ViewData>(() => {
  return {
    groupField: 'finish',
    groupFieldValue: 'finish',
    label: '已完成',
    taskList: props.finishData || [],
    disableTaskDrag: true
  }
})

</script>

<template>
  <TaskDragPreview />
  <GridView v-if="viewMode === 'grid'"
            :viewDataList="viewDataList"
            :finishView="finishView" />
  <ListView v-else-if="viewMode === 'list'"
            :viewDataList="viewDataList"
            :scene="scene"
            :finishView="finishView" />
</template>

<style scoped lang="less">

</style>
