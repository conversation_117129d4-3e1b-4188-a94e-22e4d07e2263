<script setup lang="ts">
import type { ViewData } from "@/views/GroupDetail/templates";
import { computed, onMounted, onBeforeUnmount, ref, useTemplateRef } from 'vue'
import TaskItem from "@/components/Tasks/TaskItem/TaskItem.vue";
import type { TaskListItem } from "@xiaou66/todo-plugin";
import {combine} from "@atlaskit/pragmatic-drag-and-drop/combine";
import {
  dropTargetForElements,
} from "@atlaskit/pragmatic-drag-and-drop/element/adapter";
import { useTaskDragContext } from '@/components/Tasks/TaskItem/TaskDragWrapper.ts'
import { useTaskDetailDrawer, useTaskViewProvide } from '@/components/Tasks';
import { getGroupItemData } from '@/components/Tasks/TaskItem/TaskGroup.ts'

const props = defineProps<{
  viewData: ViewData;
}>();

const titleColor = computed(() => props.viewData.color);

const taskViewProvide = useTaskViewProvide();

const taskDetailDrawerInstance = useTaskDetailDrawer();
// 处理任务点击事件
function handleTaskClick(item: TaskListItem) {
  taskDetailDrawerInstance.show(item);
}

const collapseContentRef = useTemplateRef<HTMLDivElement>('collapseContentRef');
const groupRef = useTemplateRef<HTMLDivElement>('groupRef');
let cleanup = () => { };
const [taskDragContext, setTaskDragContext] = useTaskDragContext();

const emits = defineEmits<{
  open: [id: string];
  close: [id: string];
}>();
const dragHover = ref<boolean>(false);
onMounted(() => {
  if (!groupRef.value || !collapseContentRef.value) {
    return;
  }
  cleanup = combine(
    dropTargetForElements({
      element: collapseContentRef.value,
      onDragEnter: () => {
        if (!props.viewData.taskList || !props.viewData.taskList.length) {
          emits('open', props.viewData.groupFieldValue);
        }
      },
      onDragLeave() {
        if (!props.viewData.taskList || !props.viewData.taskList.length) {
          emits('close', props.viewData.groupFieldValue);
        }
      },
    }),
    dropTargetForElements({
      element: groupRef.value,
      canDrop(args) {
        return !props.viewData.disableViewDrop;
      },
      getData(args) {
        return getGroupItemData({});
      },
      onDragEnter: () => {
        dragHover.value = true;
        const viewData = props.viewData;
        if (viewData.groupField === 'finish') {
          setTaskDragContext({
            newTask: {
              finish: true,
            }
          });
        } else {
          setTaskDragContext({
            newTask: {
              [viewData.groupField]: viewData.groupFieldValue,
              finish: false,
            }
          });
        }
      },
      onDragLeave() {
        dragHover.value = false;
      },
      onDrop() {
        dragHover.value = false;
      },
    }),
    dropTargetForElements({
      element: document.documentElement,
      canDrop(args) {
        return true;
        // return false;
      },
      onDrop(args) {
        setTaskDragContext(undefined);
      },
    }),
  );
});

onBeforeUnmount(() => {
  cleanup();
});
</script>

<template>
 <div ref="collapseContentRef">
   <a-collapse-item :key="viewData.groupFieldValue">
     <template #header>
       <div class="u-fx u-gap10">
         <div>{{ viewData.label }}</div>
         <div style="color: var(--color-neutral-4); font-weight: 400;">
           {{ viewData.taskList?.length || 0 }}
         </div>
       </div>
     </template>
     <template v-if="viewData.rightAction" #extra>
       <Component :is="viewData.rightAction" :viewData="viewData" />
     </template>
     <template #expand-icon="{ active }">
       <iconpark-icon style="font-size: 14px" v-if="active" name="down"></iconpark-icon>
       <iconpark-icon style="font-size: 14px" v-else name="right"></iconpark-icon>
     </template>
     <div ref="groupRef" class="group-container">
       <div v-if="viewData.taskList.length">
         <!-- 任务项 -->
         <TaskItem
           v-for="task in viewData.taskList"
           :key="task.id"
           :task-item="task"
           show-type="default"
           @click="handleTaskClick"
           @dragStart="taskViewProvide.dragStart"
           @dragEnd="taskViewProvide.dragEnd"
           @save="taskViewProvide.saveTask"
           :disable-drag="!!viewData.disableTaskDrag"
         />
       </div>
       <div v-else>
         <a-empty>无任务</a-empty>
       </div>
     </div>
   </a-collapse-item>
 </div>
</template>

<style scoped lang="less">
:deep(.arco-collapse-item-header-title) {
  color: v-bind(titleColor);
}
.group-container {
  border: 2px solid transparent;
  border-radius: var(--border-radius-small);
  padding: 2px;
  &.drag-hover {
    border: 2px solid rgb(var(--arcoblue-5));
  }
}
</style>
