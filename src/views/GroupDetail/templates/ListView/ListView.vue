<script setup lang="ts">
import type { ViewData } from "@/views/GroupDetail/templates";
import ListItem from "./ListItem.vue";
import TaskCreateInput from "@/components/Tasks/TaskCreateInput/TaskCreateInput.vue";
import {computed, onMounted, ref, useTemplateRef, watch} from 'vue'
import type { TaskCreateInputInstance } from '@/components/Tasks/TaskCreateInput/TaskCreateInput.ts'
import type {  TaskSaveParams } from '@xiaou66/todo-plugin'
import {useTaskViewProvide} from "@/components/Tasks";
import { cloneDeep } from 'es-toolkit';
const props = withDefaults( defineProps<{
  viewDataList: ViewData[];
  scene?: 'finish' | 'delete' | 'default',
  finishView: ViewData;
}>(), {
  scene: 'default',
})

const taskViewContext = useTaskViewProvide();
const taskCreateDefaultConfig = computed<Partial<TaskSaveParams>>(() => {
  if (taskViewContext.context) {
    // console.log('taskCreateDefaultConfig', cloneDeep({
    //   taskGroupId: taskViewContext.context.value?.groupId,
    //   tagNameList: taskViewContext.context.value?.tagNameList || [],
    //   taskLevel: taskViewContext.context.value?.taskLevel,
    // }));
    return cloneDeep({
      taskGroupId: taskViewContext.context.value?.groupId,
      tagNameList: taskViewContext.context.value?.tagNameList || [],
      taskLevel: taskViewContext.context.value?.taskLevel,
    })
  }
  return {};
});
const addTaskInputRef = useTemplateRef<TaskCreateInputInstance>('addTaskInputRef');
function handleAddTask(createTask: TaskSaveParams) {
  taskViewContext.saveTask(createTask);
}
const collapseActiveKey = ref<string[]>(['1']);

function autoCollapseActiveKey() {
  const isOpenFinish = collapseActiveKey.value.includes('finish');
  const oldActiveKey = collapseActiveKey.value;
  const disableAutoCollapseValue = props.viewDataList.filter(item => !!item.disableAutoCollapse)
    .map(item => item.groupFieldValue);
  collapseActiveKey.value = props.viewDataList.filter(item => !item.disableAutoCollapse)
    // 判断出任务长度和需要自动折叠分组
    .filter(item => item.taskList.length && !disableAutoCollapseValue.includes(item.groupFieldValue))
    .map(item => item.groupFieldValue);
  if (isOpenFinish) {
    collapseActiveKey.value.push('finish');
  }
  collapseActiveKey.value.push(...oldActiveKey.filter(item => disableAutoCollapseValue.includes(item)));
}
const taskCount = computed(() => {
  return Object.keys(props.viewDataList)
    .map(key => props.viewDataList[key as any].taskList.length)
});
watch(taskCount, () => {
  autoCollapseActiveKey();
});
onMounted(() => {
  autoCollapseActiveKey();
});

function openCollapse(value: string) {
  if (collapseActiveKey.value.indexOf(value) === -1) {
    collapseActiveKey.value.push(value);
  }
}
function closeCollapse(value: string) {
  const index = collapseActiveKey.value.indexOf(value);
  if (index !== -1) {
    collapseActiveKey.value.splice(index, 1);
  }
}

</script>

<template>
  <div style="width: 99%;">
    <div class="header">
      <TaskCreateInput
        v-if="scene === 'default'"
        ref="addTaskInputRef"
        :createDefaultConfig="taskCreateDefaultConfig"
        :disable-module="[]"
        @create="handleAddTask" />
    </div>
    <a-scrollbar class="scrollbar-width">
      <a-collapse v-model:active-key="collapseActiveKey"
                  class="group-view-list">
        <list-item v-for="(viewData) in viewDataList"
                   :key="viewData.groupFieldValue"
                   :viewData="viewData"
                   @open="openCollapse"
                   @close="closeCollapse" />
        <list-item v-if="finishView.taskList && finishView.taskList.length"
                   :view-data="finishView" />
      </a-collapse>
    </a-scrollbar>
  </div>
</template>

<style scoped lang="less">
:deep(.scrollbar-width) {
  height: calc(100vh - 158px) !important;
  overflow-x: auto !important;
}
.header {
  padding: 10px 10px 0;
}
.group-view-list {
  .group-container {
    display: flex;
    flex-direction: column;
    gap: 0;
  }
  :deep(.arco-collapse-item-content) {
    padding-right: 6px;
  }
}
</style>
