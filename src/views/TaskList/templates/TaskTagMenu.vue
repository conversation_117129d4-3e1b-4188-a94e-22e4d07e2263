<script setup lang="tsx">
import {  nextTick, onMounted, ref, useTemplateRef } from 'vue';
import type { ITaskTag } from '@xiaou66/todo-plugin';
import { ExtensionManager } from '@/extension';
import type { IMenuItem } from '@/views/TaskList/templates/Menu.ts';
import { useEventListener } from '@vueuse/core';
import { DialogPlugin } from 'tdesign-vue-next';
import TaskTagSaveModal from '@/views/TaskList/templates/TaskTagMenu/TaskTagSaveModal.vue';
import type { TaskTagSaveModalInstance } from '@/views/TaskList/templates/TaskTagMenu/TaskTagSaveModal.ts';
import { useCollapse } from '@/hooks/useCollapse.ts';
import { FlowExecutor } from '@/views/CollectScene/flow';
import { ContextMenu, ContextMenuItem } from '@xiaou66/u-web-ui';

const tagListData = ref<ITaskTag[]>();

const props = defineProps<{
  setCategoryList: (menuItems: IMenuItem[]) => void
}>();


const active = defineModel<string>('active');
const tagStatistics = ref<{
  [key: string]: number;
}>({});

function handleStatisticsCategoriesCount() {
  ExtensionManager.getTaskInstance().listTask({})
    .then((data) => {
      const list = data.list;
      // 统计标签数量
      tagStatistics.value = list.reduce((acc, cur) => {
        for (const tagName of cur.tagNameList || []) {
          if (acc[tagName]) {
            acc[tagName] += 1;
          } else {
            acc[tagName] = 1;
          }
        }
        return acc;
      }, {} as Record<string, number>);
    });
}

async function request() {
  const taskTagInstance = ExtensionManager.getTaskTagInstance();
  tagListData.value = await taskTagInstance.listTag();
  props.setCategoryList(tagListData.value.map(item => {
    return {
      id: item.id,
      name: item.name,
      desc: '',
      menuType: 'tags',
      searchParams: {
        tagNameList: [ item.name ]
      }
    } as IMenuItem
  }));
  nextTick(() => {
    calcCollapseHeight()
  });
  handleStatisticsCategoriesCount();
}
onMounted(() => {
  request()
});
useEventListener(window, 'taskList::refreshAll', () => {
  if (!collapsed.value) {
    request();
  }
});

function handleDeleteTag(tag: ITaskTag) {
  const dialogInstance = DialogPlugin.confirm({
    header: '移除标签提醒',
    cancelBtn: '放弃',
    body: () => (
      <div>
        <div>在所有任务中移除「{tag.name}」标签使用</div>
        <div>
          <span style="color: #f5222d">此操作</span>
          <span style="font-weight: bold; color: #f5222d">无法还原</span>
        </div>
      </div>
    ),
    confirmBtn: {
      content: '确认移除',
      theme: 'danger'
    },
    onConfirm: async () => {
      dialogInstance.update({
        confirmLoading: true,
      })
      await ExtensionManager.getTaskTagInstance().deleteTag(tag.name);
      await FlowExecutor.adjustFlowNodeData();
      await request();
      if (active.value === tag.id) {
        // 当前是删除的标签
        active.value = 'toDay';
      }
      dialogInstance.hide();
      dialogInstance.destroy();
    }
  })
}
function handleCreateAfter(tagId: string) {
  active.value = tagId;
  request();
  collapsed.value = false;
}
const [collapsed, handleCollapse, calcCollapseHeight] = useCollapse();
const taskTagSaveModalRef = useTemplateRef<TaskTagSaveModalInstance>('taskTagSaveModalRef');
</script>

<template>
  <TaskTagSaveModal ref="taskTagSaveModalRef"
                    @create="handleCreateAfter" />
  <!--  分组  -->
  <div class="sidebar-group">
    <div class="sidebar-section">
      <div class="section-title"
           @click="handleCollapse">
        <div class="u-fx u-fac gap-1 u-pos-rel" >
          <t-icon v-if="collapsed" class="i-u-right"></t-icon>
          <t-icon v-else class="i-u-down"></t-icon>
          <div>标签</div>
        </div>
        <div class="u-fx u-fac">
          <div @click.stop="taskTagSaveModalRef?.show()">
            <t-icon class="i-u-plus text-lg"></t-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="sidebar-section item"
         :class="{ collapsed }">
      <div  ref="collapseRef">
        <ContextMenu v-for="tag in tagListData"
                     :key="tag.id"
                     size="small">
          <div class="sidebar-item"
               :class="{ active: active === tag.id }"
               @click="active = tag.id">
            <div class="name">{{ tag.name }}</div>
            <div class="count">{{ tagStatistics[tag.name] || 0 }}</div>
          </div>
          <template #content>
            <ContextMenuItem icon="i-u-delete"
                             style="color: red"
                             label="删除"
                             theme="danger"
                             @click="handleDeleteTag(tag)">
            </ContextMenuItem>
          </template>
        </ContextMenu>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
