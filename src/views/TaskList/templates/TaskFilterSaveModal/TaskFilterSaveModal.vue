<script setup lang="ts">
import {nextTick, ref, useTemplateRef} from "vue";
import type {TaskFilterSaveModalInstance} from "./TaskFilterSaveModal.ts";
import type {ITaskTag, IGroupInfo, SaveTaskFilterType, ITaskFilter} from "@xiaou66/todo-plugin";
import { ExtensionManager } from "@/extension";
import TaskFilterTime from './TaskFilterTime.vue';
import TaskFilterLevelSelect from "./TaskFilterLevelSelect.vue";
import { FormInstanceFunctions } from 'tdesign-vue-next';
import {cloneDeep, merge} from "es-toolkit";


const emits = defineEmits<{
  close: [id?: string]
}>();
const visible = ref(false);
const taskTagList = ref<ITaskTag[]>([]);
const groupList = ref<IGroupInfo[]>([]);
const formRef = useTemplateRef<FormInstanceFunctions>('formRef');
const inputRef = useTemplateRef('inputRef');

const defaultFormData: SaveTaskFilterType = {
  filterName: '',
  dynamicTimeTypeList: [],
  customDiffTime: [],
  taskGroupIdList: [],
  tagNameList: [],
  taskLevelList: [],
  filterType: 'default',
}

const formData = ref<SaveTaskFilterType>(cloneDeep(defaultFormData));

function show(data?: ITaskFilter) {
  formData.value = merge(cloneDeep(defaultFormData), data || {});
  visible.value = true;
  nextTick(() => {
    inputRef.value.focus()
  })
  ExtensionManager.getGroupInstance()
    .listGroup({})
    .then((res) => {
      groupList.value = res.list;
    });
  ExtensionManager.getTaskTagInstance()
    .listTag()
    .then((res) => {
      taskTagList.value = res;
    });
}

async function handleTaskFilterSave() {
  const res = await formRef.value?.validate()
  if (res !== true) {
    return false;
  }
  await ExtensionManager.getTaskFilterInstance()
    .saveTaskFilter(formData.value);
  return true;
}
function handleCloseEvent() {
  emits('close', formData.value?.id)
}

defineExpose<TaskFilterSaveModalInstance>({
  show,
});


</script>

<template>
  <t-dialog header="筛选器"
            v-model:visible="visible"
            :width="500"
            :top="100"
            destroyOnClose
            confirm-btn="保存"
            @confirm="handleTaskFilterSave"
            @close="handleCloseEvent"
            :close-btn="false">
    <t-form ref="formRef"
            :data="formData" :labelWidth="70">
      <t-form-item label="名称"
                   name="filterName"
                   :rules="[{ required: true, message: '请取一个好听筛选器的名称' }]">
        <t-input v-model:value="formData.filterName"
                 ref="inputRef" />
      </t-form-item>
      <t-form-item label="时间">
        <TaskFilterTime v-model:dynamic-time-type="formData.dynamicTimeTypeList"
                        v-model:custom-diff-time="formData.customDiffTime" />
      </t-form-item>
      <t-form-item label="分组">
        <t-select v-model:value="formData.taskGroupIdList"
                  placeholder="默认所有"
                  multiple
                  clearable>
            <t-option value="collectBox" label="未分组" />
            <t-option v-for="group in groupList"
                      :key="group.groupId"
                      :value="group.groupId"
                      :label="group.groupName" />
        </t-select>
      </t-form-item>
      <t-form-item label="标签">
        <t-select v-model:value="formData.tagNameList"
                  placeholder="默认所有"
                  multiple
                  clearable>
            <t-option v-for="taskTag in taskTagList"
                      :key="taskTag.id"
                      :value="taskTag.name"
                      :label="taskTag.name" />
        </t-select>
      </t-form-item>
      <t-form-item label="优先级">
        <TaskFilterLevelSelect
          v-model:modal-value="formData.taskLevelList" />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>

<style scoped lang="less">

</style>
