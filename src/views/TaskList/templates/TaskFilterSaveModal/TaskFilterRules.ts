import type { TaskListParams } from '@xiaou66/todo-plugin'
import dayjs from 'dayjs';
export interface ITaskFilterTimeRule {
  code: string;
  label: string;
  execute?: () => Partial<TaskListParams>;
}

function getTimeRange(prefixDays: number, nextDays: number): number[] {
  const now = dayjs();
  return [
    now.startOf('day').subtract(prefixDays, 'day').valueOf(),
    now.endOf('day').add(nextDays, 'day').valueOf()
  ]
}
export const TASK_FILTER_TIME_RULES: ITaskFilterTimeRule[] = [
  {
    code: 'expired',
    label: '已过期',
    execute: () => {
      return {
        expired: true
      }
    }
  },
  {
    code: 'unsetTime',
    label: '未设置',
    execute: () => {
      return {
        unsetTime: true
      }
    }
  },
  {
    code: 'today',
    label: '今天',
    execute: () => {
      return {
        taskTimeRange: getTimeRange(0, 0)
      }
    }
  },
  {
    code: 'tomorrow',
    label: '明天',
    execute: () => {
      return {
        taskTimeRange: getTimeRange(0, 1)
      }
    }
  },
  {
    code: 'yesterday',
    label: '昨天',
    execute: () => {
      return {
        taskTimeRange: getTimeRange(1, 0)
      }
    }
  },
  {
    code: 'thisWeek',
    label: '本周',
    execute: () => {
      return {
        taskTimeRange: [
          dayjs().startOf('week').valueOf(),
          dayjs().endOf('week').valueOf()
        ]
      }
    }
  },
  {
    code: 'prefixWeek',
    label: '上周',
    execute: () => {
      return {
        taskTimeRange: [
          dayjs().subtract(1, 'week').startOf('week').valueOf(),
          dayjs().subtract(1, 'week').endOf('week').valueOf()
        ]
      }
    }
  },
  {
    code: 'prefixMonth',
    label: '上月',
    execute: () => {
      return {
        taskTimeRange: [
          dayjs().subtract(1, 'month').startOf('month').valueOf(),
          dayjs().subtract(1, 'month').endOf('month').valueOf()
        ]
      }
    }
  },
  {
    code: 'month',
    label: '本月',
    execute: () => {
      return {
        taskTimeRange: [
          dayjs().startOf('month').valueOf(),
          dayjs().endOf('month').valueOf()
        ]
      }
    }
  },
  {
    code: 'custom',
    label: '自定义',
  }
]


