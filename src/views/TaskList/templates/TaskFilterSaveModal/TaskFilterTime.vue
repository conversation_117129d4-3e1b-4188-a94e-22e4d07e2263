<script setup lang="ts">
import { TASK_FILTER_TIME_RULES } from './TaskFilterRules.ts';
import { computed } from 'vue';
const dynamicTimeType = defineModel<string[]>('dynamicTimeType', {
  default: () => [],
  set(v) {
    if (!v.includes('custom')) {
      customDiffTime.value = [];
    }
    return v;
  },
});
const customDiffTime = defineModel<number[]>('customDiffTime', {
  default: () => []
});

const isCustom = computed(() => dynamicTimeType.value.includes('custom'));

function inputAutoFocus(e:PointerEvent) {
  if (e.target instanceof HTMLInputElement) {
    e.target.focus();
  }
  return e;
}
</script>

<template>
 <div style="width: 100%">
   <div v-if="isCustom"
        class="u-mb10">
     <t-input-group size="mini">
       <t-input v-model:model-value="customDiffTime[0]"
                @click.stop="inputAutoFocus" >
         <template #prefixIcon>前</template>
         <template #suffixIcon>天</template>
       </t-input>
       <t-input v-model:model-value="customDiffTime[1]"
                @click.stop="inputAutoFocus">
         <template #prefixIcon>后</template>
         <template #suffixIcon>天</template>
       </t-input>
     </t-input-group>
   </div>
   <t-select v-model:model-value="dynamicTimeType"
             placeholder="默认所有" multiple allow-clear>
     <t-option v-for="(rule) in TASK_FILTER_TIME_RULES"
               :key="rule.code"
               :value="rule.code"
               :label="rule.label">
     </t-option>
   </t-select>
 </div>
</template>

<style scoped lang="less">

</style>
