<script setup lang="ts">
import TaskConstants from "@/constants/tasks/TaskConstants.ts";
import {computed} from "vue";


const modalValue = defineModel<string[]>('modalValue', {
  default: () => []
});

const allValue = computed(() => !modalValue.value || modalValue.value.length == 0);

function handleSelectAll() {
  modalValue.value = [];
}
</script>

<template>
  <t-radio :checked="allValue"
           @click="handleSelectAll">全部</t-radio>
  <t-divider layout="vertical" style="margin: 0 6px;"></t-divider>
  <t-checkbox-group class="flex gap-2" v-model:value="modalValue">
    <t-checkbox v-for="(item) in TaskConstants.PRIORITY_SELECT_OPTIONS_TAG"
                :key="item.value"
                :value="item.value"
                class="select-none">
      <div class="u-fx u-fac gap-1">
        <t-icon class="i-u-mark"  :style="{ color: item.color }" />
        <div :style="{ color: item.tagColor }">{{ item.label }}</div>
      </div>
    </t-checkbox>
  </t-checkbox-group>
</template>

<style scoped lang="less">

</style>
