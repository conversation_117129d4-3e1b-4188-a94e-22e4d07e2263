<script setup lang="ts">
import type { TaskTagSaveModalInstance } from './TaskTagSaveModal.ts';
import { nextTick, ref, useTemplateRef } from 'vue';
import type { ITaskTagCreateParam } from '@xiaou66/todo-plugin';
import { FormInstanceFunctions } from 'tdesign-vue-next';
import { ExtensionManager } from '@/extension';

const visible = defineModel<boolean>('visible');
function show() {
  form.value.name = '';
  visible.value = true;
  nextTick(() => {
    inputRef.value.focus()
  })
}
const formRef = useTemplateRef<FormInstanceFunctions>('formRef');
const inputRef = useTemplateRef('inputRef');
const form = ref<ITaskTagCreateParam>({
  name: '',
});
const emits = defineEmits<{
  create: [id: string]
}>();

async function handleBeforeOk(close?: boolean) {
  const res = await formRef.value.validate();
  if (res !== true) {
    return false;
  }
  const taskTag = await ExtensionManager.getTaskTagInstance().createTag({
    ...form.value,
  });
  if (close) {
    visible.value = false;
  }
  emits('create', taskTag.id);
}
defineExpose<TaskTagSaveModalInstance>({
  show,
})
</script>

<template>
  <t-dialog header="保存标签"
            v-model:visible="visible"
            :width="500"
            :top="100"
            destroyOnClose
            confirm-btn="保存"
            @confirm="handleBeforeOk"
            @close="hide">
    <t-form ref="formRef" :data="form">
      <t-form-item label="标签名称"
                   name="name"
                   :rules="[{ required: true, message: '请输入标签名称' }]">
        <t-input v-model:value="form.name"
                 ref="inputRef"
                 @enter="handleBeforeOk(true)" />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>

<style scoped lang="less">

</style>
