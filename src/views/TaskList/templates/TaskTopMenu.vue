<script setup lang="ts">
// 分类数据
import type { IMenuItem } from '@/views/TaskList/templates/Menu.ts';
import dayjs from 'dayjs';
import { onMounted, ref } from 'vue';
import { ExtensionManager } from '@/extension';
import { useEventListener } from '@vueuse/core';
import { umami } from '@/utils/umami';

const props = defineProps<{
  setCategoryList: (menuItems: IMenuItem[]) => void
}>();

// @unocss-include
const categories: IMenuItem[] = [
  {
    id: 'toDay',
    name: '今天',
    desc: '让每一天都有成就感',
    menuType: 'system',
    searchParams: {
      taskTimeRange: [
        dayjs().startOf('day').valueOf(),
        dayjs().endOf('day').valueOf()
      ],
    },
  },
  {
    id: 'threeDays',
    name: '三日规',
    desc: '三日回望，三日前瞻，进度尽在掌中',
    menuType: 'system',
    searchParams: {
      taskTimeRange: [
        dayjs().subtract(3, 'day').startOf('day').valueOf(),
        dayjs().add(3, 'day').endOf('day').valueOf()
      ]
    },
    icon: 'i-u-calendar',
  },
  {
    id: 'collectBox',
    name: '未分组',
    desc: '杂乱？收进来就是整齐的第一步',
    menuType: 'system',
    searchParams: {
      groupId: 'collectBox',
    },
    icon: 'i-u-receive',
  },
  // {
  //   id: 'all',
  //   name: '全部',
  //   desc: '杂乱？收进来就是整齐的第一步',
  //   menuType: 'system',
  //   searchParams: {},
  //   icon: 'receive',
  //   // disableCount: true,
  // },
];
onMounted(() => {
  props.setCategoryList(categories);
  handleStatisticsCategoriesCount();
});

const categoryStatistics = ref<{
  [key: string]: number;
}>({});

function handleStatisticsCategoriesCount() {
  categories.map(async (item) => {
    if (item.disableCount) {
      return;
    }
    await ExtensionManager.getTaskInstance().listTask({
      ...item.searchParams,
    }).then(res => {
      categoryStatistics.value[item.id] = res.total;
      // item.data = res.list;
    });
  });
}



const active = defineModel<string>('active');


useEventListener(window, 'taskList::refreshAll', () => {
   handleStatisticsCategoriesCount();
});
function handleMenuClick(category: IMenuItem) {
  active.value = category.id;
  umami().trackEvent(`页面预览:任务/${category.name}`, {
    title: `任务/${category.name}`,
  });
}
</script>

<template>
  <div class="sidebar-section">
    <div
      v-for="(category, index) in categories" :key="index"
      class="sidebar-item"
      :id="category.id"
      :class="{ active: active === category.id }"
      @click="handleMenuClick(category)"
    >
      <div v-if="category.name === '今天'"
           class="relative">
        <div class="icon i-u-calendar-empty">
        </div>
        <div class="pos-absolute  z-999 font-bold"
             style="color: #767676; font-size: 8px; top: 4px;left: 5px">
          {{ new Date().getDate() }}
        </div>
      </div>
      <t-icon v-else-if="category.icon"
              class="icon"
              :class="category.icon"></t-icon>
      <div class="name">{{ category.name }}</div>
      <div class="count" v-if="!category.disableCount">
        {{ categoryStatistics[category.id] || 0 }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
