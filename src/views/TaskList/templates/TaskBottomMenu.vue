<script setup lang="ts">
import type { IMenuItem } from '@/views/TaskList/templates/Menu.ts';
import { onMounted } from 'vue';
import { umami } from '@/utils/umami';


const props = defineProps<{
  setCategoryList: (menuItems: IMenuItem[]) => void
}>();

const categories: IMenuItem[] = [
  {
    id: 'finish',
    name: '已完成',
    desc: '打钩声连成片，成就感看得见',
    menuType: 'system',
    icon: 'i-u-check',
    searchParams: {
      searchType: 'finish'
    },
    viewConfig: {
      groupType: 'finishTime',
      sortType: 'custom',
      showFinish: false,
    }
  },
  {
    id: 'delete',
    name: '已删除',
    desc: '任务君已休假，请勿打扰~',
    menuType: 'system',
    icon: 'i-u-delete',
    searchParams: {
      searchType: 'deleted'
    },
    viewConfig: {
      groupType: 'deleteTime',
      sortType: 'custom',
      showFinish: false,
    },
  },
];
onMounted(() => {
  props.setCategoryList(categories);
});

const active = defineModel<string>('active');
function handleMenuClick(category: IMenuItem) {
  active.value = category.id;
  umami().trackEvent(`页面预览:任务/${category.name}`, {
    title: `任务/${category.name}`,
  });
}
</script>

<template>
  <div>
    <a-divider style="margin: 10px 0;" />
    <div class="sidebar-section">
      <div
        :id="category.id"
        v-for="(category, index) in categories" :key="index"
        class="sidebar-item"
        :class="{ active: active === category.id }"
        @click="handleMenuClick(category)"
      >
        <svg
          v-if="category.name === '今天'"
          class="icon"
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 24 24"
        >
          <path
            fill="currentColor"
            d="M 19.6 3.313 L 18.515 3.313 L 18.515 1.141 L 16.343 1.141 L 16.343 3.313 L 7.656 3.313 L 7.656 1.141 L 5.485 1.141 L 5.485 3.313 L 4.399 3.313 C 3.199 3.313 2.227 4.285 2.227 5.484 L 2.227 20.686 C 2.227 21.885 3.199 22.858 4.399 22.858 L 19.6 22.858 C 20.806 22.858 21.772 21.891 21.772 20.686 L 21.772 5.484 C 21.772 4.285 20.8 3.313 19.6 3.313 M 19.6 20.686 L 4.399 20.686 L 4.399 9.828 L 19.6 9.828 L 19.6 20.686 Z M 19.6 7.656 L 4.399 7.656 L 4.399 5.484 L 19.6 5.484 L 19.6 7.656 Z"
            style=""
            transform="matrix(1, 0, 0, 1, 1.7763568394002505e-15, 0)"
          />
          <text
            x="12"
            y="18"
            text-anchor="middle"
            font-weight="bold"
            font-size="9"
            fill="currentColor"
          >
            {{ new Date().getDate() }}
          </text>
        </svg>
        <span class=" pr-3">
          <t-icon v-if="category.icon"
                  class="text-lg"
                  :class="category.icon" />
        </span>
        <div class="name">{{ category.name }}</div>
      </div>
    </div>
</div>
</template>

<style scoped lang="less">

</style>
