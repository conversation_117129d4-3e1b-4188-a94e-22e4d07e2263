<script setup lang="tsx">
import { useCollapse } from '@/hooks/useCollapse.ts';
import { TaskFilterSaveModal } from "./TaskFilterSaveModal";
import type { TaskFilterSaveModalInstance } from "./TaskFilterSaveModal";
import { onMounted, ref, useTemplateRef, nextTick } from "vue";
import {ExtensionManager} from "@/extension";
import type { ITaskFilter } from "@xiaou66/todo-plugin";
import  { ContextMenu, ContextMenuItem } from "@xiaou66/u-web-ui";
import {useEventListener} from "@vueuse/core";
import { DialogPlugin } from 'tdesign-vue-next';
import type { IMenuItem } from './index.ts';
import { FlowExecutor } from '@/views/CollectScene/flow';


const props = defineProps<{
  setCategoryList: (menuItems: IMenuItem[]) => void
}>();
const [collapsed, handleCollapse, calcCollapseHeight] = useCollapse();


const taskFilterSaveModalRef = useTemplateRef<TaskFilterSaveModalInstance>('taskFilterSaveModalRef');
const data =ref<ITaskFilter[]>()
async function requestQuery() {
  await ExtensionManager.getTaskFilterInstance().getTaskFilterList()
    .then((list) => {
      data.value = list;
      props.setCategoryList([]);
      props.setCategoryList(list.map(item => {
        return {
          id: item.id,
          name: item.filterName,
          desc: '',
          menuType: 'filter',
          searchParams: {
            ...item,
          }
        } as IMenuItem
      }));
    });

  nextTick(() => {
    calcCollapseHeight();
  }).then(() => {});
}
onMounted(() => {
  requestQuery();
})
useEventListener(window, 'taskList::refreshAll', () => {
  if (!collapsed.value) {
    requestQuery();
  }
});
const active = defineModel<string>('active');
 function handleFilterSaveModelClose(id?: string) {
   requestQuery()
     .then(() => {
       console.log('11111', active.value === id);
       if (id && active.value === id) {
         window.dispatchEvent(new CustomEvent('tasklist::taskView'));
       }
     })
}
function handleDeleteTaskFilter(taskFilter: ITaskFilter) {
  const dialogInstance = DialogPlugin.confirm({
    header: '删除筛选器提醒',
    cancelBtn: '放弃',
    body: () => (
      <div>
        <div>确定要删除「{taskFilter.filterName}」筛选器吗？</div>
        <div>
          <span style="color: #f5222d">筛选器删除后, </span>
          <span style="font-weight: bold; color: #f5222d">无法还原</span>
        </div>
      </div>
    ),
    confirmBtn: {
      content: '确认删除',
      theme: 'danger'
    },
    onConfirm: async () => {
      dialogInstance.update({
        confirmLoading: true,
      })
      await ExtensionManager.getTaskFilterInstance().deleteTaskFilter(taskFilter.id);
      await FlowExecutor.adjustFlowNodeData();
      await requestQuery();
      if (active.value === taskFilter.id) {
        // 当前是删除的筛选器
        active.value = 'toDay';
      }
      dialogInstance.hide();
      dialogInstance.destroy();
    }
  })
}
</script>

<template>
  <TaskFilterSaveModal ref="taskFilterSaveModalRef"
    @close="handleFilterSaveModelClose" />
  <!--  分组  -->
  <div class="sidebar-group">
    <div class="sidebar-section">
      <div class="section-title"
           @click="handleCollapse">
        <div class="u-fx u-fac gap-1 u-pos-rel" >
          <t-icon v-if="collapsed" class="i-u-right"></t-icon>
          <t-icon v-else class="i-u-down"></t-icon>
          <div>筛选器</div>
        </div>
        <div class="u-fx u-fac">
          <div @click.stop="taskFilterSaveModalRef?.show()">
            <t-icon class="i-u-plus text-lg"></t-icon>
          </div>
        </div>
      </div>
    </div>
    <div class="sidebar-section item"
         :class="{ collapsed }">
      <div  ref="collapseRef">
        <ContextMenu v-for="taskFilter in data"
                     :key="taskFilter.id"
                     size="small">
          <div class="sidebar-item"
               :class="{ active: active === taskFilter.id }"
               @click="active = taskFilter.id">
            <!--              <div class="icon">🐝</div>-->
            <div class="name">{{ taskFilter.filterName }}-222</div>
            <!--            <div class="count">{{ groupStatistics[group.groupId] || 0 }}</div>-->
          </div>
          <template #content>
            <ContextMenuItem icon="i-u-write"
                             label="编辑"
                             @click="taskFilterSaveModalRef?.show(taskFilter)">
            </ContextMenuItem>
            <t-divider style="margin: 4px 0;" />
            <ContextMenuItem icon="i-u-delete"
                             style="color: red"
                             label="删除"
                             theme="danger"
                             @click="handleDeleteTaskFilter(taskFilter)">
            </ContextMenuItem>
          </template>
        </ContextMenu>
      </div>
    </div>
  </div>

</template>

<style scoped lang="less">

</style>
