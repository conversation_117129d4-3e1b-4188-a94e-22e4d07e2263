<script setup lang="tsx">
import type { IMenuItem } from './index.ts';
import { h, nextTick, onMounted, ref, useTemplateRef } from 'vue';
import { ExtensionManager } from '@/extension';
import type { IGroupListResult } from '@xiaou66/todo-plugin';
import { useEventListener } from '@vueuse/core';
import GroupBoxSaveDrawer from "@/views/GroupBox/templates/GroupBoxSaveDrawer.vue";
import type {GroupBoxSaveDrawerInstance} from "@/views/GroupBox/templates/GroupBoxSaveDrawerInstance.ts";
import type { IGroupInfo } from '@xiaou66/todo-plugin';
import { ContextMenu, ContextMenuItem } from '@xiaou66/u-web-ui';
import { Modal } from '@arco-design/web-vue';
import { useCollapse } from '@/hooks/useCollapse.ts';
import { FlowExecutor } from '@/views/CollectScene/flow';
import { DialogPlugin } from 'tdesign-vue-next';
const props = defineProps<{
  setCategoryList: (menuItems: IMenuItem[]) => void
}>();

const groupData = ref<IGroupListResult>({
  total: 0,
  list: []
});

async function request() {
  const groupInstance = ExtensionManager.getGroupInstance();
  groupData.value = await groupInstance.listGroup({});
  props.setCategoryList(groupData.value.list.map(item => {
    return {
      id: item.groupId,
      name: item.groupName,
      desc: '',
      menuType: 'group',
      searchParams: {
        groupId: item.groupId
      }
    } as IMenuItem
  }));
  nextTick(() => {
    calcCollapseHeight();
  }).then(() => {});
  handleStatisticsCategoriesCount();
}
onMounted(() => {
  request();
});
function handleGroupSaveAfter(groupId: string) {
  collapsed.value = false;
  console.log(groupId);
  request().then(() => {
    active.value = groupId;
  });
}
const active = defineModel<string>('active');


const groupStatistics = ref<{
  [key: string]: number;
}>({});


function handleStatisticsCategoriesCount() {
  groupData.value.list.map(async (item) => {
    await ExtensionManager.getTaskInstance().listTask({
      groupId: item.groupId
    }).then(res => {
      groupStatistics.value[item.groupId] = res.total;
      // item.data = res.list;
    });
  });
}

function handleDeleteGroup(group: IGroupInfo) {
  const dialogInstance = DialogPlugin.confirm({
    header: '删除分组提醒',
    cancelBtn: '放弃',
    body: () => (
      <div>
        <div>确定要删除「{group.groupName}」分组吗？</div>
        <div>
          <span style="color: #f5222d">分组内的任务彻底删除, </span>
          <span style="font-weight: bold; color: #f5222d">无法还原</span>
        </div>
      </div>
    ),
    confirmBtn: {
      content: '确认删除',
      theme: 'danger'
    },
    onConfirm: async () => {
      dialogInstance.update({
        confirmLoading: true,
      })
      await ExtensionManager.getGroupInstance().deleteGroup(group.groupId);
      await FlowExecutor.adjustFlowNodeData();
      await request();
      if (active.value === group.groupId) {
        // 当前是删除的分组
        active.value = 'toDay';
      }
      dialogInstance.hide();
      dialogInstance.destroy();
    }
  })
}
const groupBoxSaveDrawerRef = useTemplateRef<GroupBoxSaveDrawerInstance>('groupBoxSaveDrawerRef');
const [collapsed, handleCollapse, calcCollapseHeight] = useCollapse();
useEventListener(window, 'taskList::refreshAll', () => {
  if (!collapsed.value) {
    request();
  }
});
</script>

<template>
  <GroupBoxSaveDrawer ref="groupBoxSaveDrawerRef"
                      @saveAfter="handleGroupSaveAfter" />
  <!--  分组  -->
  <div class="sidebar-group">
    <div class="sidebar-section">
      <div class="section-title"
           @click="handleCollapse">
        <div class="u-fx u-fac gap-1 u-pos-rel" >
          <t-icon v-if="collapsed" class="i-u-right"></t-icon>
          <t-icon v-else class="i-u-down"></t-icon>
          <div>分组</div>
        </div>
        <div class="u-fx u-fac">
          <div @click.stop="groupBoxSaveDrawerRef?.show()">
            <t-icon class="i-u-plus text-lg"></t-icon>
          </div>
        </div>
      </div>
    </div>
    <div class="sidebar-section item"
         :class="{ collapsed }">
      <div  ref="collapseRef">
        <ContextMenu v-for="group in groupData.list"
                     :key="group.groupId"
                     size="small">
          <div class="sidebar-item"
               :class="{ active: active === group.groupId }"
               @click="active = group.groupId">
            <!--              <div class="icon">🐝</div>-->
            <div class="name">{{ group.groupName }}</div>
            <div class="count">{{ groupStatistics[group.groupId] || 0 }}</div>
          </div>
          <template #content>
            <ContextMenuItem icon="i-u-write"
                             label="编辑"
                             value=""
                             @click="groupBoxSaveDrawerRef?.show(group)">
            </ContextMenuItem>
            <t-divider style="margin: 4px 0;" />
            <ContextMenuItem icon="i-u-delete"
                             style="color: red"
                             label="删除"
                             theme="danger"
                             @click="handleDeleteGroup(group)">
            </ContextMenuItem>
          </template>
        </ContextMenu>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
