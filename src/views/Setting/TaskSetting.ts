import {useLocalData} from "@/hooks/dataHooks.ts";

const taskFinishSoundEffect = useLocalData<boolean>('taskSetting/finishSoundEffect', true);
const pluginHeight = useLocalData('taskSetting/pluginHeight', 700);
const guideDisable = useLocalData<boolean>('taskSetting/guideDisable', false);

export function useTaskSetting() {
  return {
    taskFinishSoundEffect,
    pluginHeight,
    guideDisable
  };
}
