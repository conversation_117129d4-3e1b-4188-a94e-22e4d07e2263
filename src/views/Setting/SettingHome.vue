<script setup lang="ts">
import { useTaskSetting } from './TaskSetting.ts'
import {
  PageHeader,
  SettingItem,
  SettingGroup,
  SwitchPlusEnable,
  SettingDivision,
} from '@xiaou66/u-web-ui'

const { taskFinishSoundEffect, pluginHeight, guideDisable } = useTaskSetting()

function handleUtoolsHeight() {
  utools.setExpendHeight(pluginHeight.value)
}
</script>

<template>
  <div>
    <PageHeader title="设置中心" subtitle="这里可以设置插件相关配置" size="small" />
    <div class="setting">
      <SettingGroup title="基本设置">
        <SettingItem title="任务完成音效" @click="taskFinishSoundEffect = !taskFinishSoundEffect">
          <template #title>
            <t-icon
              :class="taskFinishSoundEffect ? 'i-u-volume-notice' : 'i-u-volume-mute'"
              :style="{ color: taskFinishSoundEffect ? 'rgb(var(--green-5))' : '' }"
            ></t-icon>
            任务完成音效
          </template>
          <SwitchPlusEnable v-model:value="taskFinishSoundEffect" style="width: 48px" @click.stop />
        </SettingItem>
        <SettingDivision />
        <SettingItem title="引导显示" @click="guideDisable = !guideDisable">
          <template #title>
            <t-icon
              class="i-u-info"
              :style="{ color: !guideDisable ? 'rgb(var(--green-5))' : '' }"
            ></t-icon>
            引导显示
          </template>
          <SwitchPlusEnable
            :value="!guideDisable"
            @change="guideDisable = !guideDisable"
            style="width: 48px"
            @click.stop
          />
        </SettingItem>
        <SettingDivision />
        <SettingItem title="插件高度">
          <template #title>
            <t-icon
              class="i-u-height"
              :style="{ color: pluginHeight ? 'rgb(var(--green-5))' : '' }"
            />
            插件高度
          </template>
          <t-input-number
            v-model:value="pluginHeight"
            style="width: 90px"
            theme="column"
            :min="500"
            :max="1200"
            @change="handleUtoolsHeight()"
            hide-button
          />
          <template #extra>
            <div class="p-2">
              <t-slider
                v-model:value="pluginHeight"
                class="u-slider u-slider-no-fill u-slider-green"
                :min="500"
                :max="1200"
                @change="handleUtoolsHeight()"
              />
            </div>
          </template>
        </SettingItem>
      </SettingGroup>
    </div>
  </div>
</template>

<style scoped lang="less">
.setting {
  padding: 10px 32px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style>
