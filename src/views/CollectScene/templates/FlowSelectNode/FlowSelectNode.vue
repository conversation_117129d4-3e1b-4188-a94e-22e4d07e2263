<script setup lang="ts">
import { UContextMenu, type UContextMenuInstance } from '@/components/common';
import { ref, useTemplateRef, nextTick } from 'vue';
import { FLOW_NODE_GROUP, FLOW_NODE_MAP, SELECT_GROUP_SORT_LIST } from '@/views/CollectScene/flow/node';
import type { IFlowNodeConfig } from '@/views/CollectScene/flow/node/BaseFlowNode.ts';
import type { IFlowNode } from '@xiaou66/todo-plugin';
import { nanoid } from 'nanoid';
import {useFlowInject} from "@/views/CollectScene/flow/node/BaseFlowNode.ts";

const contextMenuRef = useTemplateRef<UContextMenuInstance>('contextMenuRef');

const nodeList = defineModel<IFlowNode[]>('nodeList', {
  default: () => [],
});

function handleOpenContextMenu(e: MouseEvent) {
  contextMenuRef.value?.show(e);
}

const { createNode } = useFlowInject();
</script>

<template>
  <u-context-menu ref="contextMenuRef">
    <div class="u-fx u-fc u-fac u-pointer flow-select-node" @click="handleOpenContextMenu">
      <iconpark-icon name="plus"></iconpark-icon>
    </div>
    <template #content>
      <div class="min-dropdown-select-options">
        <a-dsubmenu
          v-for="groupKey in SELECT_GROUP_SORT_LIST"
          :key="groupKey"
          trigger="hover"
        >
          <template #default>{{ groupKey }}</template>
          <template #content>
            <div
              v-for="flowNode in FLOW_NODE_GROUP[groupKey]!"
              :key="flowNode.info.code"
              class="min-dropdown-select min-dropdown-select-options"
              @click="createNode(flowNode)"
            >
              <a-doption>{{ flowNode.info.title }}</a-doption>
            </div>
          </template>
        </a-dsubmenu>
      </div>
    </template>
  </u-context-menu>
</template>

<style scoped lang="less">
.flow-select-node {
  width: 100%;
  line-height: 42px;
  border: 2px dashed #ccc;
  > iconpark-icon {
    font-size: 24px;
  }
}
</style>
