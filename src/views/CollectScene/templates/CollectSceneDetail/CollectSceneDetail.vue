<script setup lang="ts">
import FlowSelectNode from '../FlowSelectNode/FlowSelectNode.vue';
import {ref, watch, computed, nextTick} from 'vue';
import type { IFlowItem, SaveFlowItem } from '@xiaou66/todo-plugin';
import { FLOW_COMPONENT_CACHE, FLOW_NODE_ACTIONS_MAP, FLOW_NODE_MAP } from '@/views/CollectScene/flow/node';
import { flowProvide } from '@/views/CollectScene/flow/node/BaseFlowNode.ts';
import type { IFlowNodeConfig } from '@/views/CollectScene/flow/node/BaseFlowNode.ts';
import { cloneDeep, merge } from 'es-toolkit';
import { ExtensionManager } from '@/extension';
import { Message } from '@arco-design/web-vue';
import { nanoid } from 'nanoid';

const defaultValue: SaveFlowItem = {
  type: 'task',
  title: '',
  enable: false,
  nodeList: [{
    id: nanoid(),
    nodeCode: 'createTask',
    nodeData: {},
  }] as any,
}
const props = defineProps<{
  flowId: string;
}>();
const emits = defineEmits<{
  refresh: [],
}>();

const flowActiveIds = ref<string[]>([]);
const flowData = ref<SaveFlowItem>(cloneDeep<SaveFlowItem>(defaultValue));

watch(() => props.flowId, async (flowId) => {
  console.log(flowId, flowId);
  const flowItem = await ExtensionManager.getFlowInstance().getFlow(flowId) || {};
  flowData.value = cloneDeep<SaveFlowItem>(merge(cloneDeep<SaveFlowItem>(defaultValue), flowItem));
}, { deep: true });

function updateNode(id: string, nodeData: Record<string, any>) {
  const index = flowData.value.nodeList.findIndex((item) => item.id === id);
  if (index !== -1) {
    const oldData = flowData.value.nodeList[index];
    flowData.value.nodeList.splice(index, 1,
      { ...oldData, nodeData: { ...oldData.nodeData, ...nodeData} });
  }
}

async function saveFlowItem() {
  const { title } = flowData.value;
  if (!title) {
    Message.warning('请输入场景名称');
    return;
  }
  const flowItem = await ExtensionManager.getFlowInstance().saveFlow(flowData.value);
  if (flowItem.nodeList && flowItem.nodeList.length > 0) {
    for (const node of flowItem.nodeList) {
      const saveData =  FLOW_NODE_ACTIONS_MAP[node.nodeCode].onSaveData;
      if (saveData) {
        saveData(flowItem, node);
      }
    }
  }
  if (utools.isDev()) {
    utools.copyText(JSON.stringify(flowData.value))
  }
  Message.success('保存成功');
  emits('refresh');
}

flowProvide({
  createNode(node: IFlowNodeConfig) {
    console.log('handleCreateFlowNode', node);
    const newNode = {
      id: nanoid(),
      nodeCode: node.info.code,
      nodeData: {},
    };
    const nodeList = flowData.value.nodeList;
    const index = nodeList.findIndex(item => FLOW_NODE_MAP[item.nodeCode].info.end);
    if (index === -1) {
      // 使用不可变更新方式，避免直接修改原数组
      flowData.value.nodeList = [...nodeList, newNode];
    } else {
      flowData.value.nodeList.splice(index, 0, newNode);
    }
    // 使用 nextTick 确保 DOM 更新完成后再进行其他操作
    nextTick(() => {
      // 可以在这里添加一些后续操作，比如滚动到新添加的节点
      flowActiveIds.value.push(newNode.id);
    });
  },
  updateNodeField(id, field: string, value: any) {
    updateNode(id, { [field]: value })
  },
  updateNode,
  removeNode(id) {
    const index = flowData.value.nodeList.findIndex((item) => item.id === id);
    if (index !== -1) {
      const node = flowData.value.nodeList[index];
      if (flowData.value.id) {
        // 保存过才出发销毁方法, 清理之前保存的数据及指令
        FLOW_NODE_ACTIONS_MAP[node.nodeCode]?.onDestroy?.(flowData.value as IFlowItem, node);
      }
      flowData.value.nodeList.splice(index, 1);
    }
  },
});

const endNode = computed(() =>
  flowData.value.nodeList.find(item => FLOW_NODE_MAP[item.nodeCode].info.end));
</script>

<template>
  <div class="collect-scene-detail">
    <div class="u-fx u-fac u-f-between u-mb10">
      <div class="u-fx u-fac">
        <a-input v-model:model-value="flowData.title"
                 class="u-transparent"
                 size="small" placeholder="请输入场景名称"></a-input>
      </div>
      <div class="u-fx u-fac u-gap10">
        <div class="u-fx u-fac u-gap5">
          <div style="font-size: 13px">启用状态</div>
          <a-switch v-model:model-value="flowData.enable"
                    size="small"
                    checked-color="#52c41a" />
        </div>
        <a-button id =collect-scene-save
                  size="mini"
                  type="outline"
                  shape="round"
                  @click="saveFlowItem">
          <template #icon>
            <div class="u-fx u-fac">
              <iconpark-icon name="save-one"></iconpark-icon>
            </div>
          </template>
          保存
        </a-button>
      </div>
    </div>
    <a-collapse v-model:active-key="flowActiveIds">
      <a-scrollbar style="height: calc(100vh - 160px); overflow-y: auto">
        <div class="flow-node-wrapper">
          <div v-for="node in flowData.nodeList" :key="node.id">
            <Component
              v-if="node.id !== endNode?.id"
              :is="FLOW_COMPONENT_CACHE[node.nodeCode]"
              :node="node">
            </Component>
          </div>
          <FlowSelectNode v-model:node-list="flowData.nodeList" />
          <Component
            v-if="endNode"
            :is="FLOW_COMPONENT_CACHE[endNode.nodeCode]"
            :node="endNode">
          </Component>
        </div>
      </a-scrollbar>
    </a-collapse>
  </div>
</template>

<style scoped lang="less">
.collect-scene-detail {
  padding: 10px;
}
.flow-node-wrapper {
  display: grid;
  gap: 10px;
}
.component-loading {
  padding: 20px;
  text-align: center;
  color: var(--color-text-3);
  background: var(--color-fill-2);
  border-radius: 6px;
  border: 1px dashed var(--color-border);
}
</style>
