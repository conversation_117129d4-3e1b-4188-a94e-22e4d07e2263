import type { IFlowNodeActions, IFlowNodeConfig } from '../BaseFlowNode.ts';
import type { IFlowItem, IFlowNode } from '@xiaou66/todo-plugin';

export interface IUtoolsKeyNodeData {
  prefix?: string;
}

const CODE: string = "utoolsKey";
export const CONFIG: IFlowNodeConfig = {
  info: {
    code: CODE,
    title: 'utools 匹配指令',
    desc: '根据输入在 utools 主输入框文字进行匹配',
    group: '触发',
    nodeType: 'trigger'
  },
  nodeComponent: async () => import('./UtoolsKeyFlowNode.vue'),
}

function escapeRegExp(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义正则表达式中的特殊字符
}
function removeFeature(flow: IFlowItem, flowData: IFlowNode<IUtoolsKeyNodeData>) {
  debugger
  window.utools && utools.removeFeature(`function.flowExecutor?flowId=${flow.id}&nodeId=${flowData.id}`);
}
function utoolsSaveAfter(flow: IFlowItem, flowData: IFlowNode<IUtoolsKeyNodeData>) {
  removeFeature(flow, flowData);
  if (!flow.enable) {
    // 停用
    return;
  }
  const feature = {
    code: `function.flowExecutor?flowId=${flow.id}&nodeId=${flowData.id}`,
    explain: '优秀待办收集',
    cmds: [] as any
  }
  if (flowData.nodeData.prefix) {
    feature.cmds.push({
      type: 'regex',
      label: flow.title,
      match: `/^${escapeRegExp(flowData.nodeData.prefix)}.*/i`,
    } as any);
  } else {
    feature.cmds.push({
      type: 'over',
      label: flow.title,
      minLength: 1,
    } as any);
  }
  utools.setFeature(feature);
}

function removePrefix(str: string, prefix: string) {
  // 检查字符串是否以指定前缀开头
  if (str.startsWith(prefix)) {
    // 使用 slice 去掉前缀
    return str.slice(prefix.length);
  }
  // 如果没有前缀，返回原字符串
  return str;
}
export const ACTIONS: IFlowNodeActions<IUtoolsKeyNodeData> = {
  code: CODE,
  onSaveData(flow ,flowData) {
    window.utools && utoolsSaveAfter(flow, flowData);
  },

  async execute(flow, flowData, data) {
    data.triggerContext = {};
    if (data.originTriggerContext) {
      const originTriggerContext =  data.originTriggerContext;
      if (originTriggerContext.type === 'over') {
        data.triggerContext.textContent = originTriggerContext.payload;
      } else if (originTriggerContext.type === 'regex') {
        data.triggerContext.textContent = removePrefix(originTriggerContext.payload, flowData.nodeData.prefix || '')
      }
    }
  },

  onDestroy(flow ,flowData) {
    removeFeature(flow, flowData);
  },
}
