<script setup lang="ts">
import BaseFlowNode from '../BaseFlowNode.vue';
import { CONFIG } from './UtoolsKeyFlowNode.ts';
import type { IUtoolsKeyNodeData } from './UtoolsKeyFlowNode.ts';
import type { IFlowNode } from '@xiaou66/todo-plugin';
import { useFlowInject } from '../BaseFlowNode.ts';

const props = defineProps<{
  node: IFlowNode<IUtoolsKeyNodeData>;
}>();
const flowProvide = useFlowInject();
</script>

<template>
  <BaseFlowNode v-bind="CONFIG.info"
                :id="node.id">
    <a-input v-model:model-value="node.nodeData.prefix"
             @change="(value: string) => flowProvide.updateNodeField(node.id, 'prefix', value)"
             size="small"
             placeholder="任意文本">
      <template #prefix>前缀</template>
    </a-input>
  </BaseFlowNode>
</template>

<style scoped lang="less">

</style>
