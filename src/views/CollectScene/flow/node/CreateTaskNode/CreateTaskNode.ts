import type { IFlowNodeActions, IFlowNodeConfig } from '../BaseFlowNode.ts';
import type { TaskSaveParams } from '@xiaou66/todo-plugin'
import { cloneDeep, merge } from 'es-toolkit';
import { ExtensionManager } from '@/extension';
import { useTaskDetailDrawer } from '@/components/Tasks';
const CODE: string = "createTask";

export interface ICreateTaskData {
  saveAfterAction: 'openTask' | 'outPlugin' | 'none';
}
export const CONFIG: IFlowNodeConfig = {
  info: {
    code: CODE,
    title: '创建任务',
    desc: '将任务创建出来',
    group: '完成',
    nodeType: 'collectTask',
    end: true,
  },
  nodeComponent: async () => import('./CreateTaskNode.vue'),
}

const DEFAULT: TaskSaveParams = {
  taskLevel: '2',
  taskGroupId: 'collectBox',
  taskTitle: '',
  taskDateType: 'date'
}
export const ACTIONS: IFlowNodeActions<ICreateTaskData> = {
  code: CODE,
  async execute(flow, flowData, data) {
    debugger
    const taskSaveParams: TaskSaveParams = merge(cloneDeep<TaskSaveParams>(DEFAULT), data.context || {});
    if (data.triggerContext && data.triggerContext.textContent) {
      taskSaveParams.taskTitle = data.triggerContext.textContent;
    }
    const taskItem = await ExtensionManager.getTaskInstance()
      .saveTask(taskSaveParams.taskGroupId! || 'collectBox', taskSaveParams);
    const { saveAfterAction = 'openTask'} = flowData.nodeData;
    if (saveAfterAction === 'none') {
      window.dispatchEvent(new CustomEvent('taskList::refreshAll'));
    } else if (saveAfterAction === 'openTask') {
      setTimeout(() => {
        useTaskDetailDrawer().show(taskItem);
      }, 200);
    }
  }
}
