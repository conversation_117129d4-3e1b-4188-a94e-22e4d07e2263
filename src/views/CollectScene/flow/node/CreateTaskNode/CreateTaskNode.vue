<script setup lang="ts">
import BaseFlowNode from '../BaseFlowNode.vue';
import { CONFIG } from './CreateTaskNode';
import type { IFlowNode } from '@xiaou66/todo-plugin';
import type { ICreateTaskData } from './CreateTaskNode.ts';

const props = defineProps<{
  node: IFlowNode<ICreateTaskData>;
}>();
</script>

<template>
  <BaseFlowNode v-bind="CONFIG.info"
                :id="node.id"
                :disableDelete="true">
    <div class="u-fx u-fac u-gap5">
      <div>创建完后</div>
      <a-radio-group default-value="openTask"
                     v-model:model-value="node.nodeData.saveAfterAction">
        <a-radio value="NONE">什么都不做</a-radio>
        <a-radio value="openTask">打开任务</a-radio>
      </a-radio-group>
    </div>
  </BaseFlowNode>
</template>

<style scoped lang="less">

</style>
