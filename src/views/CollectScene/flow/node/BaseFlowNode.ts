import { type AsyncComponentLoader, inject } from 'vue';
import  { provide } from 'vue';
import type { ITaskItem, IFlowNode, IFlowItem } from '@xiaou66/todo-plugin';

export interface IFlowNodeInfo {
  code: string;
  title: string;
  group: '触发' | '标记' | '完成';
  nodeType: 'trigger' | 'collectTask';
  end?: boolean;
  desc?: string;
}

export interface IFlowNodeConfig{
  info: IFlowNodeInfo;
  nodeComponent: AsyncComponentLoader;
}

export interface IFlowContext {
  triggerNodeId: string;
  originTriggerContext?: Record<string, any>;
  triggerContext?: {
    textContent?: string;
  }
  context?: Record<string, any>;
}
export interface IFlowNodeActions<T> {
  code: string;
  execute?: (flow: IFlowItem, flowData: IFlowNode<T>, data: IFlowContext) => Promise<void>;
  onSaveData?: (flow: IFlowItem, flowData: IFlowNode<T>) => void;
  onDestroy?: (flow: IFlowItem, flowData: IFlowNode<T>) => void;
  adjustData?: (flow: IFlowItem, flowData: IFlowNode<T>) => Promise<boolean>;
}

export interface FlowProvide {
  removeNode: (id: string) => void;
  updateNodeField: (id: string, field: string, value: any) => void;
  updateNode: (id: string, nodeData: Record<string, any>) => void;
  createNode: (node: IFlowNodeConfig) => void;
}


const FLOW_PROVIDE_CODE = Symbol('flowProvide');


export function flowProvide(provideContent: FlowProvide) {
  provide<FlowProvide>(FLOW_PROVIDE_CODE, provideContent);
}

export function useFlowInject(): FlowProvide {
  return inject<FlowProvide>(FLOW_PROVIDE_CODE)!;
}
