<script setup lang="ts">
import BaseFlowNode from '@/views/CollectScene/flow/node/BaseFlowNode.vue';
import { onMounted, ref } from 'vue';
import type { ITaskTag, IGroupInfo, IFlowNode } from "@xiaou66/todo-plugin";
import { ExtensionManager } from '@/extension';
import TaskConstants from '@/constants/tasks/TaskConstants.ts';
import { CONFIG } from './ManualMarkerTask.ts';
import type {  IFlowNodeData } from './ManualMarkerTask.ts';
import { useFlowInject } from '@/views/CollectScene/flow/node/BaseFlowNode.ts';

const props = defineProps<{
  node: IFlowNode<IFlowNodeData>;
}>();
const taskTagList = ref<ITaskTag[]>([]);
const groupList = ref<IGroupInfo[]>([]);

onMounted(() => {
  ExtensionManager.getGroupInstance()
    .listGroup({})
    .then((res) => {
      groupList.value = res.list;
    });

  ExtensionManager.getTaskTagInstance()
    .listTag()
    .then((res) => {
      taskTagList.value = res;
    });
});
const { updateNodeField } = useFlowInject();
</script>

<template>
  <BaseFlowNode v-bind="CONFIG.info"
                :id="node.id">
    <a-select v-model:model-value="node.nodeData.taskGroupId"
              size="small"
              allow-clear
              @change="(value: string) => updateNodeField(node.id, 'taskGroupId', value)">
      <template #prefix>分组</template>
      <a-option value="collectBox">未分组</a-option>
      <a-option v-for="group in groupList"
                :key="group.groupId"
                :value="group.groupId">
        {{ group.groupName }}
      </a-option>
    </a-select>
    <a-select v-model:model-value="node.nodeData.tagNameList"
              class="u-mt10"
              size="small"
              multiple
              allow-clear
              @change="(value: string) => updateNodeField(node.id, 'tagNameList', value)">
      <template #prefix>标签</template>
      <a-option v-for="taskTag in taskTagList"
                :key="taskTag.id"
                :value="taskTag.name">
        {{ taskTag.name }}
      </a-option>
    </a-select>
    <div class="u-mt10 u-fx u-fac u-gap10">
      <div style="width: 50px;">时间</div>
      <a-radio-group
        default-value="''"
        :model-value="node.nodeData.timeDiffType"
        @change="(value: string) => updateNodeField(node.id, 'timeDiffType', value)">
        <a-radio value="''" style="width: 68px;">
          不设置
        </a-radio>
        <a-radio value="toDay" style="width: 55px;">今天</a-radio>
        <a-radio value="tomorrow" style="width: 55px;">明天</a-radio>
        <a-radio value="afterTomorrow" style="width: 55px;">后天</a-radio>
      </a-radio-group>
    </div>
    <div class="u-mt10 u-fx u-fac u-gap10">
      <div style="width: 50px;">优先级</div>
      <a-radio-group :default-value="'2'"
                     v-model:model-value="node.nodeData.taskLevel"
                     @change="(value: string) => updateNodeField(node.id, 'taskLevel', value)">
        <a-radio v-for="(item) in TaskConstants.PRIORITY_SELECT_OPTIONS_TAG"
                    :key="item.value"
                    :value="item.value">
          <div class="u-fx u-fac u-gap5">
            <iconpark-icon name="mark" :style="{ color: item.color }"></iconpark-icon>
            <div :style="{ color: item.tagColor }">{{ item.label }}</div>
          </div>
        </a-radio>
      </a-radio-group>
    </div>
  </BaseFlowNode>
</template>

<style scoped lang="less">

</style>
