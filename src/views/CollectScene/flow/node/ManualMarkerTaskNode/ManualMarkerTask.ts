import type { IFlowNodeActions, IFlowNodeConfig } from '../BaseFlowNode.ts';
import { merge, pickBy } from 'es-toolkit';
import { isNumber } from '@/utils/jsUtil.ts';
import dayjs from 'dayjs';
import { ExtensionManager } from '@/extension';


export interface IFlowNodeData {
  taskGroupId?: string;
  tagNameList?: string[];
  taskLevel?: string;
  timeDiffType?: string;
}

const CODE: string = "manualMarkerTask";
export const CONFIG: IFlowNodeConfig = {
  info: {
    code: CODE,
    title: '手动标记',
    desc: '根据设置的内容对创建的任务进行标记',
    group: '标记',
    nodeType: 'collectTask',
  },
  nodeComponent: async () => import('./ManualMarkerTask.vue'),
}


export const ACTIONS: IFlowNodeActions<IFlowNodeData> = {
  code: CODE,
  async execute(flow, flowData, data) {
    debugger;
    const task = data.context || {};
    const { nodeData } = flowData;
    data.context = merge(task, pickBy(nodeData,
      (value, key) => {
        if (key === 'timeDiffType') {
          return false;
        }
        if (isNumber(value)) {
          return value !== undefined && value !== null;
        }
        return !!value;
      }));
    if (nodeData.timeDiffType) {
      switch (nodeData.timeDiffType) {
        case 'toDay':
          data.context.taskStartDate = dayjs().format('YYYY-MM-DD');
          break;
        case 'tomorrow':
          data.context.taskStartDate = dayjs().add(1, 'day').format('YYYY-MM-DD');
          break;
        case 'afterTomorrow':
          data.context.taskStartDate = dayjs().add(2, 'day').format('YYYY-MM-DD');
          break;
      }
    }

  },
  async adjustData(flow, flowData) {
    let result = false;
    if (flowData.nodeData.taskGroupId) {
      const gripId = await ExtensionManager.getGroupInstance().getGroup(flowData.nodeData.taskGroupId);
      if (!gripId) {
        delete flowData.nodeData.taskGroupId;
        result = true;
      }
    }

    if (flowData.nodeData.tagNameList && flowData.nodeData.tagNameList.length > 0) {
      const taskTags = await ExtensionManager.getTaskTagInstance().listTag();
      const taskTagNameList = taskTags.map(tag => tag.name);
      const existTagNameList = flowData.nodeData.tagNameList
        .filter((tagName: string) => taskTagNameList.includes(tagName));
      if (existTagNameList.length !== flowData.nodeData.tagNameList.length) {
        flowData.nodeData.tagNameList = existTagNameList;
        result = true;
      }
    }
    return result;
  }
}
