import { ExtensionManager } from '@/extension';
import type { IFlowContext } from './node/BaseFlowNode.ts';
import { FLOW_NODE_ACTIONS_MAP, FLOW_NODE_MAP } from '@/views/CollectScene/flow/node';

export class FlowExecutor {

  public static async executor(flowId: string, initContext: IFlowContext) {
    const flow = await ExtensionManager.getFlowInstance().getFlow(flowId);
    for (const node of flow.nodeList) {
      if (FLOW_NODE_MAP[node.nodeCode].info.nodeType === 'trigger'
        && node.id !== initContext.triggerNodeId) {
        // 触发器只有当前触发源才进行执行其他的不执行
        continue;
      }
      const execute = FLOW_NODE_ACTIONS_MAP[node.nodeCode].execute;
      if (execute) {
        await execute(flow, node, initContext);
      }
    }
  }
  public static async adjustFlowNodeData() {
    debugger;
    const flowItems = await ExtensionManager.getFlowInstance().getFlowList();
    for (const flowItem of flowItems) {

      const all = flowItem.nodeList.map(node => {
        const adjustData = FLOW_NODE_ACTIONS_MAP[node.nodeCode].adjustData;
        if (adjustData) {
          return adjustData(flowItem, node);
        }
        return null;
      }).filter(item => item);
      const res = await Promise.all(all);
      if (res.filter(item => item).length > 0) {
        await ExtensionManager.getFlowInstance().saveFlow(flowItem);
      }
    }
  }
}
