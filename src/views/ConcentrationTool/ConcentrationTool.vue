<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import dayjs from 'dayjs'

/**
 * 专注记录接口定义
 */
interface FocusRecord {
  id: string
  startTime: string
  endTime: string
  duration: number // 持续时间（分钟）
}

/**
 * 状态枚举：空闲、专注中
 */
enum TimerStatus {
  IDLE = 'idle',
  FOCUSING = 'focusing',
}

// 计时器状态
const timerStatus = ref<TimerStatus>(TimerStatus.IDLE)
// 计时器剩余时间（秒）
const remainingTime = ref<number>(1 * 60) // 默认25分钟
// 计时器间隔ID
let timerInterval: number | null = null
// 专注记录列表
const focusRecords = ref<FocusRecord[]>([])
// 当前专注开始时间
const currentFocusStartTime = ref<string>('')

// 今日专注次数
const todayFocusCount = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  return focusRecords.value.filter(
    (record) => dayjs(record.startTime).format('YYYY-MM-DD') === today,
  ).length
})

// 今日专注时长（分钟）
const todayFocusDuration = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  return focusRecords.value
    .filter((record) => dayjs(record.startTime).format('YYYY-MM-DD') === today)
    .reduce((total, record) => total + record.duration, 0)
})

// 总专注次数
const totalFocusCount = computed(() => focusRecords.value.length)

// 总专注时长（分钟）
const totalFocusDuration = computed(() =>
  focusRecords.value.reduce((total, record) => total + record.duration, 0),
)

/**
 * 格式化时间显示（mm:ss）
 */
const formattedTime = computed(() => {
  const minutes = Math.floor(remainingTime.value / 60)
  const seconds = remainingTime.value % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

/**
 * 计算进度条百分比
 */
const progressPercentage = computed(() => {
  const totalSeconds = 1 * 60 // 总时间（秒）
  const elapsedSeconds = totalSeconds - remainingTime.value
  console.log((elapsedSeconds / totalSeconds) * 100)
  return (elapsedSeconds / totalSeconds) * 100
})

/**
 * 计算进度条样式
 */
const progressStyle = computed(() => {
  const percentage = progressPercentage.value
  const radius = 138
  const circumference = 2 * Math.PI * radius // 圆的周长 (r=138)
  const dashoffset = circumference * ((100 - percentage) / 100)
  console.log(dashoffset, circumference)

  return {
    strokeDashoffset: `${dashoffset}px`,
    strokeDasharray: `${circumference}px`,
  }
})

/**
 * 开始专注计时
 */
const startTimer = () => {
  if (timerStatus.value === TimerStatus.FOCUSING) return

  timerStatus.value = TimerStatus.FOCUSING
  currentFocusStartTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')

  timerInterval = window.setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    } else {
      completeTimer()
    }
  }, 100)
}

/**
 * 完成专注计时
 */
const completeTimer = () => {
  if (timerStatus.value !== TimerStatus.FOCUSING) return

  clearTimerInterval()
  timerStatus.value = TimerStatus.IDLE

  // 确保时间显示为0
  remainingTime.value = 0

  const endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
  const startTime = currentFocusStartTime.value
  const duration = dayjs(endTime).diff(dayjs(startTime), 'minute')

  // 添加专注记录
  focusRecords.value.push({
    id: Date.now().toString(),
    startTime,
    endTime,
    duration,
  })

  // 保存记录到本地存储
  saveRecords()

  // 延迟重置计时器，让用户看到完成状态
  setTimeout(() => {
    remainingTime.value = 25 * 60
  }, 1000)
}

/**
 * 停止计时器
 */
const stopTimer = () => {
  if (timerStatus.value !== TimerStatus.FOCUSING) return

  completeTimer()
}

/**
 * 清除计时器间隔
 */
const clearTimerInterval = () => {
  if (timerInterval) {
    clearInterval(timerInterval)
    timerInterval = null
  }
}

/**
 * 保存记录到本地存储
 */
const saveRecords = () => {
  localStorage.setItem('focusRecords', JSON.stringify(focusRecords.value))
}

/**
 * 从本地存储加载记录
 */
const loadRecords = () => {
  const savedRecords = localStorage.getItem('focusRecords')
  if (savedRecords) {
    focusRecords.value = JSON.parse(savedRecords)
  }
}

/**
 * 格式化日期显示
 */
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY年M月D日')
}

/**
 * 格式化时间范围显示
 */
const formatTimeRange = (startTime: string, endTime: string) => {
  return `${dayjs(startTime).format('HH:mm')} - ${dayjs(endTime).format('HH:mm')}`
}

/**
 * 组件挂载时加载记录
 */
onMounted(() => {
  loadRecords()
})

/**
 * 组件卸载时清除计时器
 */
onBeforeUnmount(() => {
  clearTimerInterval()
})
</script>

<template>
  <div class="concentration-tool">
    <!-- 主要内容区 -->
    <div class="main-content">
      <!-- 左侧计时器 -->
      <div class="timer-section">
        <div></div>
        <div class="left-header u-fx u-fac u-gap5">
          <div class="title">专注</div>
        </div>

        <!-- 计时器 -->
        <div class="timer-container">
          <div class="timer-circle">
            <svg class="progress-ring" width="280" height="280">
              <circle
                class="progress-ring-circle-bg"
                stroke="#eee"
                stroke-width="4"
                fill="transparent"
                r="138"
                cx="140"
                cy="140"
              />
              <circle
                class="progress-ring-circle"
                stroke="#4080ff"
                stroke-width="4"
                fill="transparent"
                r="138"
                cx="140"
                cy="140"
                :style="progressStyle"
              />
            </svg>
            <div class="timer-display">{{ formattedTime }}</div>
          </div>

          <!-- 开始按钮 -->
          <button
            class="start-btn"
            @click="timerStatus === TimerStatus.IDLE ? startTimer() : stopTimer()"
          >
            {{ timerStatus === TimerStatus.IDLE ? '开始' : '停止' }}
          </button>
        </div>
      </div>

      <!-- 右侧统计信息 -->
      <div class="stats-section">
        <div style="padding: 10px">
          <div class="right-header u-fx u-fac u-gap5">
            <div class="title">概览</div>
          </div>
          <!-- 今日统计 -->
          <div class="stats-row">
            <div class="stat-card">
              <div class="stat-label">今日番茄</div>
              <div class="stat-value">{{ todayFocusCount }}</div>
            </div>

            <div class="stat-card">
              <div class="stat-label">今日专注时长</div>
              <div class="stat-value">{{ todayFocusDuration }} <span class="unit">m</span></div>
            </div>
          </div>
          <!-- 总计统计 -->
          <div class="stats-row">
            <div class="stat-card">
              <div class="stat-label">总番茄</div>
              <div class="stat-value">{{ totalFocusCount }}</div>
            </div>

            <div class="stat-card">
              <div class="stat-label">总专注时长</div>
              <div class="stat-value">{{ totalFocusDuration }} <span class="unit">m</span></div>
            </div>
          </div>
        </div>
        <!-- 专注记录 -->

        <div class="records-section">
          <div class="records-header">
            <div class="records-title">专注记录</div>
            <div class="add-record-btn">
              <a-button size="mini">
                <template #icon>
                  <iconpark-icon size="18" name="plus"></iconpark-icon>
                </template>
              </a-button>
            </div>
          </div>
          <a-scrollbar style="height: 100%; overflow: auto">
            <!-- 记录列表 -->
            <div class="records-list">
              <div class="month">2025-03</div>
              <template v-for="(record, index) in focusRecords" :key="record.id">
                <!-- 日期分组 -->
                <a-timeline>
                  <a-timeline-item>
                    <div class="u-fx u-gap5 u-font-size-smail">
                      <div>{{ formatTimeRange(record.startTime, record.endTime) }}</div>
                      <div>{{ record.duration }} 分钟</div>
                    </div>
                    <template #label>
                      <div class="title">121312321321367617</div>
                      <div class="remark">1112132131232131313213213123123123213213213213213</div>
                    </template>
                  </a-timeline-item>
                </a-timeline>
              </template>
            </div>
          </a-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.concentration-tool {
  height: 100%;
  background-color: #f5f5f5;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  // 主要内容区
  .main-content {
    display: flex;
    flex: 1;
    height: 100%;
    .right-header,
    .left-header {
      .title {
        font-size: 16px;
        font-weight: bold;
      }
    }

    // 左侧计时器
    .timer-section {
      flex: 1;
      padding: 20px;
      display: flex;
      flex-direction: column;

      .timer-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 1;

        .timer-circle {
          width: 280px;
          height: 280px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 40px;
          position: relative;

          .progress-ring {
            position: absolute;
            top: 0;
            left: 0;
            transform: rotate(-90deg);

            .progress-ring-circle {
              transition: stroke-dashoffset 0.5s;
            }
          }

          .timer-display {
            cursor: pointer;
            font-size: 48px;
            font-weight: bold;
            z-index: 1;
          }
        }

        .start-btn {
          background-color: #4080ff;
          color: white;
          border: none;
          border-radius: 20px;
          padding: 8px 40px;
          font-size: 16px;
          cursor: pointer;
          transition: background-color 0.3s;

          &:hover {
            background-color: #3070ee;
          }
        }
      }
    }

    // 右侧统计信息
    .right-header {
      padding: 10px 0;
    }
    .stats-section {
      width: 320px;
      height: 100%;
      background-color: #fff;
      border-left: 1px solid #eee;
      display: grid;
      .stats-row {
        display: flex;
        margin-bottom: 16px;
        .stat-card {
          flex: 1;
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 8px;
          margin-right: 8px;
          box-sizing: border-box;

          &:last-child {
            margin-right: 0;
          }

          .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
          }

          .stat-value {
            font-size: 24px;
            font-weight: bold;

            .unit {
              font-size: 14px;
              font-weight: normal;
            }
          }
        }
      }

      // 专注记录
      .records-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        width: calc(100vw - 490px);
        .records-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 10px;
          .records-title {
            font-size: 16px;
            font-weight: bold;
          }

          .add-record-btn {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
          }
        }

        .records-list {
          flex: 1;
          max-height: 210px;
          padding: 0 10px 0;
          .month {
            font-size: 12px;
            font-weight: 700;
            padding: 4px 8px;
          }
          .title {
            font-weight: 700;
            color: var(--text-color);
          }
          .remark {
            overflow: hidden;
            text-overflow: ellipsis; //溢出用省略号显示
            white-space: nowrap; //溢出不换行
          }
          .date-group {
            font-size: 14px;
            color: #666;
            margin: 16px 0 8px;
          }
          .record-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            :global(.arco-timeline-item-content-wrapper) {
              cursor: pointer;
              padding: 4px;
              transition: background-color 300ms linear;
              border-radius: 4px;
            }
            :global(.arco-timeline-item-content-wrapper:hover) {
              background-color: var(--select-hover);
            }
            .record-icon {
              margin-right: 8px;
            }

            .record-time {
              flex: 1;
              font-size: 14px;
            }

            .record-duration {
              font-size: 14px;
              color: #666;
            }
          }
        }
      }
    }
  }
}
</style>
