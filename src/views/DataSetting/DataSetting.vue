<script lang="tsx" setup>
import { onMounted, ref } from 'vue';
import DataSettingConfig from "@/views/DataSetting/DataSettingConfig.ts";
import FileUtils from "@/utils/FileUtils.ts";
import dayjs from "dayjs";
import { PageHeader } from '@xiaou66/u-web-ui';
import type { TableProps } from 'tdesign-vue-next'
import  { Popconfirm, MessagePlugin, NotifyPlugin, DialogPlugin } from 'tdesign-vue-next'
const { localAppDataPath } = DataSettingConfig;
function handlerOpenLocalAppDataPath() {
  FileUtils.createDirIfAbsent(localAppDataPath.value);
  utools.shellShowItemInFolder(localAppDataPath.value);
}


// 删除插件所有数据
function handleDeleteAllPluginData() {
  const dialogInstance = DialogPlugin.confirm({
    header: '二次确认',
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    cancelBtn: '取消',
    body: '确认要删除插件的目录和插件内所有的数据',
    onConfirm() {
      utools.db.allDocs()
        .map(({_id}) => utools.dbStorage.removeItem(_id))
      window.fs.rmSync(localAppDataPath.value, {
        recursive: true,
        force: true
      });
      utools.getFeatures()
        .map(({ code }) => utools.removeFeature(code));
      dialogInstance.hide();
      NotifyPlugin.success({
        title: '提示',
        content: '清理成功, 需要完全退出插件后再进入即可',
        duration: 2000
      });
      dialogInstance.destroy();
      // @ts-ignore
      utools.outPlugin(true);
    }
  })
}

// 删除临时数据文件夹
function handleDeleteTempData() {
  const dialogInstance = DialogPlugin.confirm({
    header: '二次确认',
    confirmBtn: {
      content: '删除',
      theme: 'danger',
    },
    cancelBtn: '取消',
    body: '确认要清除插件数据不包括备份?',
    onConfirm() {
      utools.db.allDocs()
        .map(item => utools.dbStorage.removeItem(item._id));
      NotifyPlugin.success({
        title: '提示',
        content: '清理成功',
        duration: 2000
      });
      dialogInstance.hide();
      dialogInstance.destroy();
    }
  })
}

function backUpData() {
  const dataList = utools.db.allDocs().map(({value, _id}) => ({ _id, value }));
  JSON.stringify(dataList);
  const bakPath = window.path.join(localAppDataPath.value, 'backups');
  FileUtils.createDirIfAbsent(bakPath);
  const {VITE_APP_VERSION, VITE_ENV_NAME} = import.meta.env;
  const fileName = `备份#${VITE_ENV_NAME}#${dayjs().format("YYYY-MM-DD HH_mm_ss")}#manual#${VITE_APP_VERSION}.bak`;
  window.fs.writeFileSync(window.path.join(bakPath, fileName), JSON.stringify(dataList));
  refreshBackupsList();
  MessagePlugin.success('备份完成');
}
interface IBackupsItem {
  pluginVersion: string,
  pluginType: string,
  backupsType: 'manual' | 'auto',
  time: string,
  backupsFileName: string
}
const backupsList = ref<IBackupsItem[]>([])

function refreshBackupsList() {
  const bakPath = window.path.join(localAppDataPath.value, 'backups')
  FileUtils.createDirIfAbsent(bakPath);
  const backupsFileNameList = window.fs.readdirSync(window.path.join(bakPath))
    .filter(item => item.startsWith("备份") && item.endsWith('.bak'));
  backupsList.value = backupsFileNameList.map(backupsFileName => {
    const [name, pluginType, time, backupsType, pluginVersion] = backupsFileName.replace(window.path.extname(backupsFileName), '')
      .split("#");
    return {
      pluginType,
      pluginVersion: 'v' + pluginVersion.toString().replace(/_/g, '.'),
      backupsType: backupsType as 'manual' | 'auto',
      time: time.toString().replace(/_/g, ':'),
      backupsFileName
    }
  }).sort((a, b) => dayjs(a.time).isAfter(dayjs(b.time)) ? -1 : 1)
}
onMounted(() => {
  refreshBackupsList();
});

function restoreBackup(backups: IBackupsItem) {
  const bakPath = window.path.join(localAppDataPath.value, 'backups')
  const jsonStr = window.fs.readFileSync(window.path.join(bakPath, backups.backupsFileName), 'utf-8');
  utools.db.allDocs()
    .map(item => utools.dbStorage.removeItem(item._id));
  utools.db.bulkDocs(JSON.parse(jsonStr));
  MessagePlugin.success('还原备份成功');
}

function deleteBackUp(backups: IBackupsItem) {
  const bakPath = window.path.join(localAppDataPath.value, 'backups')
  window.fs.rmSync(window.path.join(bakPath, backups.backupsFileName));
  MessagePlugin.success('删除备份成功');
  refreshBackupsList();
}

const columns: TableProps['columns'] = [
  {
    colKey: 'time',
    title: '时间',
  },
  {
    colKey: 'pluginType',
    title: '插件类型',
    width: 150,
    align: 'center',
  },
  {
    colKey: 'pluginVersion',
    title: '插件版本',
    width: 100,
    align: 'center',
  },
  {
    colKey: 'backupsType',
    title: '备份类型',
    width: 100,
    align: 'center',
    cell: (h, { row }) => {
      return (
        <div>
          {
            row.backupsType === 'manual'
            ? (<a-tag color="blue" size="small">手动</a-tag>)
            : (<a-tag v-else  color="green" size="small">自动</a-tag>)
          }
        </div>
      )
    }
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 100,
    cell: (h, { row }) => {
      return (
        <div class="flex gap-2">
          <Popconfirm content="确认要删除这个备份"
                      onConfirm={() => deleteBackUp(row)}>
            <t-link class="text-sm" theme="danger">删除</t-link>
          </Popconfirm>
          <Popconfirm content="确认要还原这个备份"
                      onConfirm={() => restoreBackup(row)}>
            <t-link class="text-sm" theme="primary">还原</t-link>
          </Popconfirm>
        </div>
      )
    }
  },
]
</script>
<template>
  <div class="u-main-content">
    <PageHeader title="数据管理"
                subtitle="这里可以管理插件数据"
                size="small" />
    <div class="u-gap10 u-radius10 mt-5"
         style="padding: 14px; flex-direction: column; background: var(--color-bg-3);">
      <div class="u-fx u-fac u-f-between u-mb12">
        <div class="u-bold">插件数据目录</div>
        <div class="u-fx u-fac u-gap10">
          <t-link size="small"
                  theme="primary"
                  @click="handlerOpenLocalAppDataPath">
            <template #prefixIcon>
              <div class="w-4 h-4 i-u-folder-open" />
            </template>
            打开
          </t-link>
        </div>
      </div>
      <div>
        <t-input :model-value="localAppDataPath"
                 readonly />
      </div>
      <div class="u-tips u-mt10">存储插件额外需要支持库及运行时部分缓存, 无需清理插件会定期清理</div>
    </div>
    <div class="u-radius10" style="background: var(--color-bg-3); margin-top: 20px; padding: 10px">
      <div class="u-fx" style="justify-content: flex-end; padding: 0px 0px 10px">
        <t-input-group class="rounded-lg overflow-hidden">
          <t-button theme="default"
                    @click="backUpData">
            <template #icon>
              <t-icon class="i-u-history"></t-icon>
            </template>
            备份
          </t-button>
          <t-button theme="default"
                    @click="handleDeleteTempData">
            <template #icon>
              <t-icon class="i-u-delete"></t-icon>
            </template>
            清理插件数据
          </t-button>
          <t-button theme="danger"
                    @click="handleDeleteAllPluginData">
            <template #icon>
              <t-icon class="i-u-delete"></t-icon>
            </template>
            清理全部数据
          </t-button>
        </t-input-group>
      </div>

      <t-table :columns="columns"
               :data="backupsList" maxHeight="300">
      </t-table>
    </div>
  </div>
</template>
<style lang="less" scoped>
.tips {
  margin-bottom: 16px;
}
:deep(.arco-table-tr:last-child .arco-table-td) {
  border-bottom: none;
}
:deep(.arco-table-body) {
  min-height: 32px;
}
</style>
