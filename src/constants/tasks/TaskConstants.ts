import { keyBy } from 'es-toolkit';
export interface IPriorityOption {
  label: string;
  title: string;
  value: string;
  tagColor: string;
  color: string;
}
const PRIORITY_SELECT_OPTIONS_TAG: IPriorityOption[] = [
  { label: '紧急', title: '重要且紧急', value: '0', tagColor: 'red', color: '#f53f3f' },
  { label: '高', title: '重要不紧急', value: '1', tagColor: 'orangered', color: '#f97235' },
  { label: '中', title: '不重要但紧急', value: '2', tagColor: 'blue', color: '#3491fa' },
  { label: '低', title: '不重要不紧急', value: '3', tagColor: 'gray', color: '#88909c' },
]

const PRIORITY_SELECT_OPTIONS_TAG_MAP: Record<string, IPriorityOption> = keyBy(PRIORITY_SELECT_OPTIONS_TAG,
  (item) => item.value as string);

export default {
  PRIORITY_SELECT_OPTIONS_TAG,
  PRIORITY_SELECT_OPTIONS_TAG_MAP
}
