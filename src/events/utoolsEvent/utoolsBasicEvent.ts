import { addUtoolsEventListener } from '@/events'
import { FlowExecutor } from '@/views/CollectScene/flow';


/**
 * 基本事件路由跳转
 */
addUtoolsEventListener('ui.router', (e) => {
  e.router.replace({ name: e.getPara<PERSON>ey('router') }).then(() => {});
})

addUtoolsEventListener('function.flowExecutor', async (e) => {
  const flowId = e.getParamsKey('flowId');
  const nodeId = e.getParamsKey('nodeId');
  FlowExecutor.executor(flowId, {
    triggerNodeId: nodeId,
    originTriggerContext: e.pluginEnterParams,
  }).then(() => {})
})
