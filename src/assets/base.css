#app {
    width: 100%;
    height: 100%;
}
* {
    padding: 0;
    margin: 0;
}
div {
    box-sizing: border-box;
}
img {
    width: 100%;
    height: 100%;
}
.u-fx {
    display: flex;
}
.u-fc {
    justify-content: center;
}
.u-gap10 {
    gap: 10px;
}
.u-gap5 {
    gap: 5px;
}
.u-fac {
    align-items: center;
}
.u-f-between {
    justify-content: space-between;
}
.u-pointer {
    cursor: pointer;
}
.u-pos-rel {
    position: relative;
}
.u-pos-abs {
    position: absolute;
}
.u-font-strong {
    font-weight: 700;
}
.font-size-smail, .u-font-size-smail {
    font-size: 12px;
}
.u-mb12 {
    margin-bottom: 12px;
}
.u-mb10 {
    margin-bottom: 10px;
}
.u-mt10 {
    margin-top: 10px;
}
.u-tips {
    font-size: 11px;
    color: #c3c3c3;
}
.u-input-error {
    border: 1px solid #ff0000
}
body {
    color: var(--text-color);
}
.u-radius10 {
    border-radius: 10px;
}
.u-padding0 {
    padding: 0 !important;
}
.u-w-full {
  width: 100%;
}
.u-h-full {
  height: 100%;
}
