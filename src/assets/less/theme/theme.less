@import "theme-dark";
@import "theme-light";
/*滚动条整体部分*/
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/*滚动条的轨道*/
::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

/*滚动条里面的小方块，能向上向下移动*/
::-webkit-scrollbar-thumb {
  background-color: #9b97a2;
  border-radius: 1px;
  border: 1px solid #9b97a2;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
}

::-webkit-scrollbar-thumb:hover {
  background-color: #676e77;
  border: 1px solid #676e77;
}

::-webkit-scrollbar-thumb:active {
  background-color: #4E5969;
  border: 1px solid #4E5969;
}

/*边角，即两个滚动条的交汇处*/
::-webkit-scrollbar-corner {
  background-color: transparent;
}

.arco-message {
  border-radius: 10px !important;
  border: none !important;
}

.u-time-picker-content {
  .arco-timepicker-column ul::after {
    height: 0 !important;
  }
}

.arco-collapse-item-header:focus {
  outline: transparent !important;
}
.driver-class {
  background: var(--main-background) !important;
  color: var(--text-color) !important;
  .driver-popover-close-btn {
    color: var(--text-color);
    transition: all 220ms linear;
    &:focus {
      color: var(--text-color);
      opacity: 0.6;
    }
    &:hover {
      opacity: 1;
    }
  }
  .driver-popover-prev-btn {
    text-shadow: none;
    border: none;
    background: var(--color-neutral-2);
    color: var(--text-color);
    transition: all 220ms linear;
    &:hover {
      background: var(--color-neutral-4);
    }
  }
  .driver-popover-next-btn {
    text-shadow: none;
    border: none;
    background: rgb(var(--arcoblue-6));
    color: #ffffff;
    transition: all 220ms linear;
    &:hover {
      background: rgb(var(--arcoblue-5));
    }
  }
  .driver-popover-arrow-side-right{
    border-right-color: var(--main-background);
  }
  .driver-popover-arrow-side-left {
    border-left-color: var(--main-background);
  }
  .driver-popover-arrow-side-bottom {
    border-bottom-color: var(--main-background);
  }
  .driver-popover-arrow-side-top {
    border-top-color: var(--main-background);
  }
}



