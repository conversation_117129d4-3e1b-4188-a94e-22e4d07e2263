body[arco-theme="dark"] {
  .sidebar {
    background: var(--color-neutral-2);
  }
  .sidebar-item {
    &:hover {
      background-color: rgba(72, 72, 73, 0.55);
    }
  }
}
.sidebar {
  height: 100%;
  width: 190px;
  box-sizing: border-box;
  overflow-x: hidden;
  background-color: var(--main-background-transparent);
  border-right: 1px solid var(--color-neutral-3);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  flex-shrink: 0;
  > div:first-child {
    min-width: 190px !important;
  }
  &.collapsed {
    width: 0;
    padding: 0;
    min-width: 0;
    border: none;
  }
}


.sidebar-group {
  &:hover {
    .section-title {
      >div:last-child {
        opacity: 1;
      }
    }
  }
}
.sidebar-section {
  &.item {
    transition: max-height 200ms linear;
    &.collapsed {
      overflow: hidden;
      max-height: 0;
    }
  }
  .section-title {
    padding: 4px 6px 4px 2px;
    cursor: pointer;
    user-select: none;
    color: var(--color-neutral-5);
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    border-radius: 4px;
    transition: color 260ms linear;
    &:hover {
      color: var(--text-color);
    }
    >div:last-child {
      opacity: 0;
      color: var(--color-neutral-6);
      transition: all 300ms ease;
      &:hover {
        color: var(--color-neutral-9);
      }
      iconpark-icon {
        font-size: 18px;
        cursor: pointer;
      }
    }
  }
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  margin: 2px 0px;
  border-radius: var(--border-radius-medium);
  cursor: pointer;
  line-height: 24px;
  transition: all 250ms ease-in-out;
  &:hover {
    background-color: var(--color-neutral-2);
  }
  &.active {
    background-color: var(--color-neutral-3);
  }
  .icon {
    margin-right: 10px;
    font-size: 20px;
    color: #767676;
  }
  .name {
    flex-grow: 1;
  }
  .count {
    color: #888;
  }
}

.section-description {
  padding: 5px 15px;
  color: #888;
  font-size: 12px;
  line-height: 1.4;
}

.sidebar-footer {
  margin-top: auto;
}
