import { ref, shallowRef, toRaw, watch } from 'vue';
import type { ShallowRef,  Ref } from 'vue';
import type { UseUtoolsDbOptions } from '@/hooks/utoolsHooks.ts';
import { isArray, isObject } from '@/utils/jsUtil.ts';
import { mergeWith } from 'es-toolkit';
import { ExtensionManager } from '@/extension';


/**
 * 生成保存的 key
 * @param key
 * @param platform
 */
function generateSaveKey(key: string, platform: boolean) {
  const keys = [key];
  if (platform) {
    if (utools.isWindows()) {
      keys.push('windows');
    } else if (utools.isMacOS()) {
      keys.push('macOS');
    } else if (utools.isLinux()) {
      keys.push('linux');
    }
  }
  return keys.join('/');
}

export function useLocalData<T extends (string | number | boolean | object | null) = any,
  E extends (string | number | boolean | object | null) = any>(
  key: string,
  initialValue: T,
  options: UseUtoolsDbOptions<T, E> = {} as any,
): Ref<T> {
  const {
    templateValue = undefined,
    platform = false,
    flush = 'pre',
    deep = true,
    isShallowRef,
    onError = (e: any) => {
      console.error(e);
    },
    initData = (data: any) => {}
  } = options;


  key = generateSaveKey(key, platform);


  const sourceValue = ExtensionManager.getLocalStorageInstance().getData<any>(key);

  let data: ShallowRef<T> | Ref<T> | null = null;
  if (isObject(initialValue)) {
    data = (isShallowRef ? shallowRef : ref)((typeof sourceValue === 'undefined' || sourceValue === null)
      ? initialValue
      : mergeWith(sourceValue, initialValue, (objValue: any) =>  objValue)) as Ref<T>
  } else if (isArray(initialValue) && templateValue) {
    data = (isShallowRef ? shallowRef : ref)((typeof sourceValue === 'undefined' || sourceValue === null)
      ? initialValue
      : sourceValue.map((item: any) => mergeWith(item, templateValue, (objValue: any) => objValue))) as Ref<T>
  } else {
    data = (isShallowRef ? shallowRef : ref)((typeof sourceValue === 'undefined' || sourceValue === null)
      ? initialValue
      : sourceValue) as Ref<T>
  }

  if (sourceValue == null) {
    initData(data.value);
  }

  watch(
    data,
    (val) => {
      try {
        console.log('变更', val, data.value)
        if (data.value == null) {
          ExtensionManager.getLocalStorageInstance()
            .removeData(key)
        } else {
          ExtensionManager.getLocalStorageInstance()
            .setData(key, toRaw(data.value))
        }
      } catch (e) {
        onError(e)
      }
    },
    {
      flush,
      deep,
    });
  function setData(value: T) {
    if (data) {
      data.value = value;
    }
  }
  return data as Ref<T>
}
