import { type ShallowRef, useTemplateRef, ref } from 'vue';
import { isString } from 'es-toolkit';

export function useCollapse(refKey: string | Readonly<ShallowRef<HTMLDivElement | null>> = 'collapseRef'): [ShallowRef<boolean>, () => void, () => void] {
  const collapsed = ref<boolean>(false);
  const collapseRef = isString(refKey) ? useTemplateRef<HTMLDivElement>(refKey) : refKey;

  function collapse() {
    const parent = collapseRef.value!.parentNode as HTMLDivElement
    if (collapsed.value) {
      parent.style.maxHeight = `${collapseRef.value!.clientHeight}px`;
    } else {
      parent.style.maxHeight = `0px`;
    }
    collapsed.value = !collapsed.value;
  }

  function calcCollapseHeight() {
    const parent = collapseRef.value!.parentNode as HTMLDivElement
    parent.style.maxHeight = `${collapseRef.value!.clientHeight}px`;
  }

  return [collapsed, collapse, calcCollapseHeight];
}
