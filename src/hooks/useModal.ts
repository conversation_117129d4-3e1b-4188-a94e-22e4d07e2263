import { useEventListener } from '@vueuse/core';
import type { Fn } from '@vueuse/core';
export function useEscModal(eventType: 'keydown' | 'keyup' = 'keydown') {
  let cleanEsc: Fn | null = null;
  function startEsc() {
    cleanEsc = useEventListener(document, 'keydown', (e) => {
      if (e.key === 'Escape') {
        e.stopPropagation();
        e.preventDefault();
      }
    });
  }
  function stopEsc() {
    cleanEsc && cleanEsc();
    cleanEsc = null;
  }
  return {
    startEsc,
    stopEsc
  }
}
