import type { BaseExtension } from '@xiaou66/todo-plugin'
import type { GroupExtension, TaskExtension } from '@xiaou66/todo-plugin'
import { UtoolsGroupExtension } from '@/extension/utools/UtoolsGroupExtension.ts'
import { UtoolsTaskExtension } from '@/extension/utools/UtoolsTasksExtension.ts'
import {UtoolsStorageExtension} from "@/extension/utools/UtoolsStorageExtension.ts";
import { UtoolsTaskTagExtension } from '@/extension/utools/UtoolsTaskTagExtension.ts';
import { UtoolsAiExtension } from '@/extension/utools/UtoolsAiExtension.ts';
import {UtoolsTaskFilterExtension} from "@/extension/utools/UtoolsTaskFilterExtension.ts";
import { UtoolsFlowExtension } from '@/extension/utools/UtoolsFlowExtension.ts';
import {ref} from "vue";




/**
 *
 */
const extInstanceMap = ref<Record<string, BaseExtension>>({})
/**
 * 扩展点管理器
 */
export class ExtensionManager {


  public static getGroupInstance(): GroupExtension {
    return ExtensionManager.getInstance('group')as GroupExtension
  }

  public static getTaskInstance(): TaskExtension {
    return  ExtensionManager.getInstance('task')! as TaskExtension
  }
  public static getLocalStorageInstance() {
    return  ExtensionManager.getInstance('localStorage')! as UtoolsStorageExtension
  }

  public static getTaskTagInstance() {
    return  ExtensionManager.getInstance('taskTag')! as UtoolsTaskTagExtension
  }

  public static getAiInstance() {
    return  ExtensionManager.getInstance('ai')! as UtoolsAiExtension
  }

  public static getTaskFilterInstance(): UtoolsTaskFilterExtension {
    return  ExtensionManager.getInstance('taskFilter')! as UtoolsTaskFilterExtension
  }

  public static getFlowInstance() {
    return  ExtensionManager.getInstance('flow')! as UtoolsFlowExtension
  }


  public static pushInstance(code: string, extension: BaseExtension) {
     extInstanceMap.value[code] = extension;
  }


  static getInstance(code: string) {
    let instance: BaseExtension | undefined = extInstanceMap.value[code];
    if (instance) {
      return instance;
    }
    switch (code) {
      case 'group':
        instance =  new UtoolsGroupExtension();
        break;
      case 'task':
        instance = new UtoolsTaskExtension();
        break;
      case 'localStorage':
        instance = new UtoolsStorageExtension();
        break;
      case 'taskTag':
        instance = new UtoolsTaskTagExtension();
        break;
      case 'ai':
        instance = new UtoolsAiExtension();
        break;
      case 'taskFilter':
        instance = new UtoolsTaskFilterExtension();
        break;
      case 'flow':
        instance = new UtoolsFlowExtension();
        break;
    }

    extInstanceMap.value[code] = instance;

    return instance;
  }

  public static init() {
    ExtensionManager.pushInstance('group', new UtoolsGroupExtension())
    ExtensionManager.pushInstance('task', new UtoolsTaskExtension())
    ExtensionManager.pushInstance('localStorage', new UtoolsStorageExtension())
    ExtensionManager.pushInstance('taskTag', new UtoolsTaskTagExtension())
    ExtensionManager.pushInstance('ai', new UtoolsAiExtension())
    ExtensionManager.pushInstance('taskFilter', new UtoolsTaskFilterExtension())
    ExtensionManager.pushInstance('flow', new UtoolsFlowExtension())
  }

  public static rebuildSort() {
    ExtensionManager.getTaskInstance().rebuildSort();
  }
}
