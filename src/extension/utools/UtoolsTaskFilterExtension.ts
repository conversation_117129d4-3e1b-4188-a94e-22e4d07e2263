import type {ITaskFilter, SaveTaskFilterType} from "@xiaou66/todo-plugin";
import {TaskFilterExtension} from "@xiaou66/todo-plugin";
import {nanoid} from "nanoid";
import {ListSortUtils} from "@/utils/ListSortUtils.ts";
import {cloneDeep} from "es-toolkit";

export class UtoolsTaskFilterExtension extends TaskFilterExtension {
  private static TASK_FILTER_PREFIX: string = 'taskFilter';
  private static TASK_FILTER_MAX_SORT: string = "maxTaskFilterSort";

  constructor() {
    super();
  }

  /**
   * 保存任务过滤器
   * @param taskFilter
   */
  async saveTaskFilter(taskFilter: SaveTaskFilterType): Promise<string> {
    taskFilter = cloneDeep(taskFilter);
    if (!taskFilter.id) {
      // 新增
      taskFilter.id = nanoid(32);
      taskFilter.createAt = Date.now();
      taskFilter.sort = await ListSortUtils.addItem(async () => {
        return utools.dbStorage.getItem<number>(UtoolsTaskFilterExtension.TASK_FILTER_MAX_SORT) || ListSortUtils.INITIAL_VALUE;
      });
    }
    taskFilter.updateAt = Date.now();

    utools.dbStorage.setItem(UtoolsTaskFilterExtension.TASK_FILTER_PREFIX + '/' + taskFilter.id, taskFilter);

    // 更新最大排序 sort
    const currentMaxSort = utools.dbStorage.getItem<number>(UtoolsTaskFilterExtension.TASK_FILTER_MAX_SORT);
    const sort = taskFilter.sort;
    if (currentMaxSort < sort!) {
      utools.dbStorage.setItem(UtoolsTaskFilterExtension.TASK_FILTER_MAX_SORT, sort);
    }
    return taskFilter.id;
  }
  /**
   * 获得任务过滤器列表
   */
  async getTaskFilterList(): Promise<ITaskFilter[]> {
    return utools.db.allDocs<{value: ITaskFilter}>(UtoolsTaskFilterExtension.TASK_FILTER_PREFIX)
      .map(item => item.value)
      .sort((a, b) => b.sort - a.sort);
  }

  /**
   * 删除任务过滤器
   * @param id
   */
  async deleteTaskFilter(id: string): Promise<void> {
    utools.dbStorage.removeItem(UtoolsTaskFilterExtension.TASK_FILTER_PREFIX + '/' + id);
  }
}
