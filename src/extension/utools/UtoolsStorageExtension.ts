import {LocalStorageExtension} from "@xiaou66/todo-plugin";

export class UtoolsStorageExtension extends LocalStorageExtension {
  constructor() {
    super();
  }
  setData(key: string, data: any) {
    utools.dbStorage.setItem('local/' + key, data);
  }
  getData<T>(key: string): T {
    return utools.dbStorage.getItem('local/' + key);
  }

  removeData(key: string) {
    utools.dbStorage.removeItem('local/' + key);
  }
}

