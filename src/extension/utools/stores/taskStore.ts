import { defineStore } from "pinia";
import { onMounted, ref } from 'vue'
import type { ITaskStatus } from '@xiaou66/todo-plugin'

const defaultTaskStatus: ITaskStatus[] = [
  {
    id: 0,
    label: '未开始',
    color: 'gray',
    start: true,
    createAt: 0,
    updateAt: 0
  },
  {
    id: 2,
    label: '进行中',
    color: 'blue',
    createAt: 0,
    updateAt: 0,
  },
  {
    id: 3,
    label: '暂停中',
    color: 'orange',
    createAt: 0,
    updateAt: 0,
  },
  {
    id: 100,
    label: '已完成',
    color: 'green',
    finish: true,
    createAt: 0,
    updateAt: 0
  }
]
export const useTaskStore = defineStore('taskStore', () => {
  const taskStatusMap = ref<Record<string, Record<number, ITaskStatus>>>({});

  function getTaskStatus(id: number, taskStatusType = 'default') {
    return taskStatusMap.value[taskStatusType][id];
  }

  function getFinishTaskStatus(taskStatusType = 'default') {
    return taskStatusMap.value[taskStatusType][0];
  }


  function getTaskStatusList(taskStatusType = 'default') {
    // console.log('getTaskStatusList', Object.keys(taskStatusMap.value[taskStatusType]))
    return Object.keys(taskStatusMap.value[taskStatusType]).map(item => {
      return taskStatusMap.value[taskStatusType][Number(item)];
    })
  }
  function loadTaskStatusList() {
    const defaultTask = defaultTaskStatus.reduce((obj, cur) => {
      obj[cur.id] = cur;
      return obj;
    }, {} as Record<number, ITaskStatus>);
    taskStatusMap.value = {
      default: defaultTask,
    };
    console.log('taskStatusMap.value', taskStatusMap.value)
  }
  loadTaskStatusList();
  return {
    getTaskStatus,
    getTaskStatusList,
    loadTaskStatusList,
    getFinishTaskStatus
  }
});
