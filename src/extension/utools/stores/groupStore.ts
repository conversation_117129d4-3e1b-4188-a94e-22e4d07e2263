import { defineStore } from "pinia";
import type { IGroupListParams, IGroupInfo } from '@xiaou66/todo-plugin'
import { nanoid } from 'nanoid'


export const useGroupStore = defineStore('projectStore', () => {

  function getUtoolsCover(params: Partial<IGroupInfo>, groupId: string) {
    if (params.cover && params.cover.startsWith("data:image")) {
      // base64
      // @ts-ignore
      const buffer = window.nodeBuffer.Buffer.from(params.cover
        .replace(/^data:image\/\w+;base64,/, ''), 'base64');
      const key = `group/${groupId}/logo`;
      utools.db.remove(key);
      utools.db.postAttachment(key, buffer, 'image/png');
      return `utools:attachment://${key}`;
    }
    return params.cover;
  }
});
