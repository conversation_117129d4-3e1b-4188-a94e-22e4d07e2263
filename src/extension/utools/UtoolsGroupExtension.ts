import type {
  IGroupCreateParams,
  IGroupInfo,
  IGroupListParams,
  IGroupListResult,
  ITaskTag,
  ITaskStatus,
  ITaskItem,
  SaveGroupParams
} from '@xiaou66/todo-plugin'
import { GroupExtension } from '@xiaou66/todo-plugin'
import { nanoid } from 'nanoid'
import { useTaskStore } from '@/extension/utools/stores/taskStore.ts'
import { UtoolsTaskExtension } from '@/extension/utools/UtoolsTasksExtension.ts';
import { ExtensionManager } from '@/extension';
import { umami } from '@/utils/umami';
import { ListSortUtils } from '@/utils/ListSortUtils.ts';


/**
 * utools 实现分组扩展点
 */
export class UtoolsGroupExtension extends GroupExtension {
  private static GROUP_INFO_PREFIX = 'groupInfo';
  private static GROUP_MAX_SORT = "maxGroupSort";
  public async createGroup(params: IGroupCreateParams) {
    // 新建
    const groupId = nanoid();
    const sort = await ListSortUtils.addItem(async () => {
      return utools.dbStorage.getItem<number>(UtoolsGroupExtension.GROUP_MAX_SORT) || ListSortUtils.INITIAL_VALUE;
    });
    await this.saveGroupInner({
      ...params,
      groupId,
      sort,
      updateAt: Date.now(),
      createAt: Date.now(),
    })
    umami().trackEvent('创建分组');
    return groupId;
  }

  async saveGroup(data: SaveGroupParams): Promise<void> {
    await this.saveGroupInner(data);
  }

  async saveGroupInner(params: Partial<IGroupInfo>) {
    const now = Date.now();
    const sort = params.sort;
    const key =  UtoolsGroupExtension.GROUP_INFO_PREFIX + `/${params.groupId}`;
    const groupInfo = utools.dbStorage.getItem(key);
    // params.cover = getUtoolsCover(params, params.groupId);
    utools.dbStorage.setItem(key, {
      ...groupInfo,
      ...params,
      updateAt: now,
    } as IGroupInfo);

    // 更新最大排序 sort
    const currentMaxSort = utools.dbStorage.getItem<number>(UtoolsGroupExtension.GROUP_MAX_SORT);
    if (currentMaxSort < sort!) {
      utools.dbStorage.setItem(UtoolsGroupExtension.GROUP_MAX_SORT, sort);
    }
    return params.groupId;
  }

  public async listGroup(params: IGroupListParams = {}): Promise<IGroupListResult> {
    const list = utools.db.allDocs(UtoolsGroupExtension.GROUP_INFO_PREFIX + '/')
      .map(item => {
        return item.value as IGroupInfo;
      }).sort((a, b) => b.sort - a.sort);
    return Promise.resolve({
      total: 0,
      list,
    })
  }

  public async getGroup(groupId: string): Promise<IGroupInfo> {
    return utools.dbStorage.getItem<IGroupInfo>(UtoolsGroupExtension.GROUP_INFO_PREFIX + '/' + groupId);
  }

  /**
   * 保存任务标签
   * @param groupId
   * @param tag
   */
  public async saveTaskTag(groupId: string, tag: Partial<ITaskTag>): Promise<ITaskTag> {
    if (!tag.id) {
      tag.id = nanoid();
      tag.createdAt = Date.now();
    }
    const docId = `group/${groupId}/tagList`;
    const list: ITaskTag[] = utools.dbStorage.getItem(docId) || [];
    const index = list.findIndex(item => item.id === tag.id);
    if (index > -1) {
      list.splice(index, 1, {
        ...list[index],
        ...tag
      });
    } else {
      list.push({
        ...(tag as ITaskTag),
      });
    }
    utools.dbStorage.setItem(docId, list);
    return tag as ITaskTag;
  }


  /**
   * 根据 groupId 获取这个分组全部状态标签
   * @param groupId 分组 id
   */
  public async getTaskStatusList(groupId: string): Promise<ITaskStatus[]> {
    const taskStore = useTaskStore();
    return taskStore.getTaskStatusList();
  }

  /**
   * 获取指定组的完成任务状态
   *
   * @param groupId 组ID
   * @returns 返回包含完成任务状态的ITaskStatus对象
   */
  public async getFinishTaskStatus(groupId: string): Promise<ITaskStatus> {
    const groupInfo = await this.getGroup(groupId);
    const taskStore = useTaskStore();
    return taskStore.getFinishTaskStatus(groupInfo.taskStatusTypeId  || 'default');
  }

  /**
   * 删除分组
   * @param groupId
   */
  public async deleteGroup(groupId: string) {
    const taskInstance = ExtensionManager.getTaskInstance();
    const allDocs = utools.db.allDocs<{value: ITaskItem}>(UtoolsTaskExtension.TASK_DOC_PREFIX + '/' + groupId);
    const tasks: Promise<any>[] = [];
    tasks.push(...allDocs.map(item =>
      taskInstance.completelyDeleteTask(item.value)));
    tasks.push(...utools.db.allDocs<{value: ITaskItem}>(UtoolsTaskExtension.FINISH_TASK_DOC_PREFIX)
      .filter(item => item.value.taskGroupId === groupId)
      .map(item => taskInstance.completelyDeleteTask(item.value)));
    tasks.push(...utools.db.allDocs<{value: ITaskItem}>(UtoolsTaskExtension.DELETED_TASK_DOC_PREFIX)
      .filter(item => item.value.taskGroupId === groupId)
      .map(item => taskInstance.completelyDeleteTask(item.value)));
    console.log(`groupInfo/${groupId}`);
    tasks.push(utools.db.promises.remove(UtoolsGroupExtension.GROUP_INFO_PREFIX + `/${groupId}`));
    await Promise.all(tasks);
    umami().trackEvent('删除分组').then(() => {});
  }
}
