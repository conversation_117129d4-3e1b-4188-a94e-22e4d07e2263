import { TaskTagExtension } from '@xiaou66/todo-plugin';
import type { ITaskTagCreateParam, ITaskTagMergeParam, ITaskTag, ITaskItem } from '@xiaou66/todo-plugin';
import { nanoid } from 'nanoid';
import { ListSortUtils } from '@/utils/ListSortUtils.ts';
import { umami } from '@/utils/umami';
import { UtoolsTaskExtension } from '@/extension/utools/UtoolsTasksExtension.ts';
import { ExtensionManager } from '@/extension';

export class UtoolsTaskTagExtension extends TaskTagExtension {
  private static TASK_TAG_DOC_PREFIX = 'tag/task';
  private static TASK_TAG_MAX_SORT = "maxTaskTagSort";

  /**
   * 新建标签项
   */
  public async createTag(tag: ITaskTagCreateParam): Promise<ITaskTag> {
    const sort = await ListSortUtils.addItem(async () => {
      return utools.dbStorage.getItem<number>(UtoolsTaskTagExtension.TASK_TAG_MAX_SORT) || ListSortUtils.MIN_INTERVAL_VALUE;
    });
    const id =  nanoid(16);
    const saveTag: ITaskTag = {
      ...tag,
      id,
      sort,
      createdAt: Date.now(),
      updateAt: Date.now(),
    };
    this.saveTagInner(saveTag);

    umami().trackEvent('创建任务标签')
      .then(() => {});
    return saveTag;
  }

  /**
   * 保存标签项
   */
  public async saveTag(saveTag: ITaskTag): Promise<string> {
    this.saveTagInner(saveTag);
    umami().trackEvent('保存任务标签')
      .then(() => {});
    return saveTag.id;
  }


  private saveTagInner(saveTag: ITaskTag) {
    utools.dbStorage.setItem(UtoolsTaskTagExtension.TASK_TAG_DOC_PREFIX + '/' + saveTag.name, {
      ...saveTag,
      updateAt: Date.now(),
    } as ITaskTag);
    // 更新最大排序 sort
    const currentMaxSort = utools.dbStorage.getItem<number>(UtoolsTaskTagExtension.TASK_TAG_MAX_SORT);
    if (currentMaxSort < saveTag.sort) {
      utools.dbStorage.setItem(UtoolsTaskTagExtension.TASK_TAG_MAX_SORT, saveTag.sort);
    }
  }

  /**
   * 获取标签列表
   */
  public async listTag(): Promise<ITaskTag[]> {
    return utools.db.promises
      .allDocs(UtoolsTaskTagExtension.TASK_TAG_DOC_PREFIX)
      .then((item) => {
        return item.map((item) => item.value) as ITaskTag[];
      })
      .then((item) => {
        return item.sort((a, b) =>  b.sort - a.sort);
      });
  }

  public async getTag(tagName: string): Promise<ITaskTag> {
    return utools.dbStorage.getItem(UtoolsTaskTagExtension.TASK_TAG_DOC_PREFIX + '/' + tagName);
  }

  /**
   * 合并标签项
   */
  public async mergeTag(tag: ITaskTagMergeParam): Promise<string> {
    const tasks: Promise<any>[] = [];
    tasks.push(utools.db.promises.allDocs(UtoolsTaskExtension.TASK_DOC_PREFIX)
      .then(doc => {
        return this.updateTaskItemTag(doc.map(({value}) => value), tag.tagName, tag.toTagName)
    }));
    tasks.push(utools.db.promises.allDocs(UtoolsTaskExtension.TASK_DOC_PREFIX)
      .then(doc => {
        return this.updateTaskItemTag(doc.map(({value}) => value), tag.tagName, tag.toTagName)
      }));
    tasks.push(utools.db.promises.allDocs(UtoolsTaskExtension.TASK_DOC_PREFIX)
      .then(doc => {
        return this.updateTaskItemTag(doc.map(({value}) => value), tag.tagName, tag.toTagName)
      }));
    tasks.push(utools.db.promises.remove(UtoolsTaskTagExtension.TASK_TAG_DOC_PREFIX + '/' + tag.tagName));
    await Promise.all(tasks);
    umami().trackEvent('合并标签').then(() => {});
    return 'ok';
  }

  /**
   * 删除标签项
   */
  public async deleteTag(tagName: string): Promise<string> {
    umami().trackEvent('删除标签').then(() => {});
    return this.mergeTag({
      tagName,
      toTagName: '',
    });
  }

  private async updateTaskItemTag(taskItemList: ITaskItem[], tagName: string, toTagName?: string) {
    const taskInstance = ExtensionManager.getTaskInstance();
    const allPromise = taskItemList.map(taskItem => {
      if (!taskItem.tagNameList) {
        return null;
      }
      const tagNameList = taskItem.tagNameList || [];
      const index = tagNameList.indexOf(tagName);
      if (index > -1) {
        tagNameList.splice(index, 1);
        if (toTagName && toTagName.indexOf(tagName) !== -1) {
          tagNameList.push(toTagName);
        }
        return taskInstance.saveTask(taskItem.taskGroupId, {
          ...taskItem,
          tagNameList,
        })
      }
      return null;
    }).filter(item => item);
    await Promise.all(allPromise);
  }
}
