import { AiExtension } from '@xiaou66/todo-plugin';
import type { SavePromptParams, IAiPrompt } from '@xiaou66/todo-plugin';
import { nanoid } from 'nanoid';
import { ListSortUtils } from '@/utils/ListSortUtils.ts';

const defaultPrompts: SavePromptParams[] = [
  {
    name: '总结',
    content: `## 标题: 工作(XX 年 XX 月 XX 日-XX 年 XX 月 XX 日)
一、工作总结

- 工作事项 1 (已完成,XX 年 XX 月 XX 日)
使用20个字进行描述总结
- 工作事项 2
使用20个字进行描述总结
- 工作事项 3
使用20个字进行描述总结

---

最终完成 X 项任务,其中 X 项暂未完成,最终任务完成度 X%。

二、存在问题

- 问题 1
- 问题 2

三、下阶段工作计划

- 工作计划 1
- 工作计划 2
- 工作计划 3
`,
    useFor: 'summary',
  },
  {
    name: '简单复盘',
    content: `你是一名效率教练，请根据用户提供的任务数据，分析{{时间段}}内的整体任务执行情况，提炼行为模式、核心问题及优化方向。输出内容需满足：
1. **不分析单个任务**，只总结周期性规律；
2. 语言简洁，结论用「关键词+1句解释」形式；
3. 优先从「时间管理」「优先级判断」「行为惯性」三个维度切入。
输出格式:
**{{时间段}}任务复盘报告**
⏱️ **时间陷阱**
- 关键词1：{{结论}}（示例：行政事务 · 平均超时120%）
- 关键词2：{{结论}}
{{/}}
❗ **优先级误判**
- 关键词1：{{结论}}（示例：60%重要任务延迟因晨间会议干扰）
{{/}}
🔄 **行为惯性**
- 关键词1：{{结论}}（示例：“学习”标签任务· 周末完成率下降40%）
{{/}}
🚀 **优化方向**
1. {{可执行指令1}}
2. {{可执行指令2}} `,
    useFor: 'summary',
  },
  {
    name: '复盘',
    content: `### 一、整体分析框架
- **G（目标回顾）**：
  本阶段核心目标（根据任务等级与标签推导，示例：提升要事占比至60%）
- **R（结果评估）**：
  - ✅ 成功点：高完成率任务类别的共性（如：固定时间块的任务更易完成）
  - ❌ 差距点：未完成任务的典型特征（如：无明确截止时间的任务流失率80%）
- **A（归因分析）**：
  - 时间陷阱：紧急事务是否挤压重要不紧急任务？
  - 能量管理：高精力时段是否匹配高价值任务？
- **I（规律总结）**：
  提炼1条可复用的核心方法论（示例：为重要不紧急任务设定“保护性时间”）
{{/}}
### 二、行动优化方案
| 类型       | 措施                                      | 落地指标               |
|------------|-------------------------------------------|------------------------|
| **Keep**   | 需延续的高效行为（例：每日晨间规划）       | 维持≥5次/周            |
| **Improve**| 待改进的薄弱环节（例：碎片任务批量处理）   | 减少50%中断响应        |
| **Start**  | 新启动的关键动作（例：周日晚复盘下阶段要事）| 每周执行率100%         |
| **Stop**   | 需终止的低效行为（例：无预案应对紧急事务） | 建立3套应急预案        |
{{/}}
### 三、下阶段目标设定
> 基于复盘结论，制定1个具体改进目标：
> **“在{下一时间段}，通过{具体行动}，将{关键指标}提升/降低{X}%”**
> （示例：未来1个月，通过每日17:00清理待办清单，将遗漏任务数降至≤2件/周）

--- `,
    useFor: 'summary',
  },
  {
    name: '更新日志',
    content: `
    你是一名亲切的产品日志助手，根据用户任务数据生成温暖友好的更新日志。如果缺少数据请忽略严格按此流程执行：
1. **过滤数据**：仅处理 \`finish: true\` 的任务，跳过未完成任务
2. **智能分类**：
   - \`tagNameList\`含"新增/新功能"→\`🎉功能新增\`
   - \`tagNameList\`含"优化/改进"→\`🚀功能优化\`
   - \`tagNameList\`含"BUG/修复"→\`🐛BUG修复\`
   - \`tagNameList\`含"废弃/下线"→\`❌功能废弃\`
3. **情感化表达**：
   - 每个条目加随机表情符号(✨/💖/⚡/🔧/🐞)
   - 用口语化描述（如"现在你能更轻松地..."）
   - 添加人性细节（如"设计师小林反复调试了3版"）
4. **动态时间**：
   - 将\`taskEndDate\`转为相对时间（"X天前"）
   - 保留绝对时间（\`YYYY-MM-DD HH:mm\`）
5. **日志生成**：
## 🥳 {{版本号:在标签中查询获取最高版本号如果没有则是v1.0.0}}
### 🎉 功能新增
1. **{{taskTitle}}**
  {{口语化提炼content}}
2. **{{taskTitle}}**
  {{口语化提炼content}}
{{/}}
### 🚀 功能优化
{{#优化类任务}}
1. **{{taskTitle}}**
  {{口语化提炼content}}
2. **{{taskTitle}}**
  {{口语化提炼content}}
{{/}}
### 🐛 BUG 修复
{{#修复类任务}}
1. **{{taskTitle}}**
  {{口语化提炼content}}
2. **{{taskTitle}}**
  {{口语化提炼content}}
{{/}}
### ❌ 功能废弃
1. **{{taskTitle}}**
  {{口语化提炼content}}
2. **{{taskTitle}}**
  {{口语化提炼content}}
{{/}}
    `,
    useFor: 'summary'
  }
]
export class UtoolsAiExtension extends AiExtension {

  private AI_PROMPT = 'ai/prompt';
  private static AI_PROMPT_MAX_SORT = "aiPromptMaxSort";

  /**
   * 获取提示词列表
   */
  async getPromptList(): Promise<IAiPrompt[]> {

    let aiPrompts = await this.getPromptListInner();
    if (aiPrompts.length > 0) {
      return aiPrompts;
    }
    await this.initData();
    aiPrompts = await this.getPromptListInner();
    return aiPrompts;
  }

  async getPromptListInner(): Promise<IAiPrompt[]> {
    return (utools.db.allDocs<{value: IAiPrompt[]}>(this.AI_PROMPT) || [])
      .map(({ value }) => value)
      .flat()
      .sort((a, b) => b.sort - a.sort);
  }


  async initData() {
    for (const defaultPrompt of defaultPrompts) {
      await this.savePrompt(defaultPrompt)
    }
  }

  /**
   * 保存提示词
   */
  async savePrompt(params: SavePromptParams) {
    if (!params.id) {
      params.id = nanoid(32);
      params.sort = await ListSortUtils.addItem(async () => {
        return utools.dbStorage.getItem<number>(UtoolsAiExtension.AI_PROMPT_MAX_SORT) || ListSortUtils.INITIAL_VALUE;
      });
    }

    utools.dbStorage.setItem(this.AI_PROMPT + '/' + params.id, params);

    // 更新最大排序 sort
    const sort = params.sort;
    const currentMaxSort = utools.dbStorage.getItem<number>(UtoolsAiExtension.AI_PROMPT_MAX_SORT);
    if (currentMaxSort && currentMaxSort < sort!) {
      utools.dbStorage.setItem(UtoolsAiExtension.AI_PROMPT_MAX_SORT, sort);
    } else if (!currentMaxSort) {
      utools.dbStorage.setItem(UtoolsAiExtension.AI_PROMPT_MAX_SORT, sort);
    }
  }

  /**
   * 删除提示词
   */
  async deletePrompt(id: string) {
    utools.dbStorage.removeItem(this.AI_PROMPT + '/' + id);
  }
}
