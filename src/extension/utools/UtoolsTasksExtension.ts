import type {
  PageResult,
  ITaskItem,
  TaskListItem,
  TaskListParams,
  TaskListBatchParams,
  TaskSaveParams,
  ITaskDesc,
} from '@xiaou66/todo-plugin';
import { TaskExtension } from '@xiaou66/todo-plugin'
import { nanoid } from 'nanoid'
import { ListSortUtils } from '@/utils/ListSortUtils.ts'
import dayjs from 'dayjs'
import {useTaskStore} from "@/extension/utools/stores/taskStore.ts";
import JsUtil from '@/utils/jsUtil.ts';
import { umami } from '@/utils/umami';
import { cloneDeep, isNil } from 'es-toolkit';
import { TASK_FILTER_TIME_MAP, TASK_FILTER_TIME_RULES } from '@/extension/TaskFilterRules.ts';

/**
 * utools 实现分组扩展点
 */
export class UtoolsTaskExtension extends TaskExtension {
  public static TASK_DOC_PREFIX = 'task/task-item';
  public static FINISH_TASK_DOC_PREFIX = 'finish/task/task-item';
  public static DELETED_TASK_DOC_PREFIX = 'deleted/task/task-item';
  private static TASK_CONTENT_DESCRIPTION = "task/description";
  private static TASK_MAX_SORT = "maxTaskSort";

  constructor() {
    super()
  }

  async saveTask(groupId: string, params: TaskSaveParams): Promise<ITaskItem> {
    if (!groupId) {
      groupId = 'collectBox';
      params.taskGroupId = 'collectBox';
    }
    params = JSON.parse(JSON.stringify(params))
    let data: Partial<ITaskItem> = {}
    if (!params.taskId) {
      umami().trackEvent('创建任务');
      params.taskId = nanoid()
      const sort = await ListSortUtils.addItem(async () => {
        return utools.dbStorage.getItem<number>(UtoolsTaskExtension.TASK_MAX_SORT) || ListSortUtils.MIN_INTERVAL_VALUE;
      });
      data = {
        taskStatus: 0,
        createdAt: Date.now(),
        taskLevel: '2',
        sort,
        taskSource: 'utools',
      };
    } else if (params.docId) {
      data = utools.dbStorage.getItem<ITaskItem>(params.docId)
    }
    const taskStore = useTaskStore();

    let isFinish = false;
    const taskStatus = !isNil(params.taskStatus) ?  params.taskStatus :  data.taskStatus;
    if (taskStatus) {
      const taskStatusItem = taskStore.getTaskStatus(taskStatus, 'default');
      if (taskStatusItem.finish) {
        isFinish = true;
      }
    }

    const nowDate = dayjs().format('YYYY-MM-DD');

    const keys = [groupId, params.taskId]
    if (isFinish && !params.deleted) {
      keys.unshift(UtoolsTaskExtension.FINISH_TASK_DOC_PREFIX);
    }  else if (params.deleted) {
      keys.unshift(UtoolsTaskExtension.DELETED_TASK_DOC_PREFIX);
    } else {
      keys.unshift(UtoolsTaskExtension.TASK_DOC_PREFIX);
    }

    let key = keys.join('/');

    if (params.parentId) {
      key += `/parentId/${params.parentId}`;
    }

    const oldTaskDoc = utools.dbStorage.getItem<ITaskItem>(key);
    if (params.docId && !oldTaskDoc) {
      // parentId 发生变动了
      utools.dbStorage.removeItem(params.docId)
    }

    if (params.docId && key !== params.docId) {
      // 删除旧的 docId
      utools.dbStorage.removeItem(params.docId);
    }

    const saveData = {
      ...data,
      ...params,
      docId: key,
      finish: isFinish,
      deleted: !!params.deleted,
      taskGroupId: groupId,
      updateAt: Date.now(),
    } as ITaskItem;

    // 完结任务设置
    if (isFinish) {
      if (!oldTaskDoc || !oldTaskDoc.finish) {
        saveData.finishTime = nowDate;
        umami().trackEvent('完结任务');
      }
    }

    // 删除任务设置
    if (saveData.deleted) {
      if (!oldTaskDoc || !oldTaskDoc.deleted) {
        saveData.deleteTime = nowDate;
        umami().trackEvent('删除任务');
      }
    }

    console.log('saveTask', key, saveData);
    utools.dbStorage.setItem(key, saveData);

    const currentMaxSort = utools.dbStorage.getItem<number>(UtoolsTaskExtension.TASK_MAX_SORT);
    if (currentMaxSort < saveData.sort) {
      utools.dbStorage.setItem(UtoolsTaskExtension.TASK_MAX_SORT, saveData.sort);
    }
    return utools.dbStorage.getItem<ITaskItem>(key);
  }

  public async restartTask(task: ITaskItem): Promise<void> {
    debugger
    if (task.docId.startsWith(UtoolsTaskExtension.FINISH_TASK_DOC_PREFIX)) {
      const data = utools.dbStorage.getItem(task.docId);
      data.taskStatus = 0;
      data.sort =  await ListSortUtils.addItem(async () => {
        return utools.dbStorage.getItem<number>(UtoolsTaskExtension.TASK_MAX_SORT) || ListSortUtils.INITIAL_VALUE;
      });
      await this.saveTask(data.taskGroupId, data);
    }
  }

  async saveTaskDesc(taskId: string, content: string): Promise<void> {
    const taskPrefix = UtoolsTaskExtension.TASK_CONTENT_DESCRIPTION + "/" + taskId;
    utools.dbStorage.setItem(taskPrefix, {
      content,
      updateAt: Date.now(),
    } as ITaskDesc)
    umami().trackEvent('保存任务描述');
  }

  async getTaskDesc(taskId: string): Promise<string> {
    const docId = UtoolsTaskExtension.TASK_CONTENT_DESCRIPTION + "/" + taskId;
    return utools.dbStorage.getItem<ITaskDesc>(docId)?.content || '';
  }

  public async getTask(groupId: string, taskId: string): Promise<ITaskItem | null> {
    const allTaskDocPrefixList = [UtoolsTaskExtension.TASK_DOC_PREFIX,
      UtoolsTaskExtension.FINISH_TASK_DOC_PREFIX,
      UtoolsTaskExtension.DELETED_TASK_DOC_PREFIX];
    for (const taskDocPrefix of allTaskDocPrefixList) {
      const docId = taskDocPrefix + "/" + groupId + "/" + taskId;
      const item = utools.dbStorage.getItem<ITaskItem>(docId);
      if (item) {
        return item;
      }
    }
    return null;
  }

  private handleSearchListTaskParams(params: TaskListParams) {
    if (params.taskTimeRange && params.taskTimeRange.length == 2) {
      debugger
      params.taskStartTime = dayjs(params.taskTimeRange[0]).valueOf();
      params.taskEndTime = dayjs(params.taskTimeRange[1]).valueOf();
    }
  }

  public async listTaskBatch(params: TaskListBatchParams): Promise<TaskListItem[]> {
    if (!params.searchTypeList ||  !params.searchTypeList.length) {
      return [];
    }
    const res = await Promise.all(params.searchTypeList.map((searchType) => {
      const taskListParams = cloneDeep(params);
      let promise: Promise<PageResult<TaskListItem>>;
      if (searchType === 'default') {
        promise = this.listTask(taskListParams);
      } else {
        taskListParams.searchType = searchType;
        promise = this.listTask(taskListParams);
      }
      return promise.then((result) => {
        return result.list;
      }).catch(e => {
        console.error('listTaskBatch--出现异常', e);
        return [] as TaskListItem[];
      });
    }));
    return res.flat();
  }

  public async listTask(params: TaskListParams): Promise<PageResult<TaskListItem>> {
    // 处理查询参数
    // debugger
    this.handleSearchListTaskParams(params);
    let taskPrefix = UtoolsTaskExtension.TASK_DOC_PREFIX
    if (params.searchType === 'finish') {
      taskPrefix = UtoolsTaskExtension.FINISH_TASK_DOC_PREFIX
    } else if (params.searchType === 'deleted') {
      taskPrefix = UtoolsTaskExtension.DELETED_TASK_DOC_PREFIX
    }
    if (params.groupId) {
      // 分组查询
      taskPrefix += `/${params.groupId}`
    }
    const docs = utools.db.allDocs<{ value: ITaskItem }>(taskPrefix)
    // debugger
    let filterDocs = docs.filter((item) => !item._id.includes('parentId'))
    if (params.taskStartTime && params.taskEndTime) {
      // 时间过滤
      filterDocs = filterDocs.filter((item) => {
        const taskItem: ITaskItem = item.value;
        if(taskItem.taskStartDate) {
          if (taskItem.taskDateType === 'date') {
            // 单日期任务
            const taskStartTime = JsUtil.dateToConvertDayjs(taskItem.taskStartDate, taskItem.taskStartTime);
            return taskStartTime.valueOf() >= params.taskStartTime!
              && taskStartTime.valueOf() <= params.taskEndTime!;
          } else if (taskItem.taskDateType === 'dateSegment' ) {
            // 时间段任务
            const taskStartTime = JsUtil.dateToConvertDayjs(taskItem.taskStartDate, taskItem.taskStartTime);
            const taskEndTime = JsUtil.dateToConvertDayjs(taskItem.taskEndDate, taskItem.taskEndTime);
            return taskStartTime.valueOf() < params.taskEndTime! && taskEndTime.valueOf() > params.taskStartTime!;
          }
        }
        return false;
      });
    }
    // 关键字过滤
    if (params.keyword) {
      filterDocs = filterDocs.filter((doc) => {
        const taskItem: ITaskItem = doc.value;
        const keyword = params.keyword!;
        if (taskItem.taskTitle && taskItem.taskTitle.includes(keyword)) {
          return true;
        }
        return false;
      });
    }

    // 标签过滤
    if (params.tagNameList && params.tagNameList.length > 0) {
      filterDocs = filterDocs.filter((doc) => {
        const taskItem: ITaskItem = doc.value;
        if (!taskItem.tagNameList) {
          return false;
        }
        const tagNameList = taskItem.tagNameList;
        for (const tagName of params.tagNameList!) {
          if (tagNameList.includes(tagName)) {
            return true;
          }
        }
        return false;
      });
    }



    if (params.taskGroupIdList && params.taskGroupIdList.length > 0) {
      filterDocs = filterDocs.filter((doc) => {
        const taskItem: ITaskItem = doc.value;
        if (params.taskGroupIdList!.includes(taskItem.taskGroupId)) {
          return true;
        }
        return false;
      })
    }

    if (params.taskLevelList && params.taskLevelList.length > 0) {
      filterDocs = filterDocs.filter((doc) => {
        const taskItem: ITaskItem = doc.value;
        if (taskItem.taskLevel === undefined) {
          return false;
        }
        if (params.taskLevelList!.includes(taskItem.taskLevel)) {
          return true;
        }
        return false;
      });
    }


    if (params.dynamicTimeTypeList && params.dynamicTimeTypeList.length > 0) {
      // 这里使用或的关系
      filterDocs = params.dynamicTimeTypeList.map(dynamicTimeType => {
        const executor = TASK_FILTER_TIME_MAP[dynamicTimeType].getExecutor(params);
        return filterDocs.filter(taskItem => executor(taskItem.value))
      }).flat()
    }

    // 排序
    const resList = filterDocs.sort((a, b) => {
      return b.value.sort - a.value.sort
    });

    const list: TaskListItem[] = []
    for (const res of resList) {
      const item: TaskListItem = { ...res.value, children: []  }
      // item.children = docs
      //   .filter((docs) => docs._id.includes(`/parentId/${item.taskId}`))
      //   .map((doc) => doc.value)
      list.push(item)
    }
    return {
      total: list.length,
      list,
    }
  }

  rebuildSort(): void {
    const timestamp = utools.dbStorage.getItem<number | undefined>('rebuildSortTaskTime');
    if (!timestamp || dayjs(Date.now()).diff(dayjs(timestamp), 'days') >= 1) {
      console.log('重建排序 sort')
      const allDocs = utools.db.allDocs<{ value: ITaskItem }>(UtoolsTaskExtension.TASK_DOC_PREFIX);
      if (allDocs.values.length) {
        const map = allDocs.map(item => item.value);
        ListSortUtils.rebuildSort(map);
        utools.db.bulkDocs(allDocs);
      }
      utools.dbStorage.setItem('rebuildSortTaskTime', Date.now());
    }
  }

  public async cleanAllDeleteTask() {
    const allDocs = utools.db.allDocs<{ value: ITaskItem }>(UtoolsTaskExtension.DELETED_TASK_DOC_PREFIX);
    const executeTask = [];
    executeTask.push(...allDocs.map(item => {
      return this.completelyDeleteTask(item.value);
    }));
    await Promise.all(executeTask);
  }

  /**
   * 彻底删除任务项
   * @param task
   */
  public async completelyDeleteTask(task: ITaskItem): Promise<void> {
    const taskPrefix = UtoolsTaskExtension.TASK_CONTENT_DESCRIPTION + "/" + task.taskId;
    const taskContent = utools.db.promises.remove(taskPrefix);
    const taskDoc = utools.db.promises.remove(task.docId);
    await Promise.all([taskContent, taskDoc]);
  }

  public async cleanErrorTask() {
    let fix = false;
    utools.db.allDocs(UtoolsTaskExtension.TASK_DOC_PREFIX).map(({_id, value}) => {
      if (value.taskTitle === undefined) {
        utools.db.promises.remove(_id);
        fix = true;
      }
    })
    if (fix) {
      utools.showNotification('修复异常数据完成, 需要重新进入插件');
      utools.outPlugin(true);
    }
  }
}
