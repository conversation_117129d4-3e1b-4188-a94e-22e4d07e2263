import { FlowExtension, type IFlowItem } from '@xiaou66/todo-plugin';
import { nanoid } from 'nanoid';
import { ListSortUtils } from '@/utils/ListSortUtils.ts';
import { cloneDeep } from 'es-toolkit';
import type { SaveFlowItem } from '@xiaou66/todo-plugin';
import defaultFlowList from '../defaultFlowList.json';
import { FLOW_NODE_ACTIONS_MAP } from '@/views/CollectScene/flow';


export class UtoolsFlowExtension extends FlowExtension {
  private static FLOW_PREFIX = 'scene/flow';
  private static FLOW_MAX_SORT = "scene/maxFlowSort";
  constructor() {
    super();
  }

  async saveFlow(flowItem: SaveFlowItem): Promise<IFlowItem> {
    if (!flowItem.id) {
      flowItem.id = nanoid();
      flowItem.sort = await ListSortUtils.addItem(async () => {
        return utools.dbStorage.getItem<number>(UtoolsFlowExtension.FLOW_MAX_SORT) || ListSortUtils.INITIAL_VALUE;
      });
      flowItem.createAt = Date.now();
    }
    debugger
    flowItem.updateAt = Date.now();

    const docId = UtoolsFlowExtension.FLOW_PREFIX + '/' + flowItem.id;
    utools.dbStorage.setItem(docId, cloneDeep(flowItem));

    // 更新最大排序 sort
    const currentMaxSort = utools.dbStorage.getItem<number>(UtoolsFlowExtension.FLOW_MAX_SORT) || -1;
    const sort = flowItem.sort || 0;
    if (currentMaxSort < sort!) {
      utools.dbStorage.setItem(UtoolsFlowExtension.FLOW_MAX_SORT, sort);
    }

    return utools.dbStorage.getItem(docId);
  }

  async getFlow(flowId: string): Promise<IFlowItem> {
    return utools.dbStorage.getItem<IFlowItem>(UtoolsFlowExtension.FLOW_PREFIX + '/' + flowId);
  }

  async getFlowList(): Promise<IFlowItem[]> {
     const result = utools.db.allDocs<{value: IFlowItem}>(UtoolsFlowExtension.FLOW_PREFIX)
      .map(item => item.value)
      .sort((a, b) => b.sort - a.sort);
     if (result.length === 0) {
       for (const item of defaultFlowList) {
         await this.importFlow(item as IFlowItem);
       }
       return utools.db.allDocs<{value: IFlowItem}>(UtoolsFlowExtension.FLOW_PREFIX)
         .map(item => item.value)
         .sort((a, b) => b.sort - a.sort)
     }
     return result;
  }

  async deleteFlow(flowId: string): Promise<void> {
    utools.dbStorage.removeItem(UtoolsFlowExtension.FLOW_PREFIX + '/' + flowId);
  }

  async importFlow(flowItem: IFlowItem): Promise<IFlowItem> {
    const flowItemClone = cloneDeep<IFlowItem>(flowItem);
    flowItemClone.nodeList = flowItemClone.nodeList.map(item => {
      if (!item.id) {
        item.id = nanoid();
      }
      return item;
    });

    flowItem = await this.saveFlow(flowItemClone);

    if (flowItem.nodeList && flowItem.nodeList.length > 0) {
      for (const node of flowItem.nodeList) {
        const saveData = FLOW_NODE_ACTIONS_MAP[node.nodeCode].onSaveData;
        if (saveData) {
          saveData(flowItem, node);
        }
      }
    }
     return flowItem;
  }
}
