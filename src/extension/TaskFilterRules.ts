import type { ITaskItem, TaskListParams } from '@xiaou66/todo-plugin';
import dayjs, { type Dayjs } from 'dayjs';
import JsUtil from '@/utils/jsUtil.ts';
import { keyBy } from 'es-toolkit';

export interface ITaskFilterTimeRule {
  code: string;
  label: string;
  getExecutor: (args: TaskListParams) => (taskItem: ITaskItem) => boolean;
}

function getSingleDayPredicate(time: Dayjs, taskItem: ITaskItem) {
  if (!taskItem.taskEndDate && !taskItem.taskStartDate) {
    // 未设置时间
    return false;
  }

  if (!taskItem.taskEndDate) {
    return dayjs(taskItem.taskStartDate).isSame(time, 'day');
  }

  // 判断是否在时间段内
  const taskStart = JsUtil.dateToConvertDayjs(taskItem.taskStartDate, taskItem.taskStartTime);
  const taskEnd = JsUtil.dateToConvertDayjs(taskItem.taskEndDate, taskItem.taskEndTime);
  return (time.isAfter(taskStart) || time.isSame(taskStart, 'day'))
    && (time.isBefore(taskEnd) || time.isSame(taskEnd, 'day'));
}

function getTimeRangePredicate(startDate: Dayjs, endDate: Dayjs, taskItem: ITaskItem) {
  debugger
  if (!taskItem.taskEndDate && !taskItem.taskStartDate) {
    // 未设置时间
    return false;
  }
  const taskStartDate = dayjs(taskItem.taskStartDate);
  if (!taskItem.taskEndDate) {
    return (startDate.isBefore(taskStartDate) || startDate.isSame(taskStartDate, 'day'))
      && (endDate.isAfter(taskStartDate) || endDate.isSame(taskStartDate, 'day'));
  }

  const taskEndDate = dayjs(taskItem.taskEndDate);
  console.log(startDate.format("YYYY-MM-DD"), taskItem.taskStartDate)
  return (startDate.isBefore(taskStartDate) || startDate.isSame(taskStartDate, 'day'))
    && (endDate.isAfter(taskEndDate) || endDate.isSame(taskEndDate, 'day'));
}
export const TASK_FILTER_TIME_RULES: ITaskFilterTimeRule[] = [
  {
    code: 'expired',
    label: '已过期',
    getExecutor: (args) => {
      const now = dayjs();
      return (taskItem: ITaskItem) => {
        if (!taskItem.taskEndDate && !taskItem.taskStartDate) {
          // 未设置时间
          return false;
        }
        if (taskItem.taskEndDate) {
          return now.isAfter(JsUtil.dateToConvertDayjs(taskItem.taskEndDate, taskItem.taskEndTime));
        }
        return now.isAfter(
          JsUtil.dateToConvertDayjs(taskItem.taskStartDate, taskItem.taskStartTime),
        );
      };
    },
  },
  {
    code: 'unsetTime',
    label: '未设置',
    getExecutor: (args) => {
      return (taskItem: ITaskItem) => {
        if (!taskItem.taskEndDate && !taskItem.taskStartDate) {
          // 未设置时间
          return true;
        }
        return false;
      };
    },
  },
  {
    code: 'yesterday',
    label: '昨天',
    getExecutor(args) {
      const now = dayjs().add(-1, 'day').startOf('day');
      return (taskItem: ITaskItem) => {
        return getSingleDayPredicate(now, taskItem);
      }
    },
  },
  {
    code: 'today',
    label: '今天',
    getExecutor: (args) => {
      const now = dayjs().startOf('day');
      return (taskItem: ITaskItem) => {
        return getSingleDayPredicate(now, taskItem);
      };
    },
  },
  {
    code: 'tomorrow',
    label: '明天',
    getExecutor(args) {
      const now = dayjs().add(1, 'day').startOf('day');
        return (taskItem: ITaskItem) => {
          return getSingleDayPredicate(now, taskItem);
        };
    },
  },
  {
    code: 'prefixWeek',
    label: '上周',
    getExecutor(args) {
      const startDate = JsUtil.getDayjsStartWeek(dayjs().subtract(1, 'week'));
      const endDate = JsUtil.getDayjsStartWeek(dayjs().subtract(1, 'week'));
      return (taskItem: ITaskItem) => {
        return getTimeRangePredicate(startDate, endDate, taskItem);
      }
    },
  },
  {
    code: 'thisWeek',
    label: '本周',
    getExecutor(args) {
        const startDate = JsUtil.getDayjsStartWeek(dayjs());
        const endDate = JsUtil.getDayjsStartWeek(dayjs());
        return (taskItem: ITaskItem) => {
          return getTimeRangePredicate(startDate, endDate,  taskItem);
        }
    },
  },
  {
    code: 'prefixMonth',
    label: '上月',
    getExecutor(args) {
      const startDate = dayjs().subtract(1, 'month').startOf('month');
      const endDate = dayjs().subtract(1, 'month').endOf('month');
      return (taskItem: ITaskItem) => {
        return getTimeRangePredicate(startDate, endDate, taskItem);
      }
    },
  },
  {
    code: 'month',
    label: '本月',
    getExecutor(args) {
      const startDate = dayjs().startOf('month');
      const endDate = dayjs().endOf('month');
      return (taskItem: ITaskItem) => {
        return getTimeRangePredicate(startDate, endDate, taskItem);
      }
    },
  },
  {
    code: 'custom',
    label: '自定义',
    getExecutor(args) {
      debugger
      const [ dayStartDiff = 0,  dayEndDiff = 0 ] = args.customDiffTime  || [];
      const startDate = dayjs().subtract(dayStartDiff, 'day').startOf('day');
      const endDate = dayjs().subtract(dayEndDiff, 'day').endOf('day');
      return (taskItem: ITaskItem) => {
        return getTimeRangePredicate(startDate, endDate, taskItem);
      }
    },
  },
];
export const TASK_FILTER_TIME_MAP =
  keyBy(TASK_FILTER_TIME_RULES, (item) => item.code);
