import type { App } from 'vue'


function handleUtoolsAttachment(value: string): string {
  let url = value;
  if (value.startsWith('utools:attachment://')) {
    value = value.replace('utools:attachment://', '');
    const attachment = utools.db.getAttachment(value)!;
    const blob = new Blob([attachment], { type: utools.db.getAttachmentType(value)! });
    url = URL.createObjectURL(blob);
  }
  return url;
}



export default {
  install (app: App) {
    app.directive('imageUrl', {
      mounted (el, binding) {
        console.log('utoolsImage', el, binding);
        el.id = binding.value;
        el.src = handleUtoolsAttachment(binding.value.toString());
      },
      beforeUnmount: (el, binding) => {
        // 释放元素 img 标签
        if (el.src.startsWith('blob:')) {
          URL.revokeObjectURL(el.src);
        }
      }
    })
  }
}
