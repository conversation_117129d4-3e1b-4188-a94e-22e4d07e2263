<script lang="ts" setup>
import {computed, onMounted, ref} from "vue";
import {ExtensionManager} from "@/extension";
import type { IGroupInfo } from "@xiaou66/todo-plugin";
import type { DropdownOption } from 'tdesign-vue-next';


defineProps<{
  popupContainer?: string | HTMLElement | undefined | null;
}>();

const options = ref<{label:string, value: string}[]>([]);

const optionMap = computed<Record<string, any>>(() => {
  return options.value.reduce((obj, cur) => {
    // @ts-ignore
    obj[cur.value] = cur;
    return obj;
  }, {});
});

async function groupList() {
  const taskInstance = ExtensionManager.getGroupInstance();
  const group = await taskInstance.listGroup({} as any);
  options.value = group.list.map((item: IGroupInfo) => ({
    label: item.groupName,
    value: item.groupId,
  }));
}
onMounted(() => {
  groupList();
});

const modelValue = defineModel<string>('modelValue');

const emits = defineEmits<{
  (e: 'change', value: string): void
}>();

function handleSelectGroup(dropdownItem: DropdownOption) {
  modelValue.value = dropdownItem.value as string;
  emits('change', dropdownItem.value as string);
}
</script>
<template>
  <t-dropdown placement="top"
              :popupProps="{ attach: popupContainer, overlayClassName: 'u-dropdown-small' }"
              @click="(value: any) => handleSelectGroup(value)">
    <t-button class="u-transparent"
              size="small"
              theme="default"
              style="margin-right: 10px;">
      {{ modelValue && modelValue != 'collectBox' && optionMap[modelValue] ? optionMap[modelValue].label : '未分组' }}
    </t-button>
    <t-dropdown-menu>
      <t-dropdown-item value="collectBox">
        <span >未分组</span>
      </t-dropdown-item>
      <t-dropdown-item v-for="(option) in options"
                       :key="option.value"
                       :value="option.value">
        {{ option.label }}
      </t-dropdown-item>
    </t-dropdown-menu>
  </t-dropdown>
</template>
