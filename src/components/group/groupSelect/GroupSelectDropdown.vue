<script setup lang="ts">
import type { IGroupInfo } from "@xiaou66/todo-plugin";
import { computed, onMounted, ref, watch } from 'vue';
import { ExtensionManager } from '@/extension'


const options = ref<{label:string, value: string}[]>([]);
const optionMap = computed<Record<string, any>>(() => {
  return options.value.reduce((obj, cur) => {
    // @ts-ignore
    obj[cur.value] = cur;
    return obj;
  }, {});
})
async function groupList() {
  const taskInstance = ExtensionManager.getGroupInstance();
  const group = await taskInstance.listGroup({} as any);
  options.value = group.list.map((item: IGroupInfo) => ({
    label: item.groupName,
    value: item.groupId,
  }));
  // 未分组
  options.value.unshift({
    label: '未分组',
    value: 'collectBox'
  });
  if (modelValue.value !== '') {
    const idx = options.value.findIndex((item) => item.value === modelValue.value);
    if (idx === -1) {
      modelValue.value = '';
    }
  }
  if (modelValue.value === '') {
    modelValue.value = group.list[0].groupId;
  }
}

const modelValue = defineModel('modelValue', {
  default: 'collectBox'
});
watch(modelValue, () => {
  if (!modelValue.value) {
    modelValue.value = 'collectBox'
  } else {
    if (!popupVisible.value && modelValue.value !== 'collectBox') {
      const index = options.value.findIndex(item => item.value === modelValue.value);
      if (index === -1) {
        groupList();
      }
    }
  }
})
const emits = defineEmits<{
  blur: [];
}>();
function handleSelectEvent(value: string) {
  modelValue.value = value;
  emits('blur');
}
const popupVisible = ref(false);
onMounted(() => {
  groupList();
})

watch(() => popupVisible.value, (value) =>{
  if (value) {
    groupList();
  }
});

defineExpose<{refresh: () => Promise<void>}>({
  refresh: groupList,
})

const currentOptions = computed(() => {
  return options.value.filter(item => item.value !== modelValue.value);
});
</script>

<template>
  <t-dropdown v-if="modelValue"
              :popup-props="{
                  visible: popupVisible, overlayClassName: 'u-dropdown-small',
                  onVisibleChange: (value: boolean) => popupVisible = value
              }"
              trigger="hover">
    <t-button variant="text" size="small">
      {{modelValue === 'collectBox' || modelValue === '' || !optionMap[modelValue as any] ? '未分组' : optionMap[modelValue as any].label}}
    </t-button>
    <t-dropdown-menu>
      <t-dropdown-item v-for="option in currentOptions"
                       :key="option.value"
                       :value="option.value"
                       @click="handleSelectEvent(option.value as string)">
        <span class="text-sm">{{ option.label }}</span>
      </t-dropdown-item>
    </t-dropdown-menu>
  </t-dropdown>
</template>

<style scoped lang="less">

</style>
