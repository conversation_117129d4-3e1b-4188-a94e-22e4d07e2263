<script setup lang="ts">
import type { SvgIconProps } from './SvgIcon'
import {computed, ref} from 'vue'

const props = withDefaults(defineProps<SvgIconProps>(), {
  color: '',
  prefix: 'icon',
  size: 14,
  svgClass: '',
  hoverColor: '',
})

const symbolId = computed(() => `#${props.prefix}-${props.name}`)
</script>

<template>
  <div class="icon" :style="{fontSize: `${size}px`}">
    <svg aria-hidden="true">
      <use :xlink:href="symbolId.trim()" />
    </svg>
  </div>
</template>

<style   scoped>
/*.icon {
  display: flex;
  align-items: center;
  justify-content: center;
}*/
/*svg {
  width: 1em;
  height: 1em;
}*/
</style>
