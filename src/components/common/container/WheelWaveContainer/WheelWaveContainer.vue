<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue';

const props = defineProps<{
  size: number
}>()

const activeIndex = ref(0)
const timer = ref<number>()

/**
 * 切换显示内容
 */
function rotateMessage() {
  activeIndex.value = (activeIndex.value + 1) % props.size
  console.log(activeIndex.value)
}

onMounted(() => {
  // 每 5 秒切换一次
  timer.value = setInterval(rotateMessage, 5000) as any
})
onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})
</script>

<template>
  <div class="wheel-container">
    <transition-group name="slide">
      <div
        v-for="pos in Array.from({ length: size }, (_, i) => i)"
        :key="pos"
        class="slide-item"
        v-show="activeIndex === pos"
      >
        <slot :name="`${pos}`"></slot>
      </div>
    </transition-group>
  </div>
</template>

<style scoped lang="less">
.wheel-container {
  position: relative;
  overflow: hidden;
  width: 100%;
}

.slide-item {
  //position: absolute;
  width: 100%;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.5s ease;
  position: absolute;
  width: 100%;
}

.slide-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

.slide-enter-to,
.slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}
</style>
