<script setup lang="ts">
import { computed, type ComputedRef } from 'vue'
import dayjs, { type Dayjs } from 'dayjs';

const props = defineProps<{
  panelMonth: ComputedRef<Dayjs>
}>()
const emits = defineEmits<{
  selectMonth: [Dayjs]
}>();
const today = dayjs();
const monthItems = computed(() =>
  Array.from({ length: 12 }, (_, i) => ({
    label: `${i + 1}月`,
    value: i,
  })),
)

function selectMonth(monthIndex: number) {
  const dayjs = props.panelMonth.value.month(monthIndex).startOf('month');
  emits('selectMonth', dayjs);
}
</script>

<template>
  <div class="u-web-date-panel-month-grid">
    <div
      v-for="m in monthItems"
      :key="m.value"
      class="u-web-date-panel-month-cell"
      :class="{
          active: panelMonth.value.month() === m.value,
          current: today.month() === m.value && today.year() === panelMonth.value.year(),
        }"
      @click="selectMonth(m.value)"
    >
      <span class="text">{{ m.label }}</span>
    </div>
  </div>
</template>
