<script setup lang="ts">
import dayjs, { type Dayjs } from 'dayjs'
import type { DateCellMeta, DatePanelProps, HolidayType } from '../interface.ts'
import { computed, onMounted, useAttrs } from 'vue'
// @ts-ignore
import LunarCalendar from 'lunar-calendar'

const props = defineProps<DatePanelProps>()

// 判断是否范围选择
const isRange = computed(() => !!props.values);

const emits = defineEmits<{
  (e: 'selectCell', cell: DateCellMeta): void
}>();

const today = dayjs();

function fmtYMD(d: Dayjs): string {
  return d.format('YYYYMMDD')
}

function createLunarText(d: Dayjs): string {
  const lunar = LunarCalendar.solarToLunar(d.year(), d.month() + 1, d.date())
  if (fmtYMD(d) === today.format('YYYYMMDD')) {
    return lunar.lunarDayName
  }
  if (lunar.lunarFestival) return lunar.lunarFestival
  // if (lunar.solarFestival) return lunar.solarFestival
  // if (lunar.term) return lunar.term
  return lunar.lunarDayName
}

function buildCells(monthBase: Dayjs): DateCellMeta[] {
  const startOfMonth = monthBase.startOf('month')
  const start = startOfMonth.subtract(startOfMonth.day(), 'day') // 从周日开始
  const cells: DateCellMeta[] = []
  for (let i = 0; i < 42; i += 1) {
    const d = start.add(i, 'day')
    const key = fmtYMD(d)
    const marker = props.holidayMap?.[key] as HolidayType | undefined
    cells.push({
      dateTimestamp: d.valueOf(),
      inCurrentMonth: d.month() === monthBase.month(),
      isToday: d.isSame(today, 'day'),
      displayText: String(d.date()),
      lunarText: createLunarText(d),
      marker,
    })
  }
  return cells
}

// 天单元格渲染数据
const cells = computed(() => buildCells(props.panelMonth));
function selectCell(cell: DateCellMeta) {
  console.log('selectCell', cell, props)
  emits('selectCell',  cell);
}

// 范围
const start = computed<number | null>(() => isRange.value ? props?.values?.[0] || null : null);
const end = computed<number | null>(() => isRange.value ? props?.values?.[1] || null : null);
function inRange(ts: number): boolean {
  if (start.value == null || end.value == null) return false
  const a = Math.min(start.value, end.value)
  const b = Math.max(start.value, end.value)
  return ts >= a && ts <= b
}

const attrs = useAttrs();
onMounted(() => {
  console.log('useAttrs', attrs)
})
</script>

<template>
  <div class="u-web-date-panel-weeks">
    <div class="w">日</div>
    <div class="w">一</div>
    <div class="w">二</div>
    <div class="w">三</div>
    <div class="w">四</div>
    <div class="w">五</div>
    <div class="w">六</div>
  </div>
  <!-- 日期 -->
  <div class="u-web-date-panel-grid">
    <div
      v-for="cell in cells"
      :key="cell.dateTimestamp"
      class="u-web-date-panel-cell"
      :class="[
          {
            other: !cell.inCurrentMonth,
            today: cell.isToday,
          },
          isRange
          ? {
              start: start === cell.dateTimestamp,
              end: end === cell.dateTimestamp,
              inrange: inRange(cell.dateTimestamp),
          }
          : { selected: props.value && cell.dateTimestamp === props.value }
        ]"
      @click="selectCell(cell)"
    >
      <slot name="cell">
        <div class="corner" v-if="cell.marker">
            <span class="tag" :class="cell.marker === 'rest' ? 'rest' : 'work'">
              {{ cell.marker === 'rest' ? '休' : '班' }}
            </span>
        </div>
        <div class="day">{{ cell.displayText }}</div>
        <div class="lunar">{{ cell.lunarText }}</div>
      </slot>
    </div>
  </div>
</template>
