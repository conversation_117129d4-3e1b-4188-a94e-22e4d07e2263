<script setup lang="ts">
import type { HeaderPanelProps, PanelModeType } from '../interface.ts'
import { computed } from 'vue';

const props = defineProps<HeaderPanelProps>();
const emits = defineEmits<{
  setPanelMode: [value: PanelModeType];
}>();

const titleTextLeft = computed(() =>
  props.panelMode === 'date'
    ? `${props.panelMonth.year()}年${props.panelMonth.month() + 1}月`
    : `${props.panelMonth.year()}`,
);



function switchPanelMode() {
  console.log('props.panelMode', props.panelMode)
  emits('setPanelMode', props.panelMode === 'date' ? 'month' : 'date')
}
</script>

<template>
  <div class="u-web-date-panel-header">
    <div class="u-web-date-panel-title"
         @click="switchPanelMode">
      {{ titleTextLeft }}
    </div>
    <div class="u-web-date-panel-nav">
      <div class="u-web-date-panel-btn u-hover" @click="toPrevMonth"><t-icon class="i-u-left"></t-icon></div>
      <div class="u-web-date-panel-btn u-hover" title="回到本月" @click="toThisMonth">○</div>
      <div class="u-web-date-panel-btn u-hover" @click="toNextMonth"><t-icon class="i-u-right"></t-icon></div>
    </div>
  </div>
</template>

