import type { Ref, ComputedRef } from 'vue'
import type { Dayjs } from 'dayjs'

/**
 * 假期类型
 * rest: 休息
 * work: 工作
 */
export type HolidayType = 'rest' | 'work';

export type DateValueType = string | number | null | undefined;

/**
 * 假期定义
 */
export interface HolidayMap {
  [yyyyMMdd: string]: HolidayType
}

/**
 * 面板定义
 */
export interface PanelProps {
  size?: 'default' | 'small';
  holidayMap?: HolidayMap;
}

/**
 * 日期单元格元数据
 */
export interface DateCellMeta {
  /**
   * 日期时间戳
   */
  dateTimestamp: number
  /**
   * 是否当前月
   */
  inCurrentMonth: boolean
  /**
   * 是否当天
   */
  isToday: boolean
  /**
   * 显示日期-公历日
   */
  displayText: string
  /**
   * 农历/节气/节日显示
   */
  lunarText?: string
  /**
   * 休/班 标识
   */
  marker?: HolidayType
}

/**
 * 单日期组件 props
 */
export interface DatePickerPanelProps {
  /**
   * 格式化
   */
  formatValue?: string;
  /**
   * 日期面板
   */
  panelProps?: PanelProps;
}


export interface DateRangePickerProps {
  formatValue?: string;
  panelProps?: PanelProps;
}


export type PanelModeType = 'month' | 'date';

export type DatePickerCoreEmits = {
  (e: 'change-month', month: number, year: number): void;
}

export type DatePickerEmits = DatePickerCoreEmits & {};

export interface HeaderPanelProps {
  panelMode:  PanelModeType;
  panelMonth: Dayjs;
  toPrevMonth: () => void;
  toNextMonth: () => void;
  toThisMonth: () => void;
  onSetPanelMode: (panelMode: PanelModeType) => void;
}


export interface DatePanelProps {
  panelMonth: Dayjs
  value?: number;
  values?: (number | null)[];
  holidayMap?: HolidayMap
  onSelectCell: (cell: DateCellMeta) => void;
}
