
import type { ModelRef, Ref, EmitFn } from 'vue'
import { ref, computed, reactive } from 'vue';
import type {
  DateCellMeta,
  DatePickerCoreEmits,
  DatePickerEmits,
  PanelModeType,
} from './interface.ts'
import dayjs, { type Dayjs } from 'dayjs'


function useMonthActions(panelMonth: Ref<Dayjs>, emits: DatePickerCoreEmits) {
  // 面板模式与月份
  const panelMode = ref<PanelModeType>('date');
  // 面板当前月份
  function toPrevMonth() {
    panelMonth.value =
      panelMode.value === 'date'
        ? panelMonth.value.subtract(1, 'month')
        : panelMonth.value.subtract(1, 'year')
    emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
  }
  function toNextMonth() {
    panelMonth.value =
      panelMode.value === 'date' ? panelMonth.value.add(1, 'month') : panelMonth.value.add(1, 'year')
    emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
  }
  function toThisMonth() {
    panelMonth.value = dayjs().startOf('month')
    emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
  }

  function onSelectMonth(monthDayjs: Dayjs) {
    panelMonth.value = monthDayjs
    panelMode.value = 'date'
    emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
  }
  function onSetPanelMode(panelModeValue: PanelModeType) {
    console.log('onSelectMonth', panelModeValue)
    panelMode.value = panelModeValue;
  }
  return {
    panelMode: computed(() => panelMode.value),
    toPrevMonth,
    toNextMonth,
    toThisMonth,
    onSelectMonth,
    onSetPanelMode,
  }
}

export function useDatePickerPanel(modelValue: ModelRef<number | undefined>, emits: DatePickerEmits) {
  // 面板当前月份
  const panelMonth = ref<Dayjs>((modelValue.value ? dayjs(modelValue.value) : dayjs()).startOf('month'));
  const monthActions = useMonthActions(panelMonth, emits);

  // [事件]选择日期
  function onSelectCell(cell: DateCellMeta) {
    modelValue.value = cell.dateTimestamp
    console.log('onSelectCell', cell, modelValue.value)
    if (!cell.inCurrentMonth) {
      panelMonth.value = dayjs(cell.dateTimestamp).startOf('month')
      emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
    }
  }
  return {
    value: computed(() => modelValue.value),
    panelMonth: computed(() => panelMonth.value),
    onSelectCell,
    ...monthActions,
  }
}
