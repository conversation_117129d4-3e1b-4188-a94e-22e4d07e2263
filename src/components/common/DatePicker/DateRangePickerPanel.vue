<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import dayjs, { Dayjs } from 'dayjs'
// @ts-ignore
import LunarCalendar from 'lunar-calendar'
import { DatePanel, MonthPanel } from '@/components/common/DatePicker/panel'
import type { DateCellMeta } from './interface.ts';
type HolidayType = 'off' | 'work'
interface HolidayMap {
  [yyyyMMdd: string]: HolidayType
}
type TimestampOrNull = number | null
type DateRangeTuple = [TimestampOrNull, TimestampOrNull]

const props = withDefaults(
  defineProps<{
    modelValue?: DateRangeTuple
    holidayMap?: HolidayMap
  }>(),
  {
    modelValue: () => [null, null],
    holidayMap: () => ({}),
  },
)

// const modelValue = defineModel<{start: DateValueType; end: DateValueType}>({
//   default:() => ({ start: null, end: null })
// })
const emits = defineEmits<{
  'update:modelValue': [val: DateRangeTuple]
  'change-month': [year: number, month: number]
}>()

const today = dayjs().startOf('day')
const start = ref<number | null>(props.modelValue?.[0] ?? null)
const end = ref<number | null>(props.modelValue?.[1] ?? null)

watch(
  () => props.modelValue,
  (v) => {
    start.value = v?.[0] ?? null
    end.value = v?.[1] ?? null
  },
  { deep: true },
)

const panelMode = ref<'date' | 'month'>('date')
const panelMonth = ref<Dayjs>((start.value ? dayjs(start.value) : today).startOf('month'))
const rightPanelMonth = ref<Dayjs>(panelMonth.value.add(1, 'month'))

// function fmtYMD(d: Dayjs): string {
//   return d.format('YYYYMMDD')
// }
// function lunarText(d: Dayjs): string {
//   const l = LunarCalendar.solarToLunar(d.year(), d.month() + 1, d.date())
//   if (fmtYMD(d) === today.format('YYYYMMDD')) return l.lunarDayName
//   if (l.lunarFestival) return l.lunarFestival
//   return l.lunarDayName
// }

// function buildCells(monthBase: Dayjs): DateCellMeta[] {
//   const startOfMonth = monthBase.startOf('month')
//   const startDay = startOfMonth.subtract(startOfMonth.day(), 'day')
//   const arr: DateCellMeta[] = []
//   for (let i = 0; i < 42; i += 1) {
//     const d = startDay.add(i, 'day')
//     const key = fmtYMD(d)
//     arr.push({
//       dateTimestamp: d.valueOf(),
//       inCurrentMonth: d.month() === monthBase.month(),
//       isToday: d.isSame(today, 'day'),
//       day: d.date(),
//       lunarText: lunarText(d),
//       marker: props.holidayMap?.[key],
//     })
//   }
//   return arr
// }

const titleTextLeft = computed(() =>
  panelMode.value === 'date'
    ? `${panelMonth.value.year()}年${panelMonth.value.month() + 1}月`
    : `${panelMonth.value.year()}`,
)
const titleTextRight = computed(() =>
  panelMode.value === 'date'
    ? `${rightPanelMonth.value.year()}年${rightPanelMonth.value.month() + 1}月`
    : `${rightPanelMonth.value.year()}`,
)
// const cellsLeft = computed(() => buildCells(panelMonth.value))
// const cellsRight = computed(() => buildCells(rightPanelMonth.value))
const months = computed(() =>
  Array.from({ length: 12 }, (_, i) => ({ label: `${i + 1}月`, value: i })),
)


// function inRange(ts: number): boolean {
//   if (start.value == null || end.value == null) return false
//   const a = Math.min(start.value, end.value)
//   const b = Math.max(start.value, end.value)
//   return ts >= a && ts <= b
// }

function selectDate(cell: DateCellMeta) {
  if (start.value != null && end.value == null) {
    end.value = cell.dateTimestamp
    if (start.value > end.value) {
      const t = start.value
      start.value = end.value
      end.value = t
    }
  } else {
    start.value = cell.dateTimestamp
    end.value = null
  }
  console.log('selectDate', cell, start.value, end.value);
  emits('update:modelValue', [start.value, end.value])
  // 双月视图下无需自动跳到该月
}

function selectMonth(monthDayjs: Dayjs) {
  panelMonth.value = monthDayjs;
  panelMode.value = 'date'
  emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
}
function toPrev() {
  panelMonth.value =
    panelMode.value === 'date'
      ? panelMonth.value.subtract(1, 'month')
      : panelMonth.value.subtract(1, 'year')
  emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
}
function toNext() {
  panelMonth.value =
    panelMode.value === 'date' ? panelMonth.value.add(1, 'month') : panelMonth.value.add(1, 'year')
  emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
}
function toThisMonth() {
  panelMonth.value = today.startOf('month')
  emits('change-month', panelMonth.value.year(), panelMonth.value.month() + 1)
}

// 独立控制右侧面板月份
function selectMonthRight(monthDayjs: Dayjs) {
  rightPanelMonth.value = monthDayjs;
  panelMode.value = 'date'
  emits('change-month', rightPanelMonth.value.year(), rightPanelMonth.value.month() + 1)
}
function toPrevRight() {
  rightPanelMonth.value =
    panelMode.value === 'date'
      ? rightPanelMonth.value.subtract(1, 'month')
      : rightPanelMonth.value.subtract(1, 'year')
  emits('change-month', rightPanelMonth.value.year(), rightPanelMonth.value.month() + 1)
}
function toNextRight() {
  rightPanelMonth.value =
    panelMode.value === 'date'
      ? rightPanelMonth.value.add(1, 'month')
      : rightPanelMonth.value.add(1, 'year')
  emits('change-month', rightPanelMonth.value.year(), rightPanelMonth.value.month() + 1)
}
function toThisMonthRight() {
  rightPanelMonth.value = today.startOf('month')
  emits('change-month', rightPanelMonth.value.year(), rightPanelMonth.value.month() + 1)
}

onMounted(() => {
  if (!start.value && !end.value) {
    panelMonth.value = today.startOf('month')
  }
})
</script>

<template>
  <div class="date-range">
    <div class="panel">
      <div class="header">
        <div class="title" @click="panelMode = panelMode === 'date' ? 'month' : 'date'">
          {{ titleTextLeft }}
        </div>
        <div class="nav">
          <div class="btn u-hover" @click="toPrev"><t-icon class="i-u-left"></t-icon></div>
          <div class="btn u-hover" title="回到本月" @click="toThisMonth">○</div>
          <div class="btn u-hover" @click="toNext"><t-icon class="i-u-right"></t-icon></div>
        </div>
      </div>
      <DatePanel v-if="panelMode === 'date'"
                 :panel-month="panelMonth"
                 :values="[start, end]"
                 @select="selectDate" />
      <MonthPanel v-else-if="panelMode === 'month'"
                  :panel-month="panelMonth"
                  @select="selectMonth" />
    </div>
    <div class="panel">
      <div class="header">
        <div class="title" @click="panelMode = panelMode === 'date' ? 'month' : 'date'">
          {{ titleTextRight }}
        </div>
        <div class="nav">
          <div class="btn u-hover" @click="toPrevRight"><t-icon class="i-u-left"></t-icon></div>
          <div class="btn u-hover" title="回到本月" @click="toThisMonthRight">○</div>
          <div class="btn u-hover" @click="toNextRight"><t-icon class="i-u-right"></t-icon></div>
        </div>
      </div>
      <DatePanel v-if="panelMode === 'date'"
                 :panel-month="rightPanelMonth"
                 :values="[start, end]"
                 @select="selectDate" />
      <MonthPanel v-else-if="panelMode === 'month'"
                  :panel-month="rightPanelMonth"
                  @select="selectMonthRight" />
    </div>
  </div>
</template>

<style scoped lang="less">
.date-range {
  display: grid;
  grid-template-columns: 270px 270px;
  gap: 10px;
  color: var(--text-color);
}
.panel {
  width: 270px;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 6px 6px 8px;
}
.title {
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
}
.nav {
  display: flex;
  align-items: center;
  gap: 6px;
}
.btn {
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--color-neutral-6);
  cursor: pointer;
}
.btn:hover {
  background: var(--color-fill-2);
}

.weeks {
  display: grid;
  grid-template-columns: repeat(7, calc(37px * 1.06));
  padding: 2px 4px;
  color: var(--color-neutral-6);
  font-size: 12px;
  text-align: center;
}
.grid {
  display: grid;
  grid-template-columns: repeat(7, 37px);
  grid-auto-rows: 36px;
  gap: 2px 0;
  padding: 0 6px 6px;
}
.cell {
  position: relative;
  //border-radius: 6px;
  padding: 2px 4px;
  cursor: pointer;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.cell .range {
  display: none;
}
.cell:hover {
  background-color: var(--u-bg-color-3-hover);
}
.cell.other {
  color: var(--color-neutral-6);
}
.cell.today {
  background-color: rgba(var(--blue-4), 0.1);
  color: rgb(var(--blue-7));
}
.cell.inrange {
  background-color: rgba(0, 137, 255, 0.12);
  &.other {
    color: var(--color-neutral-6);
    background-color: transparent;
  }
}

.cell.start,
.cell.end {
  background-color: rgba(var(--blue-6));
  color: #fff;
  &.other {
    color: var(--color-neutral-6);
    background-color: transparent;
  }
}

.cell.start {
  border-radius: 10px 0 0 10px;
}

.cell.end {
  border-radius: 0 10px 10px 0;
}
.cell.start.end {
  border-radius: 10px;
}
.day {
  font-size: 12px;
}
.lunar {
  font-size: 8px;
  padding-top: 1px;
  color: #c3c3c3;
}
.corner {
  position: absolute;
  right: 4px;
  top: 4px;
}
.tag {
  position: absolute;
  top: -6px;
  right: -6px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  font-size: 7px;
  color: #fff;
}
.tag.off {
  background: #00b578;
}
.tag.work {
  background: #ff4d4f;
}

.month-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-auto-rows: 56px;
  gap: 8px 12px;
  padding: 8px 6px 10px;
}
.mcell {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
  cursor: pointer;
}
.mcell > span {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 36px;
  border-radius: 20px;
}
.mcell.current > span {
  outline: 1px solid #0089ff;
}
.mcell.active > span {
  background: rgb(var(--blue-6));
  color: #fff;
}
</style>
