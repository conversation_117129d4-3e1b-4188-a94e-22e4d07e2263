<script setup lang="ts">
import type { DatePickerPanelProps, DateValueType, DatePickerEmits } from './interface.ts'
import DatePickerBasePanel from './DatePickerBasePanel.vue'
import { ref, watch } from 'vue'
import dayjs from 'dayjs'

const props = withDefaults(defineProps<DatePickerPanelProps>(), {
  formatValue: 'YYYY-MM-DD',
})

const modalValue = defineModel<DateValueType>('modalValue');
// 内部时间戳，驱动基础面板（基础面板使用时间戳）
const innerTimestamp = ref<number | undefined>(undefined)

// 外 -> 内：将外部值解析为时间戳
watch(
  () => modalValue.value,
  (val) => {
    if (val == null || val === '') {
      innerTimestamp.value = undefined
      return
    }
    if (typeof val === 'number') {
      innerTimestamp.value = val
      return
    }
    const d = props.formatValue ? dayjs(String(val), props.formatValue) : dayjs(String(val))
    innerTimestamp.value = d.isValid() ? d.valueOf() : undefined
  },
  { immediate: true },
)

// 内 -> 外：将基础面板返回的时间戳，按 formatValue 输出到外部
watch(
  () => innerTimestamp.value,
  (ts) => {
    if (ts == null) {
      modalValue.value = null
      return
    }
    modalValue.value = props.formatValue ? dayjs(ts).format(props.formatValue) : ts
  },
)
</script>

<template>
  <div>
    <DatePickerBasePanel v-model:modal-value="innerTimestamp"
                         v-bind="props.panelProps"  />
  </div>
</template>
<style scoped lang="less"></style>
