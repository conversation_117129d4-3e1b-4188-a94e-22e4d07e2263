import { ref, useTemplateRef } from 'vue';
import type { TaskListParams } from '@xiaou66/todo-plugin';


export interface AiTaskDrawerInstance {
  show: (params?: TaskListParams) => void;
}
const aiTaskDrawerInstance = ref<AiTaskDrawerInstance>()
export function useAiTaskDrawer(): AiTaskDrawerInstance {
  return aiTaskDrawerInstance.value as AiTaskDrawerInstance;
}
export function registerAiTaskDrawerInstance(instance: AiTaskDrawerInstance) {
  aiTaskDrawerInstance.value = instance;
}
