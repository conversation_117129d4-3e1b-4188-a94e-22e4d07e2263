<script setup lang="ts">
import { onMounted, ref, computed, useTemplateRef, nextTick } from 'vue';
import { useEscModal } from '@/hooks/useModal.ts';
import type { IAiPrompt } from '@xiaou66/todo-plugin';
import { ExtensionManager } from '@/extension';
import { Message } from '@arco-design/web-vue';
import type { InputInstance } from '@arco-design/web-vue';
import { cloneDeep } from 'es-toolkit';

const emits = defineEmits<{
  close: []
}>();
const visible = ref<boolean>(false);
const { stopEsc, startEsc } = useEscModal();
function show() {
  visible.value = true;
  request();
  startEsc()
}

defineExpose({
  show,
});
const aiPromptList = ref<IAiPrompt[]>([]);
async function request() {
  aiPromptList.value = await ExtensionManager.getAiInstance().getPromptList();
}

function handleAddPrompt() {
  aiPromptList.value?.unshift({
    id: '',
    name: '',
    content: '',
    useFor: 'summary',
    sort: 0,
    updateAt: 0
  });
  enterEdit('');
}
const editorPromptNameRef = useTemplateRef<InputInstance>('editorPromptNameRef')
function enterEdit(id: string, autoTitleFocus = true) {
  if (id === editorPromptId.value) {
    return;
  }
  editorPromptId.value = id;
  if (!active.value.includes(id)) {
    active.value = [id];
  }

  if (autoTitleFocus) {
    nextTick(() => {
      if (editorPromptNameRef.value && editorPromptNameRef.value.length) {
        editorPromptNameRef.value[0].focus();
      }
    })
  }
}
const active = ref<string[]>([]);
const editorPromptId = ref<string>();
const isNew = computed(() => {
  return !!aiPromptList.value.find(item => item.id === '');
});
async function savePrompt(aiPrompt: IAiPrompt) {
  if (!aiPrompt.name) {
    Message.warning("请输入提示词名称");
    return;
  }
  await ExtensionManager.getAiInstance().savePrompt(cloneDeep(aiPrompt));
  request();
  editorPromptId.value = undefined;
}

function deletePrompt(aiPrompt: IAiPrompt) {
  ExtensionManager.getAiInstance().deletePrompt(aiPrompt.id);
  request();
  if (active.value.includes(aiPrompt.id)) {
    active.value = [];
  }
}

function handleClose() {
  aiPromptList.value.length = 0;
  stopEsc();
  emits('close');
}
</script>

<template>
  <a-modal v-model:visible="visible"
           title="提示词管理"
           :body-style="{padding: '6px'}"
           :footer="false"
           unmount-on-close
           @close="handleClose">
    <div class="u-fx" style="justify-content: end; padding: 0 10px">
      <a-button size="mini"
                class="u-transparent"
                @click="handleAddPrompt"
                :disabled="isNew">
        <template #icon>
          <div class="u-fx u-fac">
            <iconpark-icon name="plus"></iconpark-icon>
          </div>
        </template>
        添加
      </a-button>
    </div>
    <a-scrollbar style="min-height: 200px; max-height: 460px; overflow-y: auto">
      <a-collapse v-model:active-key="active" :accordion="true">
        <a-collapse-item v-for="(aiPrompt) in aiPromptList"
                         :key="aiPrompt.id">
          <template #header>
            <span  v-if="aiPrompt.id !== editorPromptId">{{ aiPrompt.name }}</span>
            <a-tooltip v-else content="提示词名称" mini>
              <a-input
                       ref="editorPromptNameRef"
                       v-model:model-value="aiPrompt.name"
                       size="mini"
                       @click.stop >
              </a-input>
            </a-tooltip>
          </template>
          <template #extra>
            <a-popconfirm content="确认删除这个提示词吗?" @ok="deletePrompt(aiPrompt)">
              <a-link v-if="aiPrompt.id"
                      status="danger"
                      style="font-size: 12px"
                      @click.stop>删除</a-link>
            </a-popconfirm>
            <a-link v-if="editorPromptId === aiPrompt.id"
                    style="font-size: 12px"
                    @click.stop="savePrompt(aiPrompt)">保存</a-link>
            <a-link v-else
                    style="font-size: 12px"
                    @click.stop="enterEdit(aiPrompt.id)">编辑</a-link>
          </template>
          <a-textarea v-model:model-value="aiPrompt.content"
                      style="height: 300px; max-height: 300px; overflow-y: auto"
                      :auto-size="true"
                      @focus="enterEdit(aiPrompt.id, false)">
          </a-textarea>
        </a-collapse-item>
      </a-collapse>
    </a-scrollbar>
  </a-modal>
</template>

<style scoped lang="less">
.arco-modal-body {
  padding:  6px;
}
</style>
