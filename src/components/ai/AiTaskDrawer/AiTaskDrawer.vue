<script setup lang="ts">
import { registerAiTaskDrawerInstance } from './AiTaskDrawer.ts';
import { computed, onMounted, provide, ref, useTemplateRef, watch } from 'vue';
import { ExtensionManager } from '@/extension';
import type {
  IAiPrompt,
  IGroupInfo,
  ITaskItem,
  ITaskTag,
  TaskListBatchParams,
  TaskSaveParams,
} from '@xiaou66/todo-plugin';
import type { ITaskViewProvide } from '@/components/Tasks';
import {
  TaskCreateInput,
  TaskItem,
  taskViewProvideCode,
  useSaveTaskItem,
  useTaskDetailDrawer,
} from '@/components/Tasks';
import markdownit from 'markdown-it';
import html2md from 'html-to-md';
import { convert } from 'html-to-text';
import { EditorTiptap, type EditorTiptapInstance } from '@/components/editor';
import JsUtil from '@/utils/jsUtil.ts';
import { Message } from '@arco-design/web-vue';
import type { ShortcutType } from '@arco-design/web-vue';
import dayjs from 'dayjs';
import { cloneDeep, keyBy, pickBy, isString } from 'es-toolkit';
import type { ChatMessageParam } from '@/ai';
import { isAiServiceModel, useAiChat } from '@/ai';
import PromptManager from '@/components/ai/AiTaskDrawer/PromptManager.vue';
import { useEscModal } from '@/hooks/useModal.ts';
import { useEventListener } from '@vueuse/core';
import { useLocalData } from '@/hooks/dataHooks.ts';
import type { TaskListParams } from '@xiaou66/todo-plugin';
import { TASK_ITEM_DESC } from "@xiaou66/todo-plugin";
import { AI_SERVICE_MODEL_SEPARATOR } from '@/ai/AiConstant.ts';
import { LazyLoader } from '@/components/common/container/LazyLoader';

const visible = defineModel<boolean>('visible');
const { stopEsc, startEsc } = useEscModal();


function show(params?: TaskListParams) {
  startEsc();
  visible.value = true;
  searchParams.value = {
    ...params,
    searchTypeList: ['finish', 'default'],
  }
  ExtensionManager.getGroupInstance()
    .listGroup({})
    .then((res) => {
      groupList.value = res.list;
    });
  ExtensionManager.getTaskTagInstance()
    .listTag()
    .then((res) => {
      taskTagList.value = res;
    });

  requestPromptList().then(() => {
    currentPromptId.value = ExtensionManager.getLocalStorageInstance().getData(
      'ai/taskSummary/currentPromptId',
    );
  });
}

function hide() {
  visible.value = false;
}
function handleAiTaskDrawerClose() {
  stopEsc();
  clearData();
}

function clearData() {
  // 清理全部数据
  groupList.value = [];
  taskTagList.value = [];
  taskItemList.value = [];
  aiPromptList.value = [];
  excludeTaskIdList.value =[];
  reasonContent.value = '';
  searchParams.value = {
    taskTimeRange: [],
    groupId: '',
    tagNameList: [],
    searchTypeList: ['finish', 'default'],
  };
}

const searchParams = ref<TaskListBatchParams>({
  taskTimeRange: [],
  groupId: '',
  tagNameList: [],
  searchTypeList: ['finish', 'default'],
});
const taskTagList = ref<ITaskTag[]>();
const groupList = ref<IGroupInfo[]>([]);
const groupMap = computed<Record<string, IGroupInfo>>(() => {
  return keyBy(groupList.value, (item) => item.groupId);
});
const taskItemList = ref<ITaskItem[]>([]);

function requestTaskList() {
  ExtensionManager.getTaskInstance()
    .listTaskBatch({ ...cloneDeep(searchParams.value) })
    .then((list) => {
      console.log('result', list);
      taskItemList.value = list;
    });
}
watch(
  () => searchParams.value,
  () => {
    requestTaskList();
  },
  { deep: true },
);

const excludeTaskIdList = ref<string[]>([]);
const aiIncludeCount = computed(() => {
  return taskItemList.value.length - excludeTaskIdList.value.length;
})
function handleSwitchTask(taskItem: ITaskItem) {
  const index = excludeTaskIdList.value.indexOf(taskItem.taskId);
  if (index === -1) {
    excludeTaskIdList.value.push(taskItem.taskId);
  } else {
    excludeTaskIdList.value.splice(index, 1);
  }
}
registerAiTaskDrawerInstance({
  show,
});
const editorTiptapRef = useTemplateRef<EditorTiptapInstance>('editorTiptapRef');

const shortcuts: ShortcutType[] = [
  {
    label: '今天',
    value: [dayjs(), dayjs()],
  },
  {
    label: '昨天',
    value: [dayjs().subtract(1, 'days'), dayjs().subtract(1, 'days')],
  },
  {
    label: '本周',
    value: [dayjs().startOf('week'), dayjs().endOf('week')],
  },
  {
    label: '上周',
    value: [dayjs().subtract(1, 'week').startOf('week'), dayjs().subtract(1, 'week').endOf('week')],
  },
  {
    label: '上月',
    value: [
      dayjs().subtract(1, 'month').startOf('month'),
      dayjs().subtract(1, 'month').endOf('month'),
    ],
  },
];

function handleGroupSelect(value: string) {
  searchParams.value.groupId = value;
}

const md = markdownit();
const reasonContent = ref<string>('');
const aiRobotRef = useTemplateRef<HTMLDataElement>('aiRobotRef');
const { aiCurrentAiModel, aiAskStatus, aiServiceList, aiAskAbort, aiAskChat } = useAiChat('all', {
  onAppend(data, reasoningContent) {
    if (reasoningContent) {
      // 不处理思考
      reasonContent.value = data;
      if (aiRobotRef.value) {
        aiRobotRef.value.scrollTop = aiRobotRef.value.scrollHeight;
      }
      return;
    }
    reasonContent.value = '';
    editorTiptapRef.value?.setContent(md.render(data));
  },
  onEnd() {
    reasonContent.value = '';
  },
  onAborted() {
    reasonContent.value = '';
  },
});

const joinTaskDesc = useLocalData('ai/taskSummary/joinTaskDesc', false);
async function handleAskChat() {
  if (!currentPrompt.value) {
    Message.warning('请选择提示词');
    return;
  }
  const aiTaskList = cloneDeep(
    taskItemList.value.filter(({ taskId }) => !excludeTaskIdList.value.includes(taskId)),
  ) as ITaskItem[] & { content?: string };
  if (aiTaskList.length === 0) {
    Message.warning('请选择任务');
    return;
  }
  if (joinTaskDesc.value) {
    await Promise.all(aiTaskList.map(async (item) => {
      const content = await ExtensionManager.getTaskInstance().getTaskDesc(item.taskId) || '';
      item.content = md.render(content).replace(/<[^>]+>/g, '')
        .replace(/&lt;p&gt;/g, '')
      return item;
    }));
  }

  const fieldNameList = Object.keys(TASK_ITEM_DESC);
  const fieldDesc = fieldNameList.map(key => `${key}:${TASK_ITEM_DESC[key]}`).join("");
  const aiTaskPickList = aiTaskList.map(item =>
    pickBy(item, (value, key) => isString(key) && fieldNameList.includes(key)))
  const messages: ChatMessageParam[] = [
    {
      role: 'system',
      content: `输入形式:每一个事项将以 json 的方式进行传递, 对提供json字段进行描述key:描述${fieldDesc}禁止使用json字符串中key值输出均根据描述进行翻译, 任何情况下不允许输出json中的字段,中间值的设定:用时使用finishTime-taskStartTime`,
    },
    {
      role: 'system',
      content: currentPrompt.value.content,
    },
    {
      role: 'user',
      content: `当前日期是${dayjs().format('YYYY-MM-DD')}`,
    },
    {
      role: 'user',
      content: `事项json字符串:${JSON.stringify(aiTaskPickList)}`,
    },
  ];
  aiAskChat(messages);
}
function aiChatCurrentModelChange(value: string) {
  ExtensionManager.getLocalStorageInstance().setData('ai/taskSummary/currentModel', value);
}
onMounted(() => {
  aiCurrentAiModel.value = ExtensionManager.getLocalStorageInstance().getData(
    'ai/taskSummary/currentModel',
  );
});

const promptManagerRef = useTemplateRef<{ show: () => void }>('promptManagerRef');
const aiPromptList = ref<IAiPrompt[]>([]);
const currentPromptId = ref<string>('');
const currentPrompt = computed(() => {
  return aiPromptList.value.find((item) => item.id === currentPromptId.value);
});
// 刷新提示词列表
async function requestPromptList() {
  aiPromptList.value = await ExtensionManager.getAiInstance().getPromptList();
  if (currentPromptId.value) {
    const promptItem = aiPromptList.value.find((item) => item.id === currentPromptId.value);
    if (!promptItem) {
      if (aiPromptList.value.length) {
        handlePromptChange(aiPromptList.value[0].id);
      } else {
        handlePromptChange('');
      }
    }
  }
}
function handlePromptChange(id: string) {
  currentPromptId.value = id;
  ExtensionManager.getLocalStorageInstance().setData('ai/taskSummary/currentPromptId', id);
}

const saveTask = useSaveTaskItem();
provide<ITaskViewProvide>(taskViewProvideCode, {
  refreshData: requestTaskList,
  getScene() {
    return 'default';
  },
  saveTask,
  dragStart: () => {},
  dragEnd(sourceTask, targetTask, newTask, type) {},
});

function handleAddTask(createTask: TaskSaveParams) {
  saveTask(createTask);
}
useEventListener(window, 'taskList::refreshAll', () => {
  if (visible.value) {
    requestTaskList();
  }
});

const aiCopyType = useLocalData<'text' | 'md'>('ai/copyType', 'md');
function handleCopyContent() {
  const html = editorTiptapRef.value?.exportHtml() || '';
  let text = '';
  if (aiCopyType.value === 'text') {
    text = convert(html, {
      preserveNewlines: true,
      wordwrap: false,
      decodeEntities: true,
    })
      .split('\n') // 按行分割
      .filter((line) => line.trim() !== '') // 过滤掉空行
      .join('\n');
  } else if (aiCopyType.value === 'md') {
    text = html2md(html);
  }
  utools.copyText(text);
  Message.success('复制成功');
}
function handleAiCopyText(value: 'text' | 'md') {
  aiCopyType.value = value;
  handleCopyContent();
}

const createDefaultConfig = computed(() => {
  const config: Partial<TaskSaveParams> = { taskStatus: 100 };
  if (searchParams.value.taskTimeRange && searchParams.value.taskTimeRange.length === 2) {
    config.taskStartDate = dayjs(searchParams.value.taskTimeRange[1]).format('YYYY-MM-DD')
  }
  return config;
});

// fix 时间选择器快捷选择直接关闭弹框
const taskRangeRangePickerVisible = ref(false);
function handleClickSelectShortcut() {
  taskRangeRangePickerVisible.value = false;
}

const utoolsCustomModel = utools.redirectAiModelsSetting
</script>

<template>
  <PromptManager ref="promptManagerRef"
                 @close="requestPromptList" />
  <a-drawer
    width="100%"
    v-model:visible="visible"
    unmount-on-close
    :footer="false"
    @close="handleAiTaskDrawerClose"
  >
    <template #header>
      <a-page-header @back="hide" title="AI 助手" subtitle="AI 洞见，任务尽在掌控"> </a-page-header>
    </template>
    <div class="ai-task-drawer">
      <div class="u-h-full left">
        <div class="u-fx u-fac header">
          <a-trigger trigger="click"
                     v-model:popup-visible="taskRangeRangePickerVisible">
            <a-button size="small" class="u-transparent">
              {{ JsUtil.dateRangeFormatLabel(searchParams.taskTimeRange) || '日期' }}
            </a-button>
            <template #content>
              <div style="min-width: 500px">
                <a-range-picker
                  @select-shortcut="handleClickSelectShortcut"
                  v-model:model-value="searchParams.taskTimeRange"
                  hide-trigger
                  :shortcuts="shortcuts"
                />
              </div>
            </template>
          </a-trigger>
          <a-dropdown class="min-dropdown-select" @select="handleGroupSelect">
            <a-button size="small" class="u-transparent">
              {{
                searchParams.groupId
                  ? groupMap[searchParams.groupId]?.groupName || '未分组'
                  : '分组'
              }}
            </a-button>
            <template #content>
              <div class="min-dropdown-select-options">
                <a-doption v-if="!!searchParams.groupId" value="">全部</a-doption>
                <a-doption v-if="searchParams.groupId !== 'collectBox'" value="collectBox">未分组</a-doption>
                <a-doption v-show="searchParams.groupId !== group.groupId"
                           v-for="group in groupList"
                           :key="group.groupId"
                           :value="group.groupId">
                  {{ group.groupName }}
                </a-doption>
              </div>
            </template>
          </a-dropdown>
          <a-select
            v-model:model-value="searchParams.tagNameList"
            class="u-transparent"
            style="width: 100%"
            placeholder="所有标签"
            size="small"
            :max-tag-count="1"
            multiple
            :trigger-props="{ contentClass: 'u-select-options min' }"
          >
            <a-option v-for="taskTag in taskTagList" :value="taskTag.name" :key="taskTag.id">
              {{ taskTag.name }}
            </a-option>
          </a-select>
        </div>
        <a-scrollbar style="max-height: calc(100vh - 154px); overflow-y: auto">
          <div class="body" style="max-height: 100%">
            <div v-if="taskItemList.length">
              <LazyLoader h="48px">
                <TaskItem
                  :className="{
                  excludeTask: excludeTaskIdList.includes(taskItem.taskId),
                  'task-custom-item': true,
                }"
                  v-for="taskItem in taskItemList"
                  :key="taskItem.taskId"
                  :taskItem="taskItem"
                  showType="default"
                  disableDrag
                  @click="useTaskDetailDrawer().show(taskItem)"
                  @save="saveTask"
                  :showCustomTaskMain="excludeTaskIdList.includes(taskItem.taskId)"
                >
                  <template #custom-task-main="{taskItem}">
                    <div class="u-fx u-fac u-f-between" style="height: 41px;">
                      <div></div>
                      <div style="text-align: center;">
                        <div style="color: rgb(var(--orange-6))">当前任务不参与 AI 分析</div>
                        <div class="u-font-size-smail">{{taskItem.taskTitle}}</div>
                      </div>
                      <div>
                        <a-tooltip content="参与">
                          <a-button size="small" class="u-transparent" @click.stop="handleSwitchTask(taskItem)">
                            <template #icon>
                              <iconpark-icon
                                name="preview-close"
                                :size="15"
                              />
                            </template>
                          </a-button>
                        </a-tooltip>
                      </div>
                    </div>
                  </template>
                  <template #titleSuffix>
                    <div class="ai-action" style="height: 15px">
                      <a-link @click.stop="handleSwitchTask(taskItem)">
                        <a-tooltip
                          mini
                          content="不参与"
                        >
                          <iconpark-icon
                            v-if="!excludeTaskIdList.includes(taskItem.taskId)"
                            name="eyes"
                            :size="15"
                          />
                          <iconpark-icon v-else name="preview-close"></iconpark-icon>
                        </a-tooltip>
                      </a-link>
                    </div>
                  </template>
                </TaskItem>
              </LazyLoader>
            </div>
            <div v-else>
              <a-empty> 暂无任务 </a-empty>
            </div>
          </div>
        </a-scrollbar>
        <div class="u-h-full footer">
          <TaskCreateInput
            placeholder="回车创建补漏任务"
            :createDefaultConfig="createDefaultConfig"
            :disableModule="[]"
            direction="top"
            @create="handleAddTask"
          />
        </div>
      </div>
      <div class="u-h-full right">
        <div class="header">
          <div class="u-fx u-fac u-f-between">
            <div class="u-fx u-fac u-gap5 title">
              <iconpark-icon name="ai" size="20" color="#9686F8"></iconpark-icon>
              <div>AI 生成区</div>
            </div>
            <div style="padding-right: 10px">
              <!--region 模型选择器-->
              <a-select
                v-model:model-value="aiCurrentAiModel"
                size="small"
                class="u-transparent"
                style="width: 170px"
                @change="aiChatCurrentModelChange"
              >
                <template v-if="!!utoolsCustomModel" #footer>
                  <div class="u-fx u-f-between" style="padding: 4px 6px">
                      <div></div>
                      <div v-if="!!utoolsCustomModel">
                          <a-link @click="utoolsCustomModel">
                              增加模型
                          </a-link>
                      </div>
                  </div>
                </template>
                <a-optgroup
                  v-for="aiService in aiServiceList"
                  :key="aiService.id"
                  :label="aiService.name"
                >
                  <a-option
                    v-for="aiModel in aiService.models"
                    :key="isAiServiceModel(aiModel) ? aiService.id : aiModel"
                    :label="isAiServiceModel(aiModel) ? aiModel.label : aiModel"
                    :value="
                      isAiServiceModel(aiModel)
                        ? `${aiService.id}${AI_SERVICE_MODEL_SEPARATOR}${aiModel.id}`
                        : `${aiService.id}${AI_SERVICE_MODEL_SEPARATOR}${aiModel}`
                    "
                  >
                    <span>{{ isAiServiceModel(aiModel) ? aiModel.label : aiModel }}</span>
                    <span
                      v-if="isAiServiceModel(aiModel) && !!aiModel.cost"
                      style="padding-left: 6px; color: #9488f8"
                    >
                      <span v-if="aiModel.cost">{{ aiModel.cost }}</span>
                      <iconpark-icon color="#9488F8" name="Ai21-hbge2c07" />
                    </span>
                  </a-option>
                </a-optgroup>
              </a-select>
              <!--endregion-->
            </div>
          </div>
          <a-divider style="margin: 6px 0"></a-divider>
        </div>
        <div class="u-h-full"
             style="max-height: calc(100vh - 154px); overflow-y: auto">
          <EditorTiptap
            ref="editorTiptapRef"
            hideToolBar
            placeholder="根据提供数据, 让 AI 助你成就每一天！"
          />
        </div>
        <div class="u-fx u-pos-rel u-fac u-f-between"
             style="padding: 10px; border-radius: 10px">
          <!--  AI 思考动画  -->
          <div v-if="reasonContent" class="u-fx u-pos-abs long-ai">
            <div><img class="ai" src="/ai-robot.png"></div>
            <div class="text" ref="aiRobotRef">{{ reasonContent }}</div>
          </div>
          <!--region 提示词选择-->
          <div>
            <a-select
              v-model:model-value="currentPromptId"
              size="small"
              class="u-transparent"
              style="width: 166px"
              placeholder="提示词"
              :trigger-props="{ contentClass: 'u-select-options small' }"
              @change="(value: any) => handlePromptChange(value)"
            >
              <template #footer>
                <div class="u-fx u-fc">
                  <a-button
                    size="mini"
                    class="u-transparent"
                    style="width: 100%"
                    @click="promptManagerRef?.show()"
                  >
                    管理提示词
                  </a-button>
                </div>
              </template>
              <a-option v-for="aiPrompt in aiPromptList" :key="aiPrompt.id" :value="aiPrompt.id">
                {{ aiPrompt.name }}
              </a-option>
            </a-select>
          </div>
          <!--endregion-->
          <div class="u-fx u-fac u-gap5">
            <a-dropdown
              trigger="hover"
              class="min-dropdown-select"
              @select="(value: any) => handleAiCopyText(value)"
            >
              <a-button
                v-if="!aiAskStatus"
                class="u-transparent"
                shape="round"
                style="width: 100px"
                @click="handleCopyContent"
              >
                <template #icon>
                  <div class="u-fx u-fac">
                    <iconpark-icon name="copy"></iconpark-icon>
                  </div>
                </template>
                复制
              </a-button>
              <template #content>
                <div class="min-dropdown-select-options">
                  <a-doption value="text">
                    <template #icon>
                      <iconpark-icon
                        v-if="aiCopyType === 'text'"
                        name="check"
                        style="color: var(--text-color)"
                      ></iconpark-icon>
                      <div v-else style="padding-left: 14px"></div>
                    </template>
                    纯文本
                  </a-doption>
                  <a-doption value="md">
                    <template #icon>
                      <iconpark-icon
                        v-if="aiCopyType === 'md'"
                        name="check"
                        style="color: var(--text-color)"
                      ></iconpark-icon>
                      <div v-else style="padding-left: 14px"></div>
                    </template>
                    markdown
                  </a-doption>
                </div>
              </template>
            </a-dropdown>
            <a-trigger v-if="!aiAskStatus" trigger="hover" :popup-offset="10">
              <template #content>
                <div class="ai-ask">
                  <a-checkbox v-model:model-value="joinTaskDesc">加入任务描述</a-checkbox>
                </div>
              </template>
              <a-button type="primary" shape="round" style="width: 100px"
                        @click="handleAskChat">
                <template #icon>
                  <div class="u-fx u-fac">
                    <iconpark-icon :size="16" name="magic-wand" />
                  </div>
                </template>
                生成
              </a-button>
            </a-trigger>
            <a-button
              v-else
              class="u-transparent"
              shape="round"
              style="width: 100px"
              @click="() => aiAskAbort()"
            >
              <template #icon>
                <div class="u-fx u-fac box-blue-3 ai-ripple-wrapper">
                  <div class="ai-ripple-circle"></div>
                  <div class="ai-ripple-circle"></div>
                  <div class="ai-ripple-circle"></div>
                  <iconpark-icon :size="16" name="magic-wand" />
                </div>
              </template>
              停止
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<style scoped lang="less">
.ai-ask {
  background: var(--color-bg-5);
  padding: 10px 12px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  width: 140px;
}
.task-custom-item {
  transition: opacity 200ms linear;
}
.excludeTask {
  position: relative;
  opacity: 0.7;
  :deep(.task-item){
    align-items: center;
  }
  :deep(.check-area) {
    display: none;
  }
}
:deep(.task-item) {
  &:hover .ai-action {
  }
  .ai-action {
    transition: opacity 300ms linear;
    opacity: 1;
  }
}

:deep(.arco-picker-input) {
  width: 100px;
}
:deep(.arco-page-header-wrapper) {
  padding-left: 0;
}
.ai-task-drawer {
  display: grid;
  height: calc(100vh - 48px);
  grid-template-columns: 50% 50%;
  grid-template-rows: 1fr;
  > div:first-child {
    padding: 10px;
  }
  .left {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 40px calc(100% - 86px) 46px;
    .header {
      gap: 10px;
      padding-bottom: 10px;
    }
    .body {
      border-radius: var(--border-radius-medium);
      background-color: var(--main-background-transparent);
    }
    .footer {
      padding: 6px;
    }
  }
  .right {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto calc(100vh - 170px) 60px;
    .header {
      padding: 10px 0px;
      .title {
        font-size: 18px;
      }
    }
  }
}

.ai-ripple-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  overflow: visible;
}

.ai-ripple-circle {
  position: absolute;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(22, 93, 255, 0.4), rgba(181, 55, 252, 0.4));
  animation: ai-ripple 1.2s ease-out infinite;

  &:nth-child(1) {
    opacity: 0;
  }

  &:nth-child(2) {
    animation-delay: 0.5s;
    opacity: 0;
  }
}

@keyframes ai-ripple {
  0% {
    transform: scale(1.2);
    opacity: 0;
  }
  100% {
    transform: scale(2.5);
    opacity: 1;
  }
}
:deep(.tiptap h2) {
  margin-top: 1.5rem;
}
:deep(.tiptap h3) {
  margin-top: 1.2rem;
}

.long-ai {
  z-index: 999;
  width: 95%;
  top: -60px;
  img {
    user-select: none;
    -webkit-user-drag: none;
  }
  .ai {
    width: 58px;
    height: auto;
    //left: 0px;
    transform: rotateY(180deg);
    animation: ai-robot 1.2s ease-out infinite;
  }
  .text {
    flex-direction: column-reverse;
    padding-left: 10px;
    font-size: 12px;
    color: var(--color-neutral-6);
    max-height: 60px;
    overflow-y: auto;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

@keyframes ai-robot {
  0% {
    transform: rotateY(180deg)  translateY(0px);
  }
  50% {
    transform: rotateY(180deg) translateY(10px);
  }
  100% {
    transform: rotateY(180deg) translateY(0px);
  }
}

</style>
