<script setup lang="ts">
import { ref, useTemplateRef } from 'vue'
import { VueCropper }  from "vue-cropper";
import 'vue-cropper/dist/index.css';
import type { ImageCropperInstance } from './image';

const visible = ref(false);

const data = ref({
  img: '',
});
const emits = defineEmits<{
  saveOk: [base64: string],
}>();
const cropperRef = useTemplateRef<any>('cropperRef');
function handleOk() {
  cropperRef.value.getCropData((data) => {
    emits('saveOk', data);
  });
}

function show(img: string) {
  console.log('show', img)
  data.value.img = img;
  visible.value = true;
}
defineExpose<ImageCropperInstance>({
  show
})
</script>

<template>
  <a-modal :width="600" v-model:visible="visible"
           simple
           unmount-on-close
           @ok="handleOk"
  >
    <template #title>
      封面编辑
    </template>
    <VueCropper
      style="width: 598px; height: 360px;"
      ref="cropperRef"
      :img="data.img"
      :outputSize="1"
      outputType="png"
      :autoCrop="true"
      :fixed="true"
      :limitMinSize="[214, 120]"
      :fixedNumber="[214, 120]"
    ></VueCropper>
  </a-modal>
</template>

<style scoped lang="less">
.arco-modal-simple {
  padding: 10px 1px 10px;
}
</style>
