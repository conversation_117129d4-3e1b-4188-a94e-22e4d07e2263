import { ref } from 'vue';

export interface IUtoolsSearchViewContext {
  setSubInput: (value: string) => void;
}
const utoolsSearchContext = ref<IUtoolsSearchViewContext>();
export function registerSearchViewContext(context: IUtoolsSearchViewContext) {
  utoolsSearchContext.value = context;
}

export function useUtoolsSearchContext(): IUtoolsSearchViewContext {
  return utoolsSearchContext.value!;
}
