<script setup lang="ts">
import { useUtoolsSubInput } from '@/hooks/utoolsSubInput.ts';
import { computed, onMounted, ref, watch } from 'vue';
import { ExtensionManager } from '@/extension';
import type { TaskListItem } from '@xiaou66/todo-plugin';
import { TaskItem, useSaveTaskItem, useTaskDetailDrawer } from '@/components/Tasks';
import { debounce } from 'es-toolkit';
import { useEventListener, useMagicKeys } from '@vueuse/core';
import hotkeys from 'hotkeys-js';
import { registerSearchViewContext } from './UtoolsSearchView.ts';

const {  value, setSubInput, onChanged, onSearch, onClear, register } = useUtoolsSubInput('', '检索任务', false);

const taskList = ref<TaskListItem[]>([]);

async function requestQuery() {
  const keyword = value.value;
  if (!keyword) {
    return;
  }
  const res = await ExtensionManager.getTaskInstance().listTask({
    keyword
  });
  taskList.value = res.list;
}
const empty = computed(() => !taskList.value.length);


const show = computed(() => !!value.value);
watch(show, () => {
  if (!show.value) {
    taskList.value.length = 0;
  }
})

onChanged(debounce( async () => {
  await requestQuery()
}, 300));
const activeKey = ref<string[]>(['1']);
const taskDetailDrawerInstance = useTaskDetailDrawer();
const saveTaskItem = useSaveTaskItem();
useEventListener(window, 'taskList::refreshAll', () => {
  requestQuery();
});
hotkeys('command+f,ctrl+f', () => {
  register(true);
});
onMounted(() => {
  registerSearchViewContext({
    setSubInput
  })
})
</script>

<template>
  <transition name="bounce">
    <div v-show="show"
         class="search-task-view">
      <div class="search-task-view-inner">
        <a-collapse v-if="!empty"
                    v-model:active-key="activeKey">
          <a-collapse-item v-if="taskList.length"
                           header="任务列表"
                           key="1">
            <div v-for="taskItem in taskList"
                 :key="taskItem.taskId">
              <TaskItem :taskItem="taskItem"
                        showType="default"
                        disableDrag
                        @click="taskDetailDrawerInstance.show(taskItem)"
                        @save="saveTaskItem"/>
            </div>
          </a-collapse-item>
        </a-collapse>
        <div v-else
             class="u-h-full"
             style="padding-top: 60px;">
          <a-empty>检索支持「任务标题」检索</a-empty>
        </div>
      </div>
    </div>
  </transition>
</template>

<style scoped lang="less">
.search-task-view {
  position: absolute;
  top: 0;
  left: 0;
  width: 99vw;
  min-height: 100vh;
  overflow-y: auto;
  background: var(--utools-background);
  z-index: 99;
  padding: 10px;
  .search-task-view-inner {
    padding: 10px;
    background: var(--main-background-transparent);
    border-radius: 10px;
    height: 100%;
  }
}
.bounce-enter-active {
  animation: bounce-in 300ms;
}
.bounce-leave-active {
  animation: bounce-in 200ms reverse;
}
@keyframes bounce-in {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
