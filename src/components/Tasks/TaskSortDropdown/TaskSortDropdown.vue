<script setup lang="ts">
const groupType = defineModel<string>('groupType');
const sortType = defineModel<string>('sortType');
const emits = defineEmits<{
  (e: 'change'): void
}>()
</script>

<template>
  <t-popup class="u-transparent min-dropdown-select"
           trigger="click">
    <t-button class="sort-group-button"
              size="small"
              variant="text"
              theme="default">
      <template #icon>
        <t-icon class="i-u-sort-two"></t-icon>
      </template>
    </t-button>
    <template #content>
      <div>
        <t-select v-model:value="groupType"
                  size="small"
                  style="width: 150px"
                  :popupProps="{ overlayClassName: 'u-web-select-options prefix-2' }"
                  @change="emits('change')">
          <template #prefixIcon>
            <span class="text-sm">归类</span>
          </template>
          <t-option value="taskLevel" label="优先级" />
          <t-option value="taskGroupId" label="分组" />
          <t-option value="taskStartDate" label="时间" />
        </t-select>
      </div>
      <div class="mt-1">
        <t-select v-model:value="sortType"
                  size="small"
                  style="width: 150px"
                  :popupProps="{ overlayClassName: 'u-web-select-options prefix-2' }"
                  @change="emits('change')">
          <template #prefixIcon>
            <span class="text-sm">排序</span>
          </template>
          <!--                    <a-option value="taskLevel" >优先级</a-option>-->
          <!--                    <a-option value="createAt">创建时间</a-option>-->
          <t-option value="custom" label="自定义" />
        </t-select>
      </div>
    </template>
  </t-popup>
</template>

<style lang="less">
.u-web-select-options {
  .t-popup__content {
    width: 100% !important;
  }
  &.prefix-2 {
    .t-popup__content {
      margin-left: 38px;
    }
  }
}
</style>
<style scoped lang="less">

</style>
