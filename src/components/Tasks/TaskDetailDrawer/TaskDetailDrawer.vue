<script setup lang="ts">
import { registerTaskDetailDrawer, type TaskDetailDrawerInstance } from './';
import {computed, ref, toRaw, useTemplateRef} from 'vue'
import { EditorTiptap, type EditorTiptapInstance } from '@/components/editor'
import type { TaskListItem, ITaskTag } from '@xiaou66/todo-plugin'
import TaskConstants from '@/constants/tasks/TaskConstants.ts'
import { ExtensionManager } from '@/extension';
import jsUtil from '@/utils/jsUtil.ts'
import { GroupButtonSelect } from "@/components/group";
import TaskStatusSelect from "@/components/Tasks/TaskStatusSelect/TaskStatusSelect.vue";
import { useTaskDateTimeActions } from '@/components/Tasks/hooks.ts';
import { useEscModal } from '@/hooks/useModal.ts';
const visible = ref(false)

const taskInfo = ref<TaskListItem>();

const groupTaskTagList = ref<ITaskTag[]>([]);
const { startEsc, stopEsc } = useEscModal('keyup');
const groupTaskTagOptions = computed(() => {
  return groupTaskTagList.value.filter(item => !(taskInfo.value?.tagNameList || []).includes(item.name));
});
const showTagInput = ref(false);
function handleEnterAddTag() {
  setTimeout(() => {
    const dom = document.querySelector('#tagSelectRef .arco-select-view-input');
    if (dom instanceof HTMLInputElement) {
      dom.click();
      dom.focus();
    }
  })
  showTagInput.value = true
}
function handleRemoveTag(tagName: string) {
  if (taskInfo.value && taskInfo.value.tagNameList) {
    const index = taskInfo.value.tagNameList.indexOf(tagName);
    if (index >= 0) {
      taskInfo.value!.tagNameList.splice(index, 1);
      // TODO [需要修改] 其他情况下无需传递 docId
      const taskInstance = ExtensionManager.getTaskInstance();
      // @ts-ignore
      taskInstance.saveTask(taskInfo.value!.taskGroupId, {
        taskId: taskInfo.value!.taskId,
        docId: taskInfo.value!.docId,
        tagNameList: toRaw(taskInfo.value?.tagNameList),
      }).then(async () => {
        const newTaskInfo = await ExtensionManager.getTaskInstance()
          .getTask(taskInfo.value!.taskGroupId, taskInfo.value!.taskId);
        if (newTaskInfo) {
          taskInfo.value = newTaskInfo;
        }
      });;
    }
  }
}
async function handleChangeTag(value: string) {
  if (!value || !taskInfo.value) {
    return;
  }
  let tag = groupTaskTagList.value.find((tag) => tag.name === value);
  if(!tag) {
    const taskTagInstance = ExtensionManager.getTaskTagInstance();
    tag = await taskTagInstance.createTag({
      name: value
    });
    groupTaskTagList.value.push(tag);
  }
  if(!taskInfo.value.tagNameList) {
    taskInfo.value.tagNameList = []
  }
  if (taskInfo.value.tagNameList!.includes(tag.name)) {
    // fix: 标签已被当前任务添加不能重复添加
    return;
  }
  taskInfo.value.tagNameList!.push(tag.name);
  const taskInstance = ExtensionManager.getTaskInstance()

  // TODO [需要修改] 其他情况下无需传递 docId
  // @ts-ignore
  taskInstance.saveTask(taskInfo.value!.taskGroupId, {
    taskId: taskInfo.value!.taskId,
    docId: taskInfo.value!.docId,
    tagNameList: toRaw(taskInfo.value?.tagNameList),
  }).then(async () => {
    const newTaskInfo = await ExtensionManager.getTaskInstance()
      .getTask(taskInfo.value!.taskGroupId, taskInfo.value!.taskId);
    if (newTaskInfo) {
      taskInfo.value = newTaskInfo;
    }
  });
  showTagInput.value = false;
}
function show(task: TaskListItem) {
  taskInfo.value = task
  visible.value = true;
  startEsc();
  const taskInstance = ExtensionManager.getTaskInstance();
  taskInstance.getTaskDesc(taskInfo!.value!.taskId).then((desc) => {
    editorTiptapRef.value?.setContent(desc);
  });

  const taskTagExtension = ExtensionManager.getTaskTagInstance();
  taskTagExtension.listTag().then((list) => {
    groupTaskTagList.value = list;
  })

  // nextTick(() => {
  //   editorTiptapRef.value?.disableEditor();
  // });
}

function handleSaveTaskInfo(field: keyof TaskListItem) {
  const taskInstance = ExtensionManager.getTaskInstance();
  const task = taskInfo.value!
  // TODO [需要修改] 其他情况下无需传递 docId
  // @ts-ignore
  taskInstance.saveTask(taskInfo.value!.taskGroupId, {
    taskId: task.taskId,
    docId: task.docId,
    [field]: task[field],
  }).then(async () => {
    const newTaskInfo = await ExtensionManager.getTaskInstance()
      .getTask(taskInfo.value!.taskGroupId, taskInfo.value!.taskId);
    if (newTaskInfo) {
      taskInfo.value = newTaskInfo;
    }
  });
}

const editorTiptapRef = useTemplateRef<EditorTiptapInstance>('editorTiptapRef')
const editorStatus = ref(true)
function handleEditorStatus(status: boolean) {
  editorStatus.value = status
  if (status) {
    editorTiptapRef.value?.enableEditor()
  } else {
    editorTiptapRef.value?.disableEditor()
  }
}

function handleSaveEditor(content: string) {
  const taskInstance =  ExtensionManager.getTaskInstance();
  taskInstance.saveTaskDesc(taskInfo.value!.taskId, content);
  // editorToolBarHide.value = true;
  // editorStatus.value = false;
}

const editorToolBarHide = ref(false);
function handleFocusEditor() {
  // editorToolBarHide.value = false;
  // editorStatus.value = true;
}

defineExpose<TaskDetailDrawerInstance>({
  show,
});

registerTaskDetailDrawer({
  show,
});

const emits = defineEmits<{
  close: []
}>()
function handleSaveChange() {
  emits('close')
}

const taskDateTimeActions = useTaskDateTimeActions(taskInfo!, () => {
  const task = taskInfo.value!;
  ExtensionManager.getTaskInstance().saveTask(taskInfo.value!.taskGroupId, {
    docId: task.docId,
    ...task,
  }).then(async () => {
    const newTaskInfo = await ExtensionManager.getTaskInstance()
      .getTask(taskInfo.value!.taskGroupId, taskInfo.value!.taskId);
    if (newTaskInfo) {
      taskInfo.value = newTaskInfo;
    }
  });
});
const { isAllDay } = taskDateTimeActions;
</script>

<template>
  <a-drawer
    @keydown.stop
    v-if="taskInfo"
    v-model:visible="visible"
    width="90%"
    :unmount-on-close="true"
    :footer="false"
    :header="false"
    :render-to-body="true"
    @beforeClose="handleSaveChange"
    @close="stopEsc"
  >
    <div class="task-detail-container">
      <div>
        <div class="header">
          <div class="title">
            <div class="u-fx">
              <a-input
                style="width: 100%"
                size="large"
                v-model:model-value="taskInfo.taskTitle"
                @blur="handleSaveTaskInfo('taskTitle')"
              />
            </div>
          </div>
          <div class="action-bar">
            <a-button
              v-if="!editorStatus"
              size="mini"
              type="outline"
              shape="round"
              @click="handleEditorStatus(true)"
            >
              <template #icon>
                <div class="u-fac u-fx">
                  <iconpark-icon name="write"></iconpark-icon>
                </div>
              </template>
              编辑描述
            </a-button>
<!--            <a-button
              v-else
              size="mini"
              type="outline"
              shape="round"
              @click="handleEditorStatus(false)"
            >
              <template #icon>
                <div class="u-fac u-fx">
                  <iconpark-icon name="write"></iconpark-icon>
                </div>
              </template>
              退出编辑
            </a-button>-->
          </div>
        </div>
        <div style="width: 100%;">
          <EditorTiptap
            ref="editorTiptapRef"
            editorContentMaxHeight="calc(100vh - 100px)"
            :hideToolBar="editorToolBarHide"
            @save="handleSaveEditor"
            @focus="handleFocusEditor"
          />
        </div>
      </div>
      <div class="task-info">
        <div class="info-item">
          <div>分组</div>
          <div class="u-fx u-fac u-gap5">
            <GroupButtonSelect v-model:model-value="taskInfo.taskGroupId"
                               @change="handleSaveTaskInfo('taskGroupId')">
            </GroupButtonSelect>
          </div>
        </div>
        <div class="info-item">
          <div>任务状态</div>
          <TaskStatusSelect v-model:model-value="taskInfo.taskStatus"
                            @change="handleSaveTaskInfo('taskStatus')"
                            size="small"
                            :group-id="taskInfo.taskGroupId"
                            type="select" />
        </div>
        <div class="info-item">
          <div class="u-fx u-f-between">
            <a-radio-group v-model:model-value="taskInfo.taskDateType"
                           type="button"
                           size="mini"
                           @change="taskDateTimeActions.switchTimeDateType">
              <a-radio value="date">时间</a-radio>
              <a-radio value="dateSegment">时间段</a-radio>
            </a-radio-group>
            <div class="u-fx u-gap5 u-fac">
              <div class="u-font-size-smail">全天</div>
              <a-switch :model-value="isAllDay"
                        size="small"
                        @change="(val: any) => taskDateTimeActions.switchAllDay(val)"></a-switch>
            </div>
          </div>
          <div style="padding-top: 6px;">
            <div v-if="taskInfo.taskDateType === 'date'"
                 class="u-fx u-gap5 task-date-time"
                 :class="{ 'all-day': isAllDay }">
              <a-date-picker v-model:model-value="taskInfo.taskStartDate"
                             size="small"
                             :show-now-btn="false"
                             @change="taskDateTimeActions.autoAdjustDateTime">
                <a-button class="u-transparent date-button"
                          size="small">
                  {{ taskInfo.taskStartDate ? jsUtil.dateFormat(taskInfo.taskStartDate) : '未设置' }}
                </a-button>
              </a-date-picker>
              <a-time-picker v-if="!isAllDay"
                             v-model:model-value="taskInfo.taskStartTime"
                             class="u-transparent u-time-picker"
                             size="small"
                             style="width: 120px;"
                             format="HH:mm"
                             :trigger-props="{ contentClass: 'u-time-picker-content', unmountOnClose: true }"
                             :step="{
                                  hour: 1,
                                  minute: 15,
                             }"
                             hide-disabled-options
                             disable-confirm
                             :allow-clear="false"
                             @change="taskDateTimeActions.autoAdjustDateTime"
                             @popup-visible-change="taskDateTimeActions.autoDefaultTaskStartTime"  />
            </div>
            <div v-else-if="taskInfo.taskDateType === 'dateSegment'">
              <div class="task-date-time" :class="{ 'all-day': isAllDay }">
                <a-date-picker v-model:model-value="taskInfo.taskStartDate"
                               size="small"
                               :show-now-btn="false"
                               @change="taskDateTimeActions.autoAdjustDateTime">
                  <a-button class="u-transparent date-button"
                            size="small">
                    {{ taskInfo.taskStartDate ? jsUtil.dateFormat(taskInfo.taskStartDate) : '未设置' }}
                  </a-button>
                </a-date-picker>
                <a-time-picker v-if="!isAllDay" v-model:model-value="taskInfo.taskStartTime"
                               class="u-transparent u-time-picker"
                               size="small"
                               style="width: 120px;"
                               format="HH:mm"
                               :trigger-props="{ contentClass: 'u-time-picker-content', unmountOnClose: true }"
                               :step="{
                                  hour: 1,
                                  minute: 15,
                               }"
                               hide-disabled-options
                               disable-confirm
                               :allow-clear="false"
                               @change="taskDateTimeActions.autoAdjustDateTime"/>
              </div>
              <div class="task-date-time" :class="{ 'all-day': isAllDay }"
                   style="padding-top: 4px;">
                <a-date-picker v-model:model-value="taskInfo.taskEndDate"
                               size="small"
                               :show-now-btn="false"
                               @change="taskDateTimeActions.autoAdjustDateTime">
                  <a-button class="u-transparent date-button"
                            size="small">
                    {{ taskInfo.taskEndDate ? jsUtil.dateFormat(taskInfo.taskEndDate) : '未设置' }}
                  </a-button>
                </a-date-picker>
                <a-time-picker v-if="!isAllDay"
                               v-model:model-value="taskInfo.taskEndTime"
                               class="u-transparent u-time-picker"
                               size="small"
                               style="width: 120px;"
                               format="HH:mm"
                               :disabled-hours="taskDateTimeActions.handleTaskEndHours"
                               :disabled-minutes="taskDateTimeActions.handleTaskEndMinutes"
                               :trigger-props="{ contentClass: 'u-time-picker-content', unmountOnClose: true }"
                               :step="{
                                  hour: 1,
                                  minute: 15,
                               }"
                               hide-disabled-options
                               disable-confirm
                               :allow-clear="false"
                               @change="taskDateTimeActions.autoAdjustDateTime" />
              </div>
            </div>
          </div>
        </div>
<!--        <div v-if="taskInfo.taskTime" class="info-item">-->
<!--          <div>提醒</div>-->
<!--          <div class="u-fx u-fac u-gap5">-->
<!--            <a-select default-value="1" size="small">-->
<!--              <a-option value="1">前15分钟</a-option>-->
<!--            </a-select>-->
<!--          </div>-->
<!--        </div>-->
        <div class="info-item">
          <div>优先级</div>
          <div class="u-fx u-fac u-gap5">
            <a-select
              v-model:model-value="taskInfo.taskLevel"
              @change="handleSaveTaskInfo('taskLevel')"
              size="small"
              :trigger-props="{ contentClass: 'u-select-options' }"
              :options="TaskConstants.PRIORITY_SELECT_OPTIONS_TAG"
            >
              <template #label="{ data }">
                <a-tag :color="data.tagColor">
                  <template #icon>
                    <iconpark-icon name="mark" class="u-fx u-fac"></iconpark-icon>
                  </template>
                  {{ data.label }}
                </a-tag>
              </template>
              <template #option="{ data }">
                <a-tag :color="data.tagColor">
                  <template #icon>
                    <iconpark-icon name="mark" class="u-fx u-fac"></iconpark-icon>
                  </template>
                  {{ data.label }}
                </a-tag>
              </template>
            </a-select>
          </div>
        </div>
        <div class="info-item">
          <div>标签</div>
          <div class="u-fx u-fac u-gap5" style="flex-wrap: wrap">
            <template v-if="taskInfo.tagNameList && taskInfo.tagNameList.length">
             <a-tag size="small"
                    v-for="(tagName) in taskInfo.tagNameList"
                    :key="tagName"
                    closable
                    @close="() => handleRemoveTag(tagName)">
               {{ tagName }}
             </a-tag>
            </template>
            <a-select
              v-if="showTagInput"
              id="tagSelectRef"
              autofocus
              :style="{ width: '120px'}"
              size="mini"
              :allow-search="true"
              allow-create
              :trigger-props="{ contentClass: 'u-select-options min' }"
              @change="(value: any) => handleChangeTag(value as any)"
              @blur="showTagInput = false;"
            >
              <template #empty>
                <a-empty>
                  <div class="u-font-size-smail">无可被选择标签</div>
                  <div class="u-font-size-smail">输入内容回车创建</div>
                </a-empty>
              </template>
              <a-option v-for="tag in groupTaskTagOptions"
                        :value="tag.name"
                        :key="tag.id"
                        :label="tag.name">
              </a-option>
            </a-select>
            <a-tag
              v-else
              class="add-tag"
              @click="handleEnterAddTag">
              <template #icon>
                <iconpark-icon class="u-fx u-fac" name="plus"></iconpark-icon>
              </template>
            </a-tag>
          </div>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<style scoped lang="less">
.add-tag {
  width: 30px;
  background-color: var(--color-fill-2);
  border: 1px dashed var(--color-fill-3);
  cursor: pointer;
  border-radius: 10px;
  transition: all 250ms linear;
  &:hover {
    border: 1px dashed rgb(var(--arcoblue-6));
  }
}
.task-detail-container {
  display: grid;
  width: 100%;
  grid-template-columns: calc(100% - 222px) 220px;
  > div:first-child {
    padding: 5px;
  }
  > div:last-child {
    width: 100%;
    height: 100vh;
    background: var(--color-fill-1);
  }
  .header {
    .title {
      .arco-input-wrapper {
        background: transparent;
        &:hover {
          border: 1px solid var(--color-neutral-3);
        }
        &:focus-within {
          border: 1px solid rgb(var(--arcoblue-5));
        }
      }
      .arco-btn-secondary,
      .arco-btn-secondary[type='button'],
      .arco-btn-secondary[type='submit'] {
        background: transparent;
        padding: 6px;
      }
    }
    .action-bar {
      display: flex;
      justify-content: flex-end;
      padding-top: 10px;
      padding-bottom: 10px;
      border-bottom: 1px dotted var(--color-neutral-3);
      .arco-btn-outline,
      .arco-btn-outline[type='button'],
      .arco-btn-outline[type='submit'] {
        border: 1px solid var(--color-neutral-4);
        color: var(--color-neutral-8);
        transition: all 250ms linear;
        &:hover {
          color: rgb(var(--arcoblue-6));
          border-color: rgb(var(--primary-6));
        }
      }
    }
  }
}
.task-info {
  width: 32%;
  max-width: 240px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  :deep(.arco-select-view-single) {
    background: transparent;
    padding-left: 2px;
    .arco-select-view-suffix {
      opacity: 0;
    }
    &:hover {
      background: var(--color-fill-2);
      .arco-select-view-suffix {
        opacity: 1;
      }
    }
    &:focus-within {
      background: var(--color-fill-2);
      .arco-select-view-suffix {
        opacity: 1;
      }
    }
  }
  .info-item {
    > :first-child {
      font-size: 13px;
      color: var(--color-neutral-6);
    }
    > :last-child {
    }
  }
  .task-date-time {
    display: grid;
    grid-template-columns: 44% 40%;
    gap: 4px;
    &.all-day {
      grid-template-columns: 98%;
    }
  }
}
</style>
