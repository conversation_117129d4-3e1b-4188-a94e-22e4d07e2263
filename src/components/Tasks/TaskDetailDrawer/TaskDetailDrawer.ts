import type { TaskListItem } from '@xiaou66/todo-plugin'
import { ref } from 'vue';
import { isString } from 'es-toolkit';

export interface TaskDetailDrawerInstance {
  show(task: TaskListItem): void;
}

const taskDetailDrawerRef = ref<TaskDetailDrawerInstance>();
export function registerTaskDetailDrawer(instance: TaskDetailDrawerInstance) {
  taskDetailDrawerRef.value = instance;
}
export function useTaskDetailDrawer(): TaskDetailDrawerInstance {
  return {
    show(task: TaskListItem) {
      if (isString(task)) {

      }
      taskDetailDrawerRef.value?.show(task)
    }
  }
}
