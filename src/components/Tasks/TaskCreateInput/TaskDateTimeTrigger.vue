<script setup lang="ts">
import dayjs from "dayjs";
import jsUtil from "@/utils/jsUtil.ts";
import { ref } from 'vue';
import type {ITaskItem} from "@xiaou66/todo-plugin";
import {cloneDeep} from "es-toolkit";
import tippy from 'tippy.js';
import { useTaskDateTimeActions } from '@/components/Tasks/hooks.ts';
import { DatePicker, DatePickerPanel } from '@/components/common/DatePicker'
import type { TimePickerValue } from 'tdesign-vue-next'
type DateInfo = Pick<ITaskItem, 'taskDateType' | 'taskStartDate' | 'taskStartTime' | 'taskEndDate' | 'taskEndTime'>;
const props = withDefaults(defineProps<{
  taskDateInfo: Partial<DateInfo>,
  position?: 'br' | 'rt' | 'tr' | 'top' | 'bottom' | 'right' | 'left' | 'tl' | 'bl' | 'lt' | 'lb' | 'rb' | undefined;
  popupTranslate?: [number, number][],
}>(), {
  position: 'left',
});

const defaultValue: DateInfo = {
  taskDateType: 'date',
  taskStartDate: '',
  taskStartTime: '',
  taskEndDate: '',
  taskEndTime: ''
};

const dateInfo = ref<DateInfo>();

const emits = defineEmits<{
  save: [data: DateInfo];
  close: [];
  open:[];
}>();

const triggerRef = ref<HTMLElement>();
let instance: any = null;

const triggerContentRef = ref<HTMLElement>();
function handleShow(e: MouseEvent) {
  // 销毁旧的 tippy 实例
  if (instance) {
    instance.destroy();
    instance = null;
  }
  handleShowEvent();
  console.log('创建弹框内容', triggerContentRef.value);
  // @ts-ignore
  instance = tippy(e.target as HTMLElement, {
    content: triggerContentRef.value,
    zIndex: 1001,
    trigger: 'manual',
    placement: 'right',
    interactive: true,
    allowHTML: true,
    appendTo: document.body,
    animation: 'scale',
    hideOnClick: false,
    onClickOutside(instance, event: any) {
      if (event.target.closest('.tippy-box')
        || event.target.closest('.u-date-picker-content')
        || event.target.closest('.t-time-picker__panel')) {
        // 防止内部的弹窗导致弹窗关闭
        return;
      }
      event.stopPropagation();
      console.log(event);
      // 这里你可以判断 event.target 是否在你允许的区域内
      // 如果不在，手动关闭
      instance.hide();
    },
    getReferenceClientRect: () => ({
      width: 0,
      height: 0,
      top: e.clientY,
      bottom: e.clientY,
      left: e.clientX,
      right: e.clientX,
    }),
    onHidden() {
      handleCloseEvent();
      showTippy.value = false;
      instance.destroy();
      instance = null;
    },
    popperOptions: {
      modifiers: [
        {
          name: 'flip',
          options: {
            fallbackPlacements: ['right', 'left'], // 空间不足时自动切换
          },
        },
        {
          name: 'preventOverflow',
          options: {
            boundary: 'viewport', // 以视口为边界
            padding: 8,
          },
        },
      ],
    }
  });

  instance.show();
}

// 重置按钮功能
function handleResetTask() {
  emits('save', defaultValue);
  instance && instance.hide();
}

// 保存任务时间处理函数
function handleSaveTask() {
  emits('save', dateInfo.value!);
  instance && instance.hide();
}

const showTippy = ref(false);
function handleShowEvent() {
  console.log('props.taskDateInfo', props.taskDateInfo);
  dateInfo.value = {
    ...cloneDeep(defaultValue),
    ...props.taskDateInfo,
  };
  showTippy.value = true;
  emits('open');
}

const taskDateTimeActions = useTaskDateTimeActions(dateInfo);
const { isAllDay } = taskDateTimeActions;
function switchTimeDateType() {
  if (!dateInfo.value) {
    return;
  }
  if (instance && instance.popperInstance) {
    instance.popperInstance.update();
  }
  taskDateTimeActions.switchTimeDateType();
}

function handleCloseEvent() {
  console.log('TaskDateTimeTrigger--handleCloseEvent');
  emits('close');
}
</script>

<template>
  <div>
    <div ref="triggerRef" @click="handleShow">
      <slot name="default"></slot>
    </div>
    <teleport to="body">
      <!-- 遮罩层 -->
      <div v-if="showTippy"
        class="tippy-mask"
        style="position: fixed; left: 0; top: 0; right: 0; bottom: 0; z-index: 100;"
      ></div>
      <div ref="triggerContentRef">
        <div v-if="showTippy && dateInfo"
             class="date-trigger">
          <t-radio-group v-model:value="dateInfo.taskDateType"
                         default-value="date"
                         variant="default-filled"
                         class="w-full"
                         size="small"
                         @change="switchTimeDateType">
            <t-radio-button value="date" class="w-1/2 flex justify-center">日期</t-radio-button>
            <t-radio-button value="dateSegment" class="w-1/2 flex justify-center">时间段</t-radio-button>
          </t-radio-group>
          <div v-if="dateInfo.taskDateType === 'date'"
               class="u-date-picker">
            <DatePickerPanel v-model:modal-value="dateInfo.taskStartDate"
                             :panel-props="{ size: 'small' }"
                             format-value="YYYY-MM-DD" />
            <t-time-picker
              v-model:value="dateInfo.taskStartTime"
              format="HH:mm"
              position="top"
              size="small"
              :steps="[1, 15]"
              allowInput
              clearable
              label="时间"
            >
            </t-time-picker>
            <DatePicker />
          </div>
          <div v-else-if="dateInfo.taskDateType === 'dateSegment'"
               style="padding: 6px">
            <div>
              <div class="time-select-container">
                <div class="u-fx u-gap5 " style="padding: 2px 0px;">
                  <a-date-picker v-model:model-value="dateInfo.taskStartDate"
                                 size="mini"
                                 :show-now-btn="false"
                                 @change="taskDateTimeActions.autoAdjustDateTime">
                    <a-button class="u-transparent date-button"
                              size="mini"
                              :style="{width: `${isAllDay ? 240 : 120}px`}">
                      {{ jsUtil.dateFormat(dateInfo.taskStartDate!) }}
                    </a-button>
                  </a-date-picker>
                  <a-time-picker v-if="!isAllDay"
                                 v-model:model-value="dateInfo.taskStartTime"
                                 class="u-transparent u-time-picker"
                                 size="mini"
                                 style="width: 120px;"
                                 format="HH:mm"
                                 :trigger-props="{ contentClass: 'u-time-picker-content', unmountOnClose: true }"
                                 :step="{
                                  hour: 1,
                                  minute: 15,
                               }"
                                 hide-disabled-options
                                 disable-confirm
                                 :allow-clear="false"
                                 @change="taskDateTimeActions.autoAdjustDateTime"
                  />
                </div>
                <div class="u-fx u-gap5" style="padding: 2px 0">
                  <a-date-picker v-model:model-value="dateInfo.taskEndDate"
                                 style="z-index: 88888"
                                 size="mini"
                                 :show-now-btn="false"
                                 @change="taskDateTimeActions.autoAdjustDateTime">
                    <a-button size="mini"
                              class="u-transparent date-button"
                              :style="{width: `${isAllDay ? 240 : 120}px`}">
                      {{ jsUtil.dateFormat(dateInfo.taskEndDate!) }}
                    </a-button>
                  </a-date-picker>
                  <a-time-picker v-if="!isAllDay"
                                 v-model:model-value="dateInfo.taskEndTime"
                                 class="u-transparent u-time-picker"
                                 size="mini"
                                 style="width: 120px;"
                                 format="HH:mm"
                                 :trigger-props="{ contentClass: 'u-time-picker-content', unmountOnClose: true }"
                                 :step="{
                                  hour: 1,
                                  minute: 15,
                               }"
                                 :disabled-hours="taskDateTimeActions.handleTaskEndHours"
                                 :disabled-minutes="taskDateTimeActions.handleTaskEndMinutes"
                                 hide-disabled-options
                                 disable-confirm
                                 :allow-clear="false"
                                 @change="taskDateTimeActions.autoAdjustDateTime"/>
                </div>
              </div>
              <div class="u-fx u-gap5 u-f-between u-mt10"
                   style="padding: 2px 0;">
                <div>
                  全天
                </div>
                <div>
                  <a-switch size="small"
                            :model-value="isAllDay"
                            style="width: 32px;"
                            @change="(val: any) => taskDateTimeActions.switchAllDay(val)"/>
                </div>
              </div>
            </div>
          </div>

          <!--                    <a-select multiple class="bg-hover" size="small" allow-clear>
            <template #prefix>提醒</template>
            <div class="min-dropdown-select-options">
              <a-option>5 分钟前</a-option>
              <a-option>15 分钟前</a-option>
              <a-option>30 分钟前</a-option>
            </div>
          </a-select>-->
          <!--                    <a-select class="bg-hover" size="small" allow-clear>
            <template #prefix>重复</template>
            <div class="min-dropdown-select-options">
              <a-option>5 分钟前</a-option>
              <a-option>15 分钟前</a-option>
              <a-option>30 分钟前</a-option>
            </div>
          </a-select>-->
          <div class="footer">
            <t-input-group class="w-full">
              <t-button class="w-1/2"
                        theme="default"
                        @click="handleResetTask">
                重置
              </t-button>
              <t-button
                class="w-1/2"
                @click="handleSaveTask">
                设置
              </t-button>
            </t-input-group>
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>
<style lang="less">
.t-time-picker__panel {
  width: 220px;
  .t-time-picker__panel-section-body {
    padding: 0;
  }
  .t-time-picker__panel-body {
    height: 150px;
  }
  .t-time-picker__panel-section-footer {
    padding: var(--td-pop-padding-s);
  }
}
</style>
<style scoped lang="less">
.date-trigger {
  box-sizing: border-box;
  min-width: 260px;
  max-width: 260px;
  overflow: hidden;
  border-radius: 4px;
  padding: 4px;
  background: var(--color-bg-5);
}

.date-button {
  background: transparent !important;
  border: 1px solid var(--color-neutral-3);

  &:hover {
    border: 1px solid rgb(var(--arcoblue-6));
  }
}

:deep(.u-time-picker) {
  background: transparent !important;
  border: 1px solid var(--color-neutral-3) !important;
  &:hover {
    border: 1px solid rgb(var(--arcoblue-6)) !important;
  }
}

.time-select-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.footer {
  padding: 6px;
}
</style>
