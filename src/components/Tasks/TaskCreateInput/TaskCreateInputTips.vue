<script setup lang="ts">
import { UContextMenu } from "@/components/common";
import type { UContextMenuInstance } from "@/components/common";
import { ref, useTemplateRef, watch } from "vue";
import JsUtil from "@/utils/jsUtil.ts";
import { useEventListener } from "@vueuse/core";
import type {Fn} from "@vueuse/core";
import TaskConstants from "@/constants/tasks/TaskConstants.ts";
import { ExtensionManager } from "@/extension";
import type { TaskSaveParams } from "@xiaou66/todo-plugin";


const props = withDefaults(defineProps<{
  direction?: 'bottom' | 'top'
}>(), {
  direction: 'bottom'
});

export interface ITipsOption {
  label: string;
  value: string;
  field: string;
  color?: string;
}

const tipsOptionsMap: Record<string, {options: (value: string) => Promise<ITipsOption[]>}> = {
  '!': {
    options: async (value: string) => {
      return TaskConstants.PRIORITY_SELECT_OPTIONS_TAG
        .map(item => {
          return {
            label: `${Number(item.value) + 1}-${item.label}`,
            value: item.value,
            field: 'taskLevel',
            color: item.color,
          }
        }).filter(item => !value || item.label.includes(value));
    },
  },
  '~': {
    options: async (value: string) => {
      return await ExtensionManager.getGroupInstance().listGroup({})
        .then((res) => {
          return res.list.map(item => {
            return {
              label: item.groupName,
              value: item.groupId,
              field: 'taskGroupId',
            }
          }).filter(item => !value || item.label.includes(value))
        });
    }
  },
  // '#': {
  //   options: async (value: string) => {
  //     return await ExtensionManager.getTaskTagInstance().listTag()
  //       .then((res) => {
  //         return res.map(item => {
  //           return {
  //             label: item.name,
  //             value: item.name,
  //             field: 'tagNameList',
  //           }
  //         }).filter(item => !value || item.label.includes(value))
  //       });
  //   },
  // }
}

const contextRef = useTemplateRef<UContextMenuInstance>('contextRef');
const tipsOptions = ref<ITipsOption[]>([]);
const tipsIndex = ref<number>(0);
const createTaskInfo = defineModel<TaskSaveParams>('modelValue');
const tipsVisible = defineModel<boolean>('tipsVisible');

watch(tipsVisible, (value) => {
  if (!value) {
    tipsIndex.value = 0;
    stopListener && stopListener();
    stopListener = null;
  }
})
function handleContextMenuMouseEnter(index: number) {
  tipsIndex.value = index;
}

function handleContextMenuSelect(tipsOption: ITipsOption) {
  if (tipsOption.field === 'tagNameList') {
    createTaskInfo.value![tipsOption.field] = [tipsOption.value];
  } else {
    createTaskInfo.value![tipsOption.field] = tipsOption.value;
  }
  createTaskInfo.value!.taskTitle = '';
  stopListener && stopListener();
  stopListener = null;
}
let stopListener: null | Fn = null;



async function handleInputEvent(value: string, e: InputEvent) {
  if (stopListener) {
    stopListener && stopListener();
    stopListener = null;
  }
  if (value.startsWith('!')
    || value.startsWith('！')
    || value.startsWith("~")) {
    const position = JsUtil.calcInputPosition(value, e);
    if (position) {
      if (props.direction === 'bottom') {
        position.top += 30;
      } else if (props.direction === 'top') {
        position.top -= 30;
      }
      const tipsOptionConfig = tipsOptionsMap[value.charAt(0)] || tipsOptionsMap['!'];
      contextRef.value?.showPosition(position);
      tipsOptions.value = await tipsOptionConfig.options(value.substring(1, value.length));
      if (tipsOptions.value.length === 0) {
        contextRef.value?.hide();
      }
      tipsIndex.value = 0;
      stopListener = useEventListener(window, 'keydown', (e) => {
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp' || e.key === 'Enter') {
          e.stopPropagation();
          e.preventDefault();
        }
        if (e.key === 'ArrowDown') {
          tipsIndex.value = Math.min(tipsOptions.value.length - 1, tipsIndex.value + 1) ;
        } else if (e.key === 'ArrowUp') {
          tipsIndex.value = Math.max(tipsIndex.value - 1, 0);
        } else if (e.key === 'Enter') {
          console.log('enter.....')
          handleContextMenuSelect(tipsOptions.value[tipsIndex.value])
          contextRef.value?.hide();
        }
      })
    }
  } else {
    contextRef.value?.hide();
    tipsIndex.value = 0;
  }
}
defineExpose({
  inputEvent: handleInputEvent
});
</script>

<template>
  <u-context-menu ref="contextRef" v-model:visible="tipsVisible">
    <template #content>
      <div class="min-dropdown-select-options">
        <a-doption v-for="(tipsOption, index) in tipsOptions"
                   :key="tipsOption.value"
                   :value="tipsOption.value"
                   :class="{'arco-dropdown-option-active': index === tipsIndex}"
                   @mouseenter="handleContextMenuMouseEnter(index)"
                   @click="handleContextMenuSelect(tipsOption)">
            <span :style="{ color: tipsOption.color }">
                {{ tipsOption.label }}
            </span>
        </a-doption>
      </div>
    </template>
  </u-context-menu>
</template>

<style scoped lang="less">

</style>
