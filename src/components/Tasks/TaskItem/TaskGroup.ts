const taskGroupDataKey = Symbol('task-group');

export type TaskGroupData = {
  [taskGroupDataKey]: true;
}
/**
 * 类型守卫
 * @param value
 */
export function isTaskGroupData(value: Record<string | symbol, unknown>): value is TaskGroupData {
  return Boolean(value[taskGroupDataKey])
}


/**
 * 数据创建函数
 * @param params
 */
export function getGroupItemData(params: Omit<TaskGroupData, typeof taskGroupDataKey>): TaskGroupData {
  return {
    [taskGroupDataKey]: true,
    ...params
  }
}
