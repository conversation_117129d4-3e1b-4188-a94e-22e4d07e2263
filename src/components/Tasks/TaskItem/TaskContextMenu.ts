import type { ITaskItem } from '@xiaou66/todo-plugin';
import { ref } from 'vue';

export { default as TaskContextMenu } from './TaskContextMenu.vue';

export interface TaskContextMenuInstance {
  /**
   * 显示任务菜单
   * @param taskId 任务 id
   * @param event 事件
   */
  show: (taskItem: ITaskItem, e: MouseEvent) => void;
}

const taskContextMenuInstance = ref<TaskContextMenuInstance>();

export function useTaskContextMenu(): TaskContextMenuInstance {
  return taskContextMenuInstance.value as TaskContextMenuInstance;
}

export function registerTaskContextMenuInstance(instance: TaskContextMenuInstance) {
  taskContextMenuInstance.value = instance;
}
