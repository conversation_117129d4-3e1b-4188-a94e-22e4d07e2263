<script setup lang="ts">
import { computed } from 'vue'
import TaskItem from './TaskItem.vue'
import {useTaskDragContext} from "@/components/Tasks/TaskItem/TaskDragWrapper.ts";


const [taskDragContext] = useTaskDragContext();

// 计算预览组件的位置样式
const layerStyles = computed<any>(() => {
  if (!taskDragContext.value) {
    return {
      display: 'none'
    }
  }

  const { x, offsetX, y, offsetY } = taskDragContext.value;

  const transform = `translate(${x - offsetX}px, ${y - offsetY}px)`
  return {
    transform,
    WebkitTransform: transform,
  }
});
</script>

<template>
  <div v-if="taskDragContext"
       class="drag-preview"
       :style="layerStyles">
    <div class="preview-content">
      <div class="task-preview-item" :style="{width: `${taskDragContext.width}px`}">
        <!-- 自定义预览内容 -->
        <TaskItem :task-item="taskDragContext.task" show-type="default" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.drag-preview {
  position: fixed;
  pointer-events: none;
  z-index: 100;
  left: 0;
  top: 0;
  width: auto;
  height: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
