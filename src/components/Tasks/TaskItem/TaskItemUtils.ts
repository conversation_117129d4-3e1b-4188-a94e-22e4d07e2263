import dayjs from 'dayjs';
import { ExtensionManager } from '@/extension';
import type { ITaskItem } from '@xiaou66/todo-plugin';

export async function taskItemGoOnToDay(taskItem: ITaskItem): Promise<boolean> {
  if (!taskItem || !taskItem.taskStartDate) {
    return false;
  }
  const toDay = dayjs();
  if (taskItem.taskDateType === 'date') {
    await ExtensionManager.getTaskInstance().saveTask(taskItem?.taskGroupId || '', {
      ...taskItem,
      taskStartDate: toDay.format('YYYY-MM-DD'),
    });
    return true;
  } else if (taskItem.taskDateType === 'dateSegment') {
    const diffDayNum = dayjs(taskItem.taskEndDate).diff(dayjs(taskItem.taskStartDate), 'day');
    await ExtensionManager.getTaskInstance().saveTask(taskItem?.taskGroupId || '', {
      ...taskItem,
      taskStartDate: toDay.format('YYYY-MM-DD'),
      taskEndDate: toDay.add(diffDayNum, 'day').format('YYYY-MM-DD'),
    });
    return true;
  }
  return false;
}
