import { ref } from "vue";
import type {Ref} from "vue";
import type { ITaskItem } from '@xiaou66/todo-plugin';
import {merge} from "es-toolkit";


export interface ITaskDragContext {
  task: ITaskItem,
  newTask?: Partial<ITaskItem>
  x: number,
  y: number,
  width: number,
  offsetX: number,
  offsetY: number,
}

const taskDragContext =  ref<ITaskDragContext>();


function setTaskDragContext(context?: Partial<ITaskDragContext>) {
  console.log('setTaskDragContext', context)
  if (context) {
    taskDragContext.value = merge(taskDragContext.value || {}, context) as ITaskDragContext;
  } else {
    taskDragContext.value = undefined;
  }
}

export function useTaskDragContext(): [Ref<ITaskDragContext | undefined>, (context?: Partial<ITaskDragContext>) => void] {
  return [taskDragContext, setTaskDragContext];
}
