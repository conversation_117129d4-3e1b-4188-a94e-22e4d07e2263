<script setup lang="ts">
import { nextTick, ref, useTemplateRef } from 'vue';
import type { ITaskItem, TaskListItem } from "@xiaou66/todo-plugin";
import { UContextMenu } from "@/components/common";
import type { UContextMenuInstance } from "@/components/common";
import {ExtensionManager} from "@/extension";
import { registerTaskContextMenuInstance } from '@/components/Tasks/TaskItem/TaskContextMenu.ts';
import dayjs, { Dayjs } from 'dayjs';
import { taskItemGoOnToDay } from '@/components/Tasks/TaskItem/TaskItemUtils.ts';


const taskItemContextMenuItems: Record<string, {
  name: string;
  icon: string;
  action: () => void;
  condition: () => boolean;
}> = {
  delete: {
    name: '删除',
    icon: 'delete',
    action: handleDeleteTask,
    condition: () => !taskItem.value || !taskItem.value.deleted,
  },
  doNotDelete: {
    name: '还原',
    icon: 'return',
    action: handleDoNotDeleteTask,
    condition: () => {
      if (taskItem.value && taskItem.value.deleted) {
        return true
      }
      return false;
    },
  },
  postpone: {
    name: '顺延',
    icon: 'go-on',
    action: async () => {
      if (!taskItem.value) {
        return false;
      }
      const result = await taskItemGoOnToDay(taskItem.value);
      if (result) {
        window.dispatchEvent(new CustomEvent('taskList::refreshAll'));
      }
    },
    condition: () => {
      // debugger
      if (!taskItem.value || !taskItem.value.taskStartDate) {
        return false;
      }
      const now = dayjs();
      if (taskItem.value.taskDateType === 'dateSegment') {
        const taskEndDateDayjs = dayjs(taskItem.value.taskEndDate)
        return now.isAfter(taskEndDateDayjs) && !now.isSame(dayjs(taskItem.value.taskEndDate), 'day');
      }
      const taskStartDateDayjs = dayjs(taskItem.value.taskStartDate);
      return now.isAfter(taskStartDateDayjs) && !now.isSame(taskStartDateDayjs, 'day');
    },
  }
};
const contextRef = useTemplateRef<UContextMenuInstance>('contextRef')
const taskItem = ref<ITaskItem>();
const activeMenuItemKeys = ref<string[]>([]);
function show(task: ITaskItem, e: MouseEvent) {
  taskItem.value = task;
  activeMenuItemKeys.value = Object.keys(taskItemContextMenuItems)
      .filter(key => taskItemContextMenuItems[key].condition());
  contextRef.value?.show(e);
}
function handleContextHide() {
  activeMenuItemKeys.value = [];
}
async function handleDeleteTask() {
  await ExtensionManager.getTaskInstance().saveTask(taskItem.value?.taskGroupId || '', {
    ...taskItem.value,
    deleted: true,
  })
  window.dispatchEvent(new CustomEvent('taskList::refreshAll'));
}
async function handleDoNotDeleteTask() {
  await ExtensionManager.getTaskInstance().saveTask(taskItem.value?.taskGroupId || '',{
    ...taskItem.value,
    deleted: false,
  });
  window.dispatchEvent(new CustomEvent('taskList::refreshAll'));
}

registerTaskContextMenuInstance({
  show
})
</script>

<template>
  <u-context-menu ref="contextRef"
                  @hide="handleContextHide">
    <slot></slot>
    <template #content>
      <div class="min-dropdown-select-options">
        <a-doption
            v-for="(actionMenuItemKey, index) in activeMenuItemKeys"
            :key="index"
            @click="taskItemContextMenuItems[actionMenuItemKey].action as any"
        >
          <template #icon>
            <iconpark-icon
                v-if="taskItemContextMenuItems[actionMenuItemKey].icon"
                :name="taskItemContextMenuItems[actionMenuItemKey].icon"
                style="color: var(--color-neutral-8)"
            />
          </template>
          {{ taskItemContextMenuItems[actionMenuItemKey].name }}
        </a-doption>
      </div>
    </template>
  </u-context-menu>
</template>

<style scoped lang="less">

</style>
