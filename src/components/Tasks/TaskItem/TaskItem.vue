<script lang="ts" setup>
import { computed, ref, onMounted, onBeforeUnmount, h } from 'vue';
import {
  getTaskDropTargetData,
  getTaskItemData,
  isTaskDropTargetData,
  isTaskItemData,
} from './TaskItem.ts';
import { TaskCheck } from '@/components/check/TaskCheck';
import type { TaskListItem, TaskSaveParams } from '@xiaou66/todo-plugin';
import { ExtensionManager } from '@/extension';
import { Message } from '@arco-design/web-vue';
import {
  draggable,
  dropTargetForElements,
} from '@atlaskit/pragmatic-drag-and-drop/element/adapter';
import { combine } from '@atlaskit/pragmatic-drag-and-drop/combine';
import {
  attachClosestEdge,
  type Edge,
  extractClosestEdge,
} from '@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge';
import TaskDropShadow from './TaskDropShadow.vue';
import { disableNativeDragPreview } from '@atlaskit/pragmatic-drag-and-drop/element/disable-native-drag-preview';
import { isTaskGroupData } from '@/components/Tasks/TaskItem/TaskGroup.ts';
import { useTaskSetting } from '@/views/Setting/TaskSetting.ts';
import { useTaskDragContext } from './TaskDragWrapper.ts';
import {useTaskViewProvide, TaskStatusSelect, useTaskContextMenu} from '../index.ts';
import dayjs from 'dayjs';
import TaskConstants from '@/constants/tasks/TaskConstants.ts';
import jsUtil from '@/utils/jsUtil.ts';
import { cloneDeep } from "es-toolkit";

const props = withDefaults(defineProps<{
  taskItem: TaskListItem;
  showType: 'card' | 'default';
  disableDrag?: boolean;
  className?: string | any;
  showCustomTaskMain?: boolean;
}>(), {
  disableDrag: false,
  className: '',
  showCustomTaskMain: false,
});

const emits = defineEmits<{
  click: [item: TaskListItem];
  dragStart: [moveTask: TaskListItem];
  dragEnd: [
    sourceTask: TaskListItem,
    targetTask: TaskListItem,
    newTask: Partial<TaskListItem> | undefined,
    type: 'top' | 'bottom',
  ];
  save: [item: TaskSaveParams];
}>();

const taskRef = ref<HTMLDivElement>();

type DragState =
  | {
      type: 'idle';
    }
  | {
      type: 'preview';
      container: HTMLElement;
    }
  | {
      type: 'is-dragging';
    }
  | {
      type: 'is-dragging-over';
      closestEdge: Edge | null;
      dragging: DOMRect;
    }
  | {
      type: 'is-dragging-and-left-self';
    };
const idle: DragState = { type: 'idle' };
const dragState = ref<DragState>({ type: 'idle' });

const [taskDragContext, setTaskDragContext] = useTaskDragContext();
const taskViewProvide = useTaskViewProvide();
let cleanup = () => {};
onMounted(() => {
  if (!taskRef.value) {
    return;
  }
  cleanup = combine(
    draggable({
      element: taskRef.value,
      getInitialData({ element }) {
        return getTaskItemData({
          id: props.taskItem.taskId,
          task: props.taskItem,
          rect: element.getBoundingClientRect(),
        });
      },
      onGenerateDragPreview({ nativeSetDragImage, source }) {
        // scrollJustEnoughIntoView({ element: source.element });
        // 禁用原生拖拽预览
        disableNativeDragPreview({ nativeSetDragImage });
      },
      onDragStart({ location, source }) {
        console.log('开始拖拽', location);
        dragState.value = { type: 'is-dragging' };
        console.log(location.current);
        const { clientX, clientY } = location.current.input;
        // 计算鼠标点击位置相对于拖拽项的偏移量
        const rect = taskRef.value!.getBoundingClientRect();
        console.log('rect', rect);
        const offsetX = clientX - rect.left;
        const offsetY = clientY - rect.top;
        setTaskDragContext({
          task: props.taskItem,
          width: taskRef.value!.clientWidth,
          x: clientX,
          y: clientY,
          offsetX,
          offsetY,
        });
        emits('dragStart', props.taskItem);
        return {
          id: props.taskItem.taskId,
          data: props.taskItem,
        };
      },
      onDrag({ location }) {
        // console.log(location.current.dropTargets[0]);
        const { clientX, clientY } = location.current.input;
        setTaskDragContext({
          x: clientX,
          y: clientY,
        });
      },
      onDrop({ location, source }) {
        const target = location.current.dropTargets[0];
        dragState.value = idle;
        console.log('taskItem-Drop', source, target);
        // debugger;
        if (!isTaskItemData(source.data)) {
          setTaskDragContext(undefined);
          return;
        }
        console.log('group-onDrop----------------------------', {
          ...source.data.task,
          ...(taskDragContext.value?.newTask || {}),
        });
        if (isTaskGroupData(target.data)) {
          taskViewProvide.saveTask({
            ...source.data.task,
            ...(taskDragContext.value?.newTask || {}),
          });
          setTaskDragContext(undefined);
          return;
        }

        if (!isTaskDropTargetData(target?.data)) {
          setTaskDragContext(undefined);
          return;
        }
        const newTask = taskDragContext.value?.newTask;
        if (newTask && newTask.finish) {
          emits('save', JSON.parse(JSON.stringify({
            ...props.taskItem,
            ...newTask,
            taskStatus: 100,
          })));
        } else {
          if (source.data.task.finish) {
            if (newTask && newTask.taskStatus === undefined) {
              newTask.taskStatus = 0;
            }
          }
          emits(
            'dragEnd',
            source.data.task,
            target.data.task,
            newTask,
            extractClosestEdge(target.data) as 'top' | 'bottom',
          );
        }
        setTaskDragContext(undefined);
      },
      canDrag(args) {
        // console.log('canDrop', args);
        if (props.disableDrag) {
          return false;
        }
        if (taskViewProvide && taskViewProvide.getScene) {
          return taskViewProvide.getScene() === 'default';
        }
        return true;
      },
    }),
    dropTargetForElements({
      element: taskRef.value,
      canDrop({ source }) {
        return true;
      },
      getData(args) {
        // console.log('dropTargetForElements--getData', args);
        return attachClosestEdge(
          getTaskDropTargetData({
            id: props.taskItem.taskId,
            task: props.taskItem,
          }),
          {
            element: taskRef.value!,
            input: args.input,
            allowedEdges: ['top', 'bottom'],
          },
        );
      },
      getIsSticky() {
        return true;
      },
      onDragEnter({ self, source }) {
        const closestEdge = extractClosestEdge(self.data);
        if (!props.disableDrag) {
          dragState.value = {
            type: 'is-dragging-over',
            dragging: source.data.rect as DOMRect,
            closestEdge,
          };
        }
      },
      onDrag(args) {
        const closestEdge = extractClosestEdge(args.self.data);
        // Only need to update react state if nothing has changed.
        if (!props.disableDrag) {
          if (
            dragState.value.type !== 'is-dragging-over' ||
            dragState.value.closestEdge !== closestEdge
          ) {
            dragState.value = {
              type: 'is-dragging-over',
              dragging: args.source.data.rect as DOMRect,
              closestEdge,
            };
          }
        }
      },
      onDragLeave({ source }) {
        // console.log('onDragLeave', source.data.data.taskId, props.taskItem.taskId)
        if (!isTaskItemData(source.data)) {
          return;
        }
        const data = source.data;
        if (data.task.taskId === props.taskItem.taskId) {
          dragState.value = { type: 'is-dragging-and-left-self' };
          return;
        }
        dragState.value = idle;
        console.log('离开拖拽');
      },
      onDrop(args) {
        dragState.value = idle;
        console.log('放下拖拽', args);
        console.log('');
        // setTaskDragContext(undefined);
      },
    }),
  );
});

onBeforeUnmount(() => {
  cleanup();
});

function handleTaskClick(taskItem: TaskListItem) {
  emits('click', taskItem);
}

function timeFormat(dateStr: string, timeStr: string) {
  return dayjs(dateStr + ' ' + timeStr).format('HH:mm');
}

const taskLevel = computed(() => {
  return TaskConstants.PRIORITY_SELECT_OPTIONS_TAG.find(
    (item) => props.taskItem.taskLevel && item.value === props.taskItem.taskLevel.toString(),
  )!;
});

const checkedStatus = ref(!!props.taskItem.finish);

function finishTaskStatusAction() {
  const taskItem = props.taskItem;
  const oldStatus = taskItem.taskStatus;
  emits('save', {
    ...taskItem,
    taskStatus: 100,
  });
  const messageReturn = Message.success({
    position: 'bottom',
    content: () =>
      h('div', {}, [
        h('div', { class: 'u-fx u-gap10' }, [
          h('div', {}, '任务已完成'),
          h('iconpark-icon', {
            name: 'return',
            size: 14,
            style: {
              cursor: 'pointer',
            },
            onClick: () => {
              console.log('taskViewProvide', taskViewProvide);
              taskViewProvide.saveTask({
                ...taskItem,
                docId: 'finish/' + taskItem.docId,
                taskStatus: oldStatus,
              });
              messageReturn.close();
              checkedStatus.value = false;
            },
          }),
        ]),
      ]),
    duration: 3000,
  });
}
function handleFinishTaskStatus(val: boolean) {
  checkedStatus.value = true;
  debugger
  if (val) {
    const { taskFinishSoundEffect } = useTaskSetting();
    if (taskFinishSoundEffect.value) {
      const mp3 = new Audio(new URL('@/assets/soundEffect/task-completion.mp3', import.meta.url).href);
      mp3.loop = false;
      mp3.volume = 0.1;
      mp3.play().finally(() => {
        mp3.remove();
      });
    }
    setTimeout(() => {
      finishTaskStatusAction();
    }, 300);
  } else {
    // 重完结中移出
    ExtensionManager.getTaskInstance()
      .restartTask(props.taskItem)
      .then(() => {
        // taskViewProvide.refreshData();
        window.dispatchEvent(new CustomEvent('taskList::refreshAll'));
      });
  }
}
</script>

<template>
  <div class="u-pos-rel" :class="className">
    <TaskDropShadow
      v-if="dragState.type === 'is-dragging-over' && dragState.closestEdge === 'top'"
      :dragging="dragState.dragging"
    />
    <div
      ref="taskRef"
      class="task-item u-fx u-fac u-gap5"
      :class="{
          hidden:
          dragState.type === 'is-dragging-and-left-self' || dragState.type === 'is-dragging',
          card: showType === 'card',
          list: showType === 'default',
          // dragging: props.taskItem.taskId === dropItem?.id,
          // 'old-dragging-position': isDragging,
          // 'is-over': isOver,
        }"
      @click="handleTaskClick(taskItem)"
      @contextmenu="(e) => useTaskContextMenu().show(taskItem, e)"
    >
      <!--{{ props.taskItem.taskId }}-{{ props.taskItem.sort }}-{{ props.taskItem.finish }}&#45;&#45;{{ checkedStatus }}-->
      <div class="check-area">
        <TaskCheck
          v-if="!props.taskItem.deleted"
          v-model:model-value="props.taskItem.finish"
          :color="taskLevel?.color"
          @change="handleFinishTaskStatus"
        />
        <div v-else>
        </div>
      </div>
      <div class="u-w-full">
        <div>
          <slot v-if="showCustomTaskMain"
                name="custom-task-main" :taskItem="taskItem">

          </slot>
          <div v-else>
            <div class="title">
              <div>{{ taskItem.taskTitle }}</div>
              <slot name="titleSuffix"></slot>
            </div>
            <!--region 列表-->
            <div v-if="showType === 'default'" class="u-fx u-fac u-f-between extend">
              <div class="u-fx">
                <div v-if="taskItem.taskStartDate">
                  <span>{{ jsUtil.dateFormatLabel(taskItem.taskStartDate) }}</span>
                  <span v-if="taskItem.taskStartTime" style="padding-left: 2px">
                    {{ timeFormat(taskItem.taskStartDate, taskItem.taskStartTime) }}
                  </span>
                </div>
                <div v-if="taskItem.taskDateType === 'dateSegment'" style="padding: 0 1px">-</div>
                <div v-if="taskItem.taskDateType === 'dateSegment' && taskItem.taskEndDate">
                  <span>{{ jsUtil.dateFormatLabel(taskItem.taskEndDate) }}</span>
                  <span v-if="taskItem.taskEndTime" style="padding-left: 2px">
                    {{ timeFormat(taskItem.taskEndDate, taskItem.taskEndTime) }}
                  </span>
                </div>
              </div>
              <div class="u-fx u-fac remind" @click.stop>
                <TaskStatusSelect
                  v-model:model-value="taskItem.taskStatus"
                  size="mini"
                  type="button"
                  :group-id="taskItem.taskGroupId"
                  @change="(taskStatus: number) => emits('save', { ...taskItem, taskStatus })"
                />
                <!--                <iconpark-icon name="alarm-clock"></iconpark-icon>-->
              </div>
            </div>
            <!--endregion-->
          </div>
        </div>
        <div>
          <div class="extend" v-if="showType === 'card'">
            <div>
              <div v-if="taskItem.taskStartDate">
                <span>{{ jsUtil.dateFormatLabel(taskItem.taskStartDate) }}</span>
                <span v-if="taskItem.taskStartTime" style="padding-left: 2px">
                    {{ timeFormat(taskItem.taskStartDate, taskItem.taskStartTime) }}
                  </span>
              </div>
            </div>
            <div class="time red">{{ taskItem.taskRemindTime || '' }}</div>
            <!--          <div class="u-fx u-fac remind">-->
            <!--            <iconpark-icon name="alarm-clock"></iconpark-icon>-->
            <!--          </div>-->
          </div>
        </div>
      </div>
    </div>
    <TaskDropShadow
      v-if="dragState.type === 'is-dragging-over' && dragState.closestEdge === 'bottom'"
      :dragging="dragState.dragging"
    />
    <!--      <DropIndicator v-if="dragState.type === 'is-dragging-over' && dragState.closestEdge"-->
    <!--                     :edge="dragState.closestEdge"-->
    <!--                     gap="8px" />-->
  </div>
</template>
<style lang="less" scoped>
body[arco-theme='dark'] {
  .task-item {
    &:hover {
      background: var(--color-neutral-3);
    }
    &.list {
      border-bottom: 1px solid var(--color-neutral-3);
    }
  }
}
.task-item {
  align-items: flex-start;
  width: 100% !important;
  padding: 8px 6px;
  background-color: var(--main-background-transparent);
  border-radius: 3px;
  transition: background 240ms linear;
  &.hidden {
    display: none;
  }
  // fix 老拖拽位置的问题, 去掉 hover 颜色
  &.old-dragging-position {
    background-color: var(--main-background-transparent) !important;
  }
  &.dragging {
    background-color: var(--main-background-transparent);
  }
  &.is-over {
    opacity: 1 !important;
    border: 1px solid #4080ff !important;
  }

  &.list {
    border-bottom: 1px solid var(--color-neutral-2);
  }

  &:hover {
    background: var(--color-neutral-2);
  }

  &.is-over * {
    visibility: hidden !important;
  }

  // 子任务模式的样式
  &.sub-task-mode {
    margin-left: 30px;
  }

  .delete-return {
    font-size: 24px;
    color: var(--color-neutral-4);
    transition: all 250ms linear;
    &:hover {
      color: var(--color-neutral-8);
    }
  }

  .checkbox {
    padding-top: 2px;
  }

  .title {
    font-weight: 500;
    font-size: 14px;
    line-height: 14px;
    display: grid;
    grid-template-columns: 1fr auto;
  }

  .content {
    margin-left: 20px;
    padding-top: 6px;
    color: #a3a3a3;
  }

  .extend {
    font-size: 10px;
    min-height: 16px;
    display: flex;
    align-items: center;
    padding-top: 6px;
    gap: 4px;
    .remind {
    }
    iconpark-icon {
      color: #a3a3a3;
    }

    .time {
      &.red {
        color: #f5222d;
      }
    }
  }
}
</style>
