import { ExtensionManager } from '@/extension';
import { ListSortUtils } from '@/utils/ListSortUtils.ts';
import type {ITaskItem, IViewConfig, TaskListItem, TaskListParams, TaskSaveParams} from '@xiaou66/todo-plugin'
import type { Ref } from 'vue';
const taskItemDataKey = Symbol('task-item-data')
export type TaskItemData = {
  [taskItemDataKey]: true;
  task: TaskListItem;
  rect: DOMRect;
  id: string;
}

/**
 * 类型守卫
 * @param value
 */
export function isTaskItemData(value: Record<string | symbol, unknown>): value is TaskItemData {
  return Boolean(value[taskItemDataKey])
}

/**
 * 数据创建函数
 * @param params
 */
export function getTaskItemData(params: Omit<TaskItemData, typeof taskItemDataKey>): TaskItemData {
  return {
    [taskItemDataKey]: true,
    ...params
  }
}


const taskDropTargetKey = Symbol('task-drop-target');

export type TaskDropTargetData = {
  [taskDropTargetKey]: true;
  task: TaskListItem;
  id: string;
};


export function isTaskDropTargetData(
  value: Record<string | symbol, unknown>,
): value is TaskDropTargetData {
  if (!value) {
    return false;
  }
  return Boolean(value[taskDropTargetKey]);
}


export function getTaskDropTargetData(params: Omit<TaskDropTargetData, typeof taskDropTargetKey>): TaskDropTargetData {
  return {
    [taskDropTargetKey]: true,
    ...params
  };
}

export function useSaveTaskItem() {
  function saveTask(task: TaskSaveParams) {
    const taskInstance = ExtensionManager.getTaskInstance();
    taskInstance.saveTask(task.taskGroupId!, task)
      .then(() => {
        window.dispatchEvent(new CustomEvent('taskList::refreshAll'))
      });
  }
  return saveTask;
}

export function useTaskItemDrag(data: Ref<TaskListItem[]>) {
  // 开始拖拽时的处理, 需要复制拖拽的副本
  function dragStart(moveTask: TaskListItem) {
  }
  async function dragEnd(sourceTask: TaskListItem,
    targetTask: TaskListItem,
    newTask: Partial<TaskListItem> = {},
    type: 'top' | 'bottom') {
    const taskList: TaskListItem[] = data.value;
    const sourceTaskIndex = taskList.findIndex(item => item.taskId === sourceTask.taskId);
    const targetTaskIndex = taskList.findIndex(item => item.taskId === targetTask.taskId);
    console.log('dragEnd', sourceTaskIndex, targetTaskIndex,data.value.length)
    const taskInstance = ExtensionManager.getTaskInstance();

    const groupId = newTask.taskGroupId === undefined ? sourceTask.taskGroupId : newTask.taskGroupId;
    if (Math.abs(sourceTaskIndex - targetTaskIndex) === 1) {
      // 邻居互换直接互换 sort 即可
      const temp = taskList[sourceTaskIndex].sort;
      taskList[sourceTaskIndex].sort = taskList[targetTaskIndex].sort;
      taskList[targetTaskIndex].sort = temp;
      await taskInstance.saveTask(groupId, {
        ...taskList[sourceTaskIndex],
        ...newTask
      });
      await taskInstance.saveTask(groupId, {
        ...taskList[sourceTaskIndex - (sourceTaskIndex - targetTaskIndex)],
      });
    } else {
      debugger
      // 不是邻居了
      console.log(targetTaskIndex, type);
      console.log('targetTaskIndex === data.value.length - 1 && type === \'bottom\'', targetTaskIndex === data.value.length - 1 && type === 'bottom')
      if (targetTaskIndex === 1 && type === 'top') {
        // 插入头部
        const [sort] = await ListSortUtils.getInsertSort(null, taskList[targetTaskIndex]);
        await taskInstance.saveTask(groupId, {
          ...taskList[sourceTaskIndex],
          ...newTask,
          sort
        });
      } else if (targetTaskIndex === data.value.length - 1 && type === 'bottom') {
        // 插入尾部
        const [sort] = await ListSortUtils.getInsertSort(taskList[targetTaskIndex], null);
        await taskInstance.saveTask(groupId, {
          ...taskList[sourceTaskIndex],
          ...newTask,
          sort
        });
      } else {
        // 中间
        const preIndex = type === 'top' ? targetTaskIndex - 1 : targetTaskIndex;
        const [sort, taskItemList] = await ListSortUtils.getInsertSort(taskList[preIndex], taskList[preIndex + 1],
          async (taskItem: ITaskItem, pre: number, next: number) => {
            const taskInstance = ExtensionManager.getTaskInstance();
            const pageResult = await taskInstance.listTask({});
            const list = pageResult.list;
            const index = list.findIndex((task) => task.taskId === taskItem.taskId)
            return pageResult.list.slice(Math.max(index - pre, 0), Math.min(index + next + 1, list.length));
          });

        if (taskItemList) {
          for (const taskItem of taskItemList) {
            await taskInstance.saveTask(groupId, {
              ...taskItem
            });
          }
        }

        await taskInstance.saveTask(groupId, {
          ...taskList[sourceTaskIndex],
          ...newTask,
          sort
        });
      }
    }
    window.dispatchEvent(new CustomEvent('taskList::refreshAll'))
  }

  return {
    dragStart,
    dragEnd,
  }
}
