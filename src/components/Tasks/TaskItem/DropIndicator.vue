<script setup lang="ts">
import type { Edge } from "@atlaskit/pragmatic-drag-and-drop-hitbox/types";

const { edge, gap } = defineProps<{
  edge: Edge;
  gap: string;
}>();

type Orientation = "horizontal" | "vertical";

const edgeToOrientationMap: Record<Edge, Orientation> = {
  top: "horizontal",
  bottom: "horizontal",
  left: "vertical",
  right: "vertical",
};

const orientationStyles: Record<Orientation, string> = {
  horizontal: "drop-indicator-horizontal",
  vertical: "drop-indicator-vertical",
};

const edgeStyles: Record<Edge, string> = {
  top: "drop-indicator-top",
  right: "drop-indicator-right",
  bottom: "drop-indicator-bottom",
  left: "drop-indicator-left",
};
</script>

<template>
  <div
    :class="[
      orientationStyles[edgeToOrientationMap[edge]],
      edgeStyles[edge],
      'drop-indicator'
    ]"
  />
</template>

<style lang="less">
@stroke-size: 2px;
@terminal-size: 8px;
@indicator-color: #2563eb;

.drop-indicator {
  position: absolute;
  z-index: 10;
  background-color: @indicator-color;
  pointer-events: none;
  box-sizing: border-box;
  left: 0;
  right: 0;

  // 圆圈
  &::before {
    content: '';
    position: absolute;
    width: @terminal-size;
    height: @terminal-size;
    border: @stroke-size solid @indicator-color;
    border-radius: 50%;
    background: white;
    transform: translateY(-3px);
  }

  &-horizontal {
    height: @stroke-size;

    &::before {
      left: -@terminal-size;
    }
  }

  &-vertical {
    width: @stroke-size;

    &::before {
      top: -@terminal-size;
    }
  }

  &-top {
    top: calc(-0.5 * (v-bind(gap) + @stroke-size));
  }

  &-right {
    right: calc(-0.5 * (v-bind(gap) + @stroke-size));
  }

  &-bottom {
    bottom: calc(-0.5 * (v-bind(gap) + @stroke-size));
  }

  &-left {
    left: calc(-0.5 * (v-bind(gap) + @stroke-size));
  }
}
</style>
