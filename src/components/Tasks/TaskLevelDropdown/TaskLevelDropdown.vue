<script setup lang="ts">
import TaskConstants from "@/constants/tasks/TaskConstants.ts";
import { computed} from 'vue';

defineProps<{
  popupContainer?: string | HTMLElement | undefined;
}>();

const modalValue = defineModel('modalValue');


const priority = computed<Record<string, any>>(() => {
  return TaskConstants.PRIORITY_SELECT_OPTIONS_TAG.reduce((obj: Record<string, any>, cur: any) => {
    obj[cur.value] = cur;
    return obj;
  }, {});
});

const emit = defineEmits<{
  blur: [];
}>();

function handleSelectEvent(value: string) {
  modalValue.value = value;
  emit('blur');
}
</script>

<template>
  <t-dropdown trigger="hover"
              placement="top"
              :popupProps="{ attach: popupContainer, overlayClassName: 'u-dropdown-small' }">
    <a-link class="task-level">
      <t-icon class="i-u-mark"
              :style="{color: priority[modalValue as any].color}"></t-icon>
    </a-link>
    <t-dropdown-menu>
      <t-dropdown-item v-for="taskLevel in TaskConstants.PRIORITY_SELECT_OPTIONS_TAG"
                       :key="taskLevel.value as string"
                       @click="handleSelectEvent(taskLevel.value as string)">
        <t-icon class="i-u-mark"
                :style="{color: taskLevel.color}"></t-icon>
      </t-dropdown-item>
    </t-dropdown-menu>
  </t-dropdown>
</template>

<style scoped lang="less">
</style>
