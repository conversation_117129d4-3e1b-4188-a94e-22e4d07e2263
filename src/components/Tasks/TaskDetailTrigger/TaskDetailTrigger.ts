import {createVNode, ref, render, inject} from 'vue';
import { TaskDetailTrigger } from '@/components/Tasks/TaskDetailTrigger/index.ts';
import tippy from 'tippy.js';
import type { TaskListItem, TaskSaveParams } from '@xiaou66/todo-plugin';
import {ExtensionManager} from "@/extension";

export interface TaskTriggerOptions {
  firstSave?: (taskInfo: TaskListItem | TaskSaveParams, saveStatus: boolean) => void;
  close?: () => void;
  showTaskDetailDrawer?: (taskInfo: TaskListItem) => void;
}


export function useTaskDetailTrigger(options: TaskTriggerOptions = {}) {
  let instance: any = null;
  const allowCloseTipsy = ref(true);
  function showTrigger(e: MouseEvent,
                       taskInfo: TaskListItem | TaskSaveParams) {
    // 销毁旧的 tippy 实例
    if (instance) {
      instance.destroy();
      instance = null;
    }
    console.log('showTrigger--', taskInfo);
    // 创建弹框内容
    const content = document.createElement('div');
    const vNode = createVNode(
      TaskDetailTrigger,
      {
        taskInfo,
        onCloseStatus: (status: any) => (allowCloseTipsy.value = status),
        close: () =>  {
          options.close?.();
        },
      },
      [],
    );
    render(vNode, content);

    // 创建 tippy 实例
    // @ts-ignore
    instance = tippy(e.target as HTMLElement, {
      content: content,
      zIndex: 99,
      trigger: 'manual',
      placement: 'right',
      interactive: true,
      allowHTML: true,
      appendTo: document.body,
      animation: 'scale',
      hideOnClick: false,
      async onClickOutside(instance, event: any) {
        console.log(event);
        // 这里你可以判断 event.target 是否在你允许的区域内
        // 如果不在，手动关闭
        if (event.target.closest('.tippy-box') ||
            event.target.closest('.t-dropdown') || !allowCloseTipsy.value) {
          return;
        }
        // 保存数据
        const taskDetailTriggerData = document.getElementById('taskDetailTriggerData')
        if (taskDetailTriggerData) {
          const newTaskInfo = JSON.parse(taskDetailTriggerData.getAttribute('data-taskinfo')!) as TaskSaveParams;
          if (newTaskInfo.taskTitle) {
            options.firstSave?.(newTaskInfo, true);
            await ExtensionManager.getTaskInstance().saveTask(newTaskInfo.taskGroupId || 'collectBox', newTaskInfo)
              .then(() => {});
          } else {
            options.firstSave?.(newTaskInfo, false);
          }
        }
        setTimeout(() => {
          options.close?.();
        }, 350)
        instance.hide();
      },
      getReferenceClientRect: () => ({
        width: 0,
        height: 0,
        top: e.clientY,
        bottom: e.clientY,
        left: e.clientX,
        right: e.clientX,
      }),
      onHidden() {
        instance.destroy();
        instance = null;
      },
    });

    instance.show();
  }
  return {
    showTrigger
  }
}
