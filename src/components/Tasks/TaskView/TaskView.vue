<script setup lang="ts">
import { useRouter } from 'vue-router';
import TaskSortDropdown from '../TaskSortDropdown/TaskSortDropdown.vue';
import { onMounted, provide, ref, watch } from 'vue';
import { ExtensionManager } from '@/extension';
import type {
  IViewConfig,
  TaskListItem,
  TaskListParams,
} from '@xiaou66/todo-plugin';
import { PageHeader } from '@xiaou66/u-web-ui';
import { TaskViewLayer } from '@/views/GroupDetail/templates';
import {
  useTaskItemDrag,
  useSaveTaskItem,
} from '@/components/Tasks';
import type { ITaskViewProvide, ITaskViewContext } from './index.ts';
import { taskViewProvideCode } from './index.ts';
import { Modal } from '@arco-design/web-vue';
import { useEventListener } from '@vueuse/core';
import { useAiTaskDrawer } from '@/components/ai';
import { cloneDeep } from 'es-toolkit';
const props = withDefaults(
  defineProps<{
    id: string;
    type: 'group' | 'system' | 'filter' | 'tags';
    defaultViewConfig?: IViewConfig;
    title: string;
    subtitle: string;
    showBack?: boolean;
    searchParams?: TaskListParams;
    scene?: 'finish' | 'delete' | 'default';
  }>(),
  {
    showBack: false,
    searchParams: () => ({}) as TaskListParams,
    scene: 'default',
  },
);

const emits = defineEmits<{
  refresh: [];
}>();

const viewMode = ref<'list' | 'grid'>('list');
const viewConfig = ref<IViewConfig>({
  groupType: 'taskLevel',
  sortType: 'custom',
  showFinish: true
});

watch(() => viewConfig.value, (newValue, oldValue) => {
  viewConfigChange();
  if (newValue.groupType === 'taskStartDate'
    || oldValue.groupType === 'taskStartDate') {
    // 刷新需要刷新出已过期的任务或清理掉之前查询出过期的任务
    refreshData();
  }
}, { deep: true });
watch(() => viewMode.value, () => {
  viewConfigChange();
}, { deep: true });
async function viewConfigChange() {
  if (props.type === 'system') {
    ExtensionManager.getLocalStorageInstance().setData(
      `systemGroup/viewConfig/${props.id}`, cloneDeep(viewConfig.value),
    );
    ExtensionManager.getLocalStorageInstance().setData(
        `systemGroup/viewMode/${props.id}`, cloneDeep(viewMode.value));
  } else if (props.type === 'group') {
    await ExtensionManager.getGroupInstance().saveGroup({
      groupId: props.id,
      viewConfig: cloneDeep(viewConfig.value),
    });
    ExtensionManager.getLocalStorageInstance().setData(
        `group/viewMode/${props.id}`, cloneDeep(viewMode.value));
  }
}
async function loadViewConfig() {
  if (props.type === 'system') {
    viewMode.value = ExtensionManager.getLocalStorageInstance().getData(
        `systemGroup/viewMode/${props.id}`) || 'list';
  } else if (props.type === 'group'){
    viewMode.value = ExtensionManager.getLocalStorageInstance().getData(
        `group/viewMode/${props.id}`) || 'list';
  }
  if (props.defaultViewConfig) {
    console.log('viewConfig', props.defaultViewConfig);
    viewConfig.value = props.defaultViewConfig;
    return;
  }
  if (props.type === 'group') {
    const group = await ExtensionManager.getGroupInstance().getGroup(props.id);
    viewConfig.value = group?.viewConfig || {
      groupType: 'taskLevel',
      sortType: 'custom',
      showFinish: true
    };
  } else if (props.type === 'system') {
    viewConfig.value = ExtensionManager.getLocalStorageInstance().getData<IViewConfig>(
      `systemGroup/viewConfig/${props.id}`,
    ) || {
      groupType: 'taskLevel',
      sortType: 'custom',
      showFinish: true
    };
  }
}

const data = ref<TaskListItem[]>([]);
async function refreshData() {
  const searchParams = cloneDeep<TaskListParams>(props.searchParams);
  console.log('taskView-refreshData-taskList-start', searchParams);
  const taskInstance = ExtensionManager.getTaskInstance();
  const modifySearchParams: TaskListParams = {};
  debugger
  if (viewConfig.value.groupType === 'taskStartDate'
    && searchParams.taskTimeRange
    && searchParams.taskTimeRange.length === 2) {
    // 在时间分类出, 需要加载出全部已过期的任务
    // fix 这里开始不能设置为 0 因为后面检索的时候都是使用 !字段 判断的会导致结果不准确
    modifySearchParams.taskTimeRange =  [1, searchParams.taskTimeRange[1]]
  }
  console.log('taskview-refreshData', {
    ...searchParams,
    ...modifySearchParams,
  });
  taskInstance
    .listTask({
      ...searchParams,
      ...modifySearchParams,
    })
    .then((res) => {
      data.value = res.list;
      console.log('taskView-refreshData-taskList-end', data.value);
    });
  if (viewConfig.value.showFinish) {
    ExtensionManager.getTaskInstance().listTask({
      ...searchParams,
      searchType: 'finish',
    }).then(res => {
      finishData.value = res.list;
    })
  } else {
    finishData.value = [];
  }
  emits('refresh');
}

useEventListener(window, 'tasklist::taskView', () => {
  refreshData();
});
useEventListener(window, 'taskList::refreshAll', () => {
  refreshData();
});
onMounted(async () => {
  await loadViewConfig();
  loadContext();
  refreshData();
});

watch(
  () => [props.id, props.searchParams],
  async () => {
    debugger
    console.log('重新加载配置');
    await loadViewConfig();
    loadContext();
    console.log('viewConfig', viewConfig.value);
    refreshData();
  }
, { deep: true });

const router = useRouter();
function handleBack() {
  router.back();
}

const context = ref<ITaskViewContext>({});
// TODO 这里一定要优化一下，出错几率会增加
function loadContext() {
  const currentContext: any = {};
  if (props.type === 'group') {
    currentContext.groupId = props.id;
  } else if (props.type === 'tags') {
    currentContext.tagNameList = [props.title];
  } else if (props.type === 'filter') {
    const searchParams =  props.searchParams;
    if (searchParams.taskGroupIdList && searchParams.taskGroupIdList.length > 0) {
      currentContext.groupId = searchParams.taskGroupIdList[0];
    }
    if (searchParams.tagNameList && searchParams.tagNameList.length > 0) {
      currentContext.tagNameList = searchParams.tagNameList[0];
    }
    if (searchParams.taskLevelList && searchParams.taskLevelList.length > 0) {
      currentContext.taskLevel = searchParams.taskLevelList[0];
    }
  } else if (props.id === 'collectBox') {
    currentContext.groupId = props.id;
  }
  context.value = currentContext

  console.log('loadContext', context.value);
}
const saveTask = useSaveTaskItem();
const taskItemDrag = useTaskItemDrag(data);
provide<ITaskViewProvide>(taskViewProvideCode, {
  context,
  refreshData,
  getScene() {
    return props.scene;
  },
  ...taskItemDrag,
  saveTask,
});
async function handleCleanAllDeleteTask() {
  Modal.confirm({
    title: '二次提醒',
    content: '确认要清理这些任务吗, 清理后将无法还原的',
    cancelText: '放弃',
    okText: '清理',
    okButtonProps: {
      status: 'danger',
    },
    async onOk() {
      setTimeout(() => {
        const mp3 = new Audio(new URL('@/assets/soundEffect/delete-all.mp3', import.meta.url).href);
        mp3.loop = false;
        mp3.volume = 0.1;
        mp3.play().finally(() => {
          mp3.remove();
        });
      })
      await ExtensionManager.getTaskInstance().cleanAllDeleteTask();
      refreshData().then(() => {});
    },
  });
}

function handleOpenAi() {
  const aiTaskDrawer = useAiTaskDrawer();
  aiTaskDrawer.show({
    ...props.searchParams,
  });
}

const finishData = ref<TaskListItem[]>([]);
function handleHideFinish() {
  viewConfig.value.showFinish = false;
  finishData.value.length = 0;
}

async function handleShowFinish() {
  viewConfig.value.showFinish = true;
  viewConfigChange()
  ExtensionManager.getTaskInstance().listTask({
    ...props.searchParams,
    searchType: 'finish',
  }).then(res => {
    finishData.value = res.list;
  })
}
</script>

<template>
  <PageHeader :title="title"
              :subtitle="subtitle"
              size="mini">
    <template #prefix>
      <slot name="header-title-prefix" />
    </template>
    <template #title>
      <div class="text-lg">
        {{ title }}
      </div>
    </template>
    <template #extra>
      <!-- 标题右边菜单 -->
      <div v-if="scene === 'default'" class="u-fx u-fac">
        <!--  AI 总结  -->
        <a-tooltip content="AI 总结"
                   mini>
          <t-button id="ai-summary"
                    theme="default"
                    size="small"
                    variant="text"
                    @click="handleOpenAi">
            <t-icon class="i-u-ai-edit " style="color: #9686F8;"></t-icon>
          </t-button>
        </a-tooltip>
        <TaskSortDropdown
          v-model:group-type="viewConfig.groupType"
          v-model:sort-type="viewConfig.sortType"
        />
        <t-popup class="u-transparent min-dropdown-select"
                 trigger="click">
          <t-button size="small"
                    theme="default"
                    variant="text"
                    class="view-more-button">
            <template #icon>
              <t-icon class="i-u-more"></t-icon>
            </template>
          </t-button>
          <template #content>
            <div class="min-dropdown-select-options view-more">
              <div>
                <div>
                  <div class="u-tips">视图</div>
                  <div class="w-full flex justify-end">
                    <t-radio-group class="w-full" v-model:value="viewMode"
                                   size="small"
                                   type="button"
                                   variant="default-filled">
                      <t-tooltip content="看板模式">
                        <t-radio-button class="w-1/2"
                                        value="grid">
                          <div class="u-cc w-full flex gap-1">
                            <t-icon class="u-cc i-u-column text-sm" />
                            <div>看板</div>
                          </div>
                        </t-radio-button>
                      </t-tooltip>
                      <t-tooltip content="列表模式">
                        <t-radio-button class="w-1/2" value="list">
                          <div class="u-cc w-full flex gap-1">
                            <t-icon class="u-cc i-u-mindmap-list"></t-icon>
                            <div>列表</div>
                          </div>
                        </t-radio-button>
                      </t-tooltip>
                      <!--                  <a-tooltip mini>
                                          <template #content>列表模式</template>
                                          <a-radio value="list">
                                            <iconpark-icon
                                              name="mindmap-list"
                                              size="14"
                                              style="padding-top: 4px"
                                            ></iconpark-icon>
                                          </a-radio>
                                        </a-tooltip>-->
                    </t-radio-group>
                  </div>
                </div>
                <div class="u-tips" style="margin-top: 6px;">
                  <t-button v-if="viewConfig.showFinish"
                            class="w-full u-web-transparent"
                            theme="default"
                            size="small"
                            @click="handleHideFinish">
                    <template #icon>
                      <t-icon class="u-cc i-u-preview-close"></t-icon>
                    </template>
                    隐藏已完成
                  </t-button>
                  <t-button v-else
                            class="w-full u-web-transparent"
                            theme="default"
                            size="small"
                            @click="handleShowFinish">
                    <template #icon>
                      <t-icon class="u-cc i-u-eyes"></t-icon>
                    </template>
                    显示已完成
                  </t-button>
                </div>
              </div>
            </div>
          </template>
        </t-popup>
      </div>
      <div v-if="scene === 'delete'">
        <a-button
          id="clean-all-delete-task"
          type="text"
          status="danger"
          size="mini"
          shape="round"
          @click="handleCleanAllDeleteTask"
          :disabled="!data.length"
        >
          清空已删除
        </a-button>
      </div>
    </template>
  </PageHeader>
  <div class="task-detail">
    <TaskViewLayer
      :viewMode="viewMode"
      :viewConfig="viewConfig"
      :scene="scene"
      :data="data"
      :finishData="finishData" />
  </div>
</template>

<style lang="less" scoped>
.arco-page-header {
  box-sizing: border-box;
  padding: 0;
  :deep(.arco-page-header-wrapper) {
    padding: 0;
    margin-bottom: 6px;
  }
  :deep(.arco-page-header-content) {
    box-sizing: border-box;
    padding: 0;
  }
}

:deep(.arco-btn-secondary, .arco-btn-secondary[type='button'], .arco-btn-secondary[type='submit']) {
  background-color: transparent;
}
:deep(.arco-btn-group .arco-btn-secondary:not(:last-child)) {
  border-right: none;
}
:deep(.arco-dropdown) {
  border: none !important;
}
</style>
