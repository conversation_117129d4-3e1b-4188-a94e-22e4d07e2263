import type { Ref } from 'vue';
import type { TaskSaveParams, TaskListItem, IViewConfig } from '@xiaou66/todo-plugin'
import {inject} from "vue";

export interface ITaskViewContext {
  groupId?: string;
  tagNameList?: string[];
  taskLevel?: string;
}

export interface ITaskViewProvide {
  context?: Ref<ITaskViewContext>;
  refreshData: () => void;
  getScene: () =>  'finish' | 'delete' | 'default';
  /**
   * 添加任务
   * @param task 任务
   */
  saveTask(task: TaskSaveParams): void;
  dragStart(moveTask: TaskListItem): void;
  dragEnd(sourceTask: TaskListItem, targetTask: TaskListItem, newTask: Partial<TaskListItem> | undefined, type: 'top' | 'bottom'): void;
}

export const taskViewProvideCode = 'taskViewProvide';
export function useTaskViewProvide(): ITaskViewProvide  {
  return inject<ITaskViewProvide>(taskViewProvideCode)!;
}
