import type { ITaskItem } from '@xiaou66/todo-plugin';
import { computed, type Ref } from 'vue';
import dayjs from 'dayjs';

export type DateInfo = Pick<ITaskItem, 'taskDateType' | 'taskStartDate' | 'taskStartTime' | 'taskEndDate' | 'taskEndTime'>;
export function useTaskDateTimeActions(dateInfo:  Ref<DateInfo | undefined>, save?: () => void) {
  function autoDateTime(time = dayjs()) {
    if (!dateInfo.value) {
      return;
    }
    // 计算下一个 10 分钟的时间
    const nextTenMinutes = Math.ceil(time.minute() / 15) * 15;
    // 如果下一个 10 分钟数为 60，则需要进位到下一个小时
    const newTime = nextTenMinutes === 60
      ? time.add(1, 'hour').minute(0)
      : time.minute(nextTenMinutes);
    dateInfo.value.taskStartTime = newTime.format("HH:mm");
    dateInfo.value.taskEndTime = newTime.add(30, 'minutes')
      .format("HH:mm");
  }

  function switchTimeDateType() {
    if (!dateInfo.value) {
      return;
    }
    if (dateInfo.value.taskDateType === 'date') {
      if (!dateInfo.value.taskStartDate) {
        dateInfo.value.taskStartDate = dayjs().format('YYYY-MM-DD');
      }
      dateInfo.value.taskEndDate = '';
      dateInfo.value.taskStartTime = '';
      dateInfo.value.taskEndTime = '';
    } else if (dateInfo.value.taskDateType === 'dateSegment') {
      dateInfo.value.taskStartDate = dayjs(dateInfo.value.taskStartDate || Date.now()).format('YYYY-MM-DD');
      if (!dateInfo.value.taskEndDate) {
        autoDateTime();
      }
      if (!dateInfo.value.taskEndDate) {
        dateInfo.value.taskEndDate = dateInfo.value.taskStartDate;
      }
    }
    save && save();
  }

  /**
   * 处理任务结束时间的小时禁用逻辑
   *
   * @returns 返回禁用的小时数组
   */
  function handleTaskEndHours() {
    const disabledHours: any[] = [];
    if (!dateInfo.value) {
      return disabledHours;
    }
    if (dateInfo.value.taskStartTime) {
      const taskStartTime = dayjs(dateInfo.value.taskStartTime, 'HH:mm');
      console.log(taskStartTime)
      // 获取任务开始时间的小时数
      const startHour = taskStartTime.hour();
      console.log('startHour', startHour)
      // 循环从 0 到 startHour - 1，添加到禁用小时数组
      for (let hour = 0; hour < startHour; hour++) {
        disabledHours.push(hour);
      }
    }
    console.log('disabledHours', disabledHours)
    return disabledHours;
  }

  function handleTaskEndMinutes(selectedHour?: number) {
    const disabledMinutes = [] as number[];
    if (!dateInfo.value) {
      return disabledMinutes;
    }
    if (dateInfo.value.taskStartTime) {
      const taskStartTime = dayjs(dateInfo.value.taskStartTime, 'HH:mm'); // 解析任务开始时间
      const startHour = taskStartTime.hour(); // 获取任务开始的小时
      const startMinute = taskStartTime.minute(); // 获取任务开始的分钟
      if (selectedHour === startHour) {
        // 如果选定的小时等于任务开始小时，则禁用当前小时之前的分钟
        for (let minute = 0; minute <= startMinute; minute++) {
          disabledMinutes.push(minute);
        }
      }
    }
    return disabledMinutes; // 返回禁用的分钟数组
  }

  function autoAdjustDateTime() {
    if (!dateInfo.value) {
      return;
    }
    const {taskStartDate, taskStartTime, taskEndDate, taskEndTime} = dateInfo.value;
    if (taskStartDate && taskEndDate && taskStartTime && taskEndTime) {
      const startTime = dayjs(taskStartDate + ' ' + taskStartTime);
      const endTime = dayjs(taskEndDate + ' ' + taskEndTime);
      if (startTime && endTime) {
        if (endTime.isBefore(startTime)) {
          dateInfo.value.taskEndDate = taskStartDate;
          const time = dayjs(taskStartTime, 'HH:mm');
          autoDateTime(time);
        }
      }
    } else if (taskStartDate) {
      const startDate = dayjs(taskStartDate);
      const endDate = dayjs(taskEndDate);
      if (endDate.isBefore(startDate)) {
        dateInfo.value.taskEndDate = dateInfo.value.taskStartDate;
      }
    }

    save && save();
  }

  function switchAllDay(status: boolean) {
    if (!dateInfo.value) {
      return;
    }
    if (status) {
      dateInfo.value.taskStartTime = '';
      dateInfo.value.taskEndTime = '';
    } else {
      autoDateTime();
    }
    save && save();
  }

  function autoDefaultTaskStartTime(value: boolean) {
    if (!value) {
      return;
    }
    if (!dateInfo.value) {
      return;
    }
    if (dateInfo.value.taskStartTime) {
      return;
    }
    const time = dayjs()
    // 计算下一个 10 分钟的时间
    const nextTenMinutes = Math.ceil(time.minute() / 15) * 15;
    // 如果下一个 10 分钟数为 60，则需要进位到下一个小时
    const newTime = nextTenMinutes === 60
      ? time.add(1, 'hour').minute(0)
      : time.minute(nextTenMinutes);
    dateInfo.value.taskStartTime = newTime.format("HH:mm");

    save && save();
  }

  const isAllDay = computed(() => dateInfo.value && !dateInfo.value.taskStartTime && !dateInfo.value.taskEndTime);

  return {
    isAllDay,
    switchAllDay,
    autoDefaultTaskStartTime,
    autoAdjustDateTime,
    handleTaskEndMinutes,
    handleTaskEndHours,
    switchTimeDateType,
  }
}
