<script setup lang="ts">
import {onMounted, ref, watch, computed} from "vue";
import type { ITaskStatus } from "@xiaou66/todo-plugin";
import { ExtensionManager } from "@/extension";
import {keyBy} from "es-toolkit";

const props = withDefaults(defineProps<{
  groupId: string
  transparent?: boolean
  size: 'mini' | 'small'
  type: 'button' | 'select'
}>(), {
  transparent: false
});

const modelValue = defineModel<number>('modelValue');
const popupVisible = ref(false);
const emits = defineEmits<{
  change: [value: number];
}>();


function handleButtonChange(value: number) {
  modelValue.value = value;
  popupVisible.value = false;
  emits('change', value);
}

function handleSelectChange(value: number) {
  emits('change', value);
}

const taskStatusOptions = ref<ITaskStatus[]>([]);
const currentTaskStatus = computed(() => {
  return taskStatusOptions.value.find(item => item.id === modelValue.value);
})
async function loadTaskStatusOptions() {
  taskStatusOptions.value = await ExtensionManager.getGroupInstance()
    .getTaskStatusList(props.groupId);
}
watch(() => props.groupId, () => {
  loadTaskStatusOptions();
});
onMounted(() => {
  loadTaskStatusOptions();
});


</script>

<template>
  <div v-if="currentTaskStatus">
    <a-dropdown v-if="type === 'button'"
                class="mini-gap-dropdown-select"
                v-model:popup-visible="popupVisible"
                trigger="hover">
      <a-tag size="small"
             class="u-pointer u-radius10"
             style="overflow: hidden"
             :color="currentTaskStatus.color">
        {{currentTaskStatus.label}}
      </a-tag>
      <template #content>
        <div class="min-dropdown-select-options taskStatusButtonSelect">
          <div v-for="taskStatusOption in taskStatusOptions"
               v-show="taskStatusOption.id !== modelValue!"
               :key="taskStatusOption.id"
               @click="handleButtonChange(taskStatusOption.id)">
            <a-tag :color="taskStatusOption.color">
              {{taskStatusOption.label}}
            </a-tag>
          </div>
        </div>
      </template>
    </a-dropdown>
    <a-select v-else-if="type === 'select'"
              v-model:model-value="modelValue"
              :size="size"
              :class="{ 'u-transparent': transparent }"
              :trigger-props="{ contentClass: 'u-select-options min' }"
              @change="handleSelectChange">
      <template #label="{ data }">
        <a-tag :color="currentTaskStatus.color">{{ data.label }}</a-tag>
      </template>
      <a-option v-for="taskStatusOption in taskStatusOptions"
                v-show="taskStatusOption.id !== modelValue!"
                :key="taskStatusOption.id"
                :value="taskStatusOption.id" style="padding: 2px;">
        <a-tag class="u-w-full" style="width: 100%"
               :color="taskStatusOption.color">
          {{ taskStatusOption.label }}
        </a-tag>
      </a-option>
    </a-select>
  </div>
</template>

<style  lang="less">
.taskStatusButtonSelect {
  display: flex;
  flex-direction: column;
  border: none !important;
  >div {
    cursor: pointer;
  }
}
</style>
