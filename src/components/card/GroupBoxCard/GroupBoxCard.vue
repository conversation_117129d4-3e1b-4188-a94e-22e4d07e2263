<script setup lang="ts">
import type { IGroupInfoItem } from '@xiaou66/todo-plugin'
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const props = withDefaults(defineProps<{
  groupInfo: IGroupInfoItem
}>(), {
});

const percent = computed(() => {
  if (props.groupInfo.total <= 0 && props.groupInfo.finish === 0) {
    return -1;
  }
  if (props.groupInfo.finish === 0) {
    return 0;
  }
  return Math.round((props.groupInfo.finish / props.groupInfo.total) * 100) / 100;
});
const router = useRouter();
function handleClick() {
  console.log('1111')
  router.push({
    name: 'groupDetail',
    params: {
      groupId: props.groupInfo.groupId
    }
  })
}
</script>
<template>
  <div class="card"
       @click="handleClick">
    <div>
      <div class="top-section">
        <img class="u-ban" alt=""  v-imageUrl="groupInfo.cover">
        <div class="icons">
          <div class="title">
            <div>{{ groupInfo.groupName }}</div>
          </div>
          <div class="social-media">
          </div>
        </div>
      </div>
      <div class="bottom-section">
        <div v-if="percent !== -1">
          <div class="dot-group">
            <div class="u-fx u-fac">
              <div class="dot" style="background:#000;">
              </div>
              <div>
                总数: {{groupInfo.total}}
              </div>
            </div>
            <div class="u-fx u-fac">
              <div class="dot" style="background: #73d13d;">
              </div>
              <div>
                完成: {{groupInfo.finish}}
              </div>
            </div>
            <div class="u-fx u-fac">
              <div class="dot" style="background:#000;">
              </div>
              <div>
                比例: {{ percent * 100 }}%
              </div>
            </div>
            <div class="u-fx u-fac">
              <div class="dot" style="background: #40a9ff;">
              </div>
              <div>
                未做: {{ groupInfo.total - groupInfo.finish }}
              </div>
            </div>
          </div>
          <a-progress class="total-progress" :percent="percent" :show-text="false"/>
        </div>
        <div v-else>
          <div class="u-fx u-fac u-gap5" style="justify-content: center; height: 40px; font-weight: 500;">
            <div>
              <iconpark-icon name="tips" style="font-size: 16px" />
            </div>
            <div>
              当前未统计到数据
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
@card-bg-color: #ffffff;
.card {
  width: 230px;
  height: 190px;
  border-radius: 10px;
  background: @card-bg-color;
  padding: 8px;
  overflow: hidden;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transition: box-shadow 200ms linear;
  &:hover {
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 20px 0px;
  }
}

.card:hover {
  /*transform: scale(1.05);*/
}

.card .top-section {
  height: 120px;
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(45deg, rgb(4, 159, 187) 0%, rgb(80, 246, 255) 100%);
  position: relative;
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  img {
    border-radius: 10px;
  }
}

.card .top-section .border {
  border-bottom-right-radius: 10px;
  height: 30px;
  width: 130px;
  background: @card-bg-color;
  position: relative;
  transform: skew(-40deg);
  box-shadow: -10px -10px 0 0 @card-bg-color;
}

.card .top-section .border::before {
  content: "";
  position: absolute;
  width: 15px;
  height: 15px;
  top: 0;
  right: -15px;
  background: rgba(255, 255, 255, 0);
  border-top-left-radius: 10px;
  box-shadow: -5px -5px 0 2px @card-bg-color;
}

/*.card .top-section::before {
  content: "";
  position: absolute;
  top: 30px;
  left: 0;
  background: rgba(255, 255, 255, 0);
  height: 15px;
  width: 15px;
  border-top-left-radius: 15px;
  box-shadow: -5px -5px 0 2px @card-bg-color;
}*/

.card .top-section .icons {
  position: absolute;
  top: 0;
  width: 100%;
  height: 30px;
  display: flex;
  justify-content: space-between;
  background: rgba(255, 255, 255, .5);
  backdrop-filter: blur(6px);
}

.card .top-section .icons .title {
  width: 100%;
  height: 100%;
  aspect-ratio: 1;
  padding: 7px 7px 7px 10px;
  font-weight: 700;
}

.card .top-section .icons .logo .top-section {
  height: 100%;
}

.card .top-section .icons .social-media {
  height: 100%;
  padding: 8px 15px;
  display: flex;
  gap: 7px;
}

.card .top-section .icons .social-media .svg {
  height: 100%;
  fill: #ffffff;
}

.card .top-section .icons .social-media .svg:hover {
  fill: white;
}

.card .bottom-section {
  padding: 8px 5px;
  //display: flex;
  gap: 10px;
  font-size: 12px;
  .dot-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
    > div {
      width: 100px;
    }
    .dot {
      width: 5px;
      height: 5px;
      background:#000;
      border-radius: 50%;
      margin-right: 4px;
    }
  }

  .total-progress {
    padding-top: 10px;
  }
}
</style>
