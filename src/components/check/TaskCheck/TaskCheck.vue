<script setup lang="ts">
withDefaults(defineProps<{
  color?: string;
}>(), {
  color: '#bfbfbf'
});

const modelValue = defineModel<boolean>('modelValue', {
  default: false,
});
const emits = defineEmits<{
  change: [boolean],
}>();
</script>
<template>
  <label class="ios-checkbox" @click.stop>
    <input type="checkbox"
           v-model="modelValue"
           @change.stop="emits('change', modelValue)" />
    <div class="checkbox-wrapper">
      <div class="checkbox-bg"></div>
      <svg fill="none" viewBox="0 0 24 24" class="checkbox-icon">
        <path
          stroke-linejoin="round"
          stroke-linecap="round"
          stroke-width="3"
          stroke="currentColor"
          d="M4 12L10 18L20 6"
          class="check-path"
        ></path>
      </svg>
    </div>
  </label>
</template>
<style lang="less" scoped>
.ios-checkbox {
  --checkbox-size: 16px;
  --checkbox-color: v-bind(color);
  --checkbox-bg: #dbeafe;
  --checkbox-border: v-bind(color);
  position: relative;
  display: inline-block;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.checkbox-container {
  display: flex;
  gap: 20px;
  padding: 10px;
  background: var(--color-bg-5);
  //border-radius: 16px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -2px rgba(0, 0, 0, 0.05);
}

.ios-checkbox input {
  display: none;
}

.checkbox-wrapper {
  position: relative;
  width: var(--checkbox-size);
  height: var(--checkbox-size);
  border-radius: 4px;
  transition: transform 0.2s ease;
}

.checkbox-bg {
  position: absolute;
  inset: 0;
  border-radius: 4px;
  border: 2px solid var(--checkbox-border);
  opacity: 0.8;
  background: var(--utools-background);
  transition: all 0.2s ease;
}

.checkbox-icon {
  position: absolute;
  inset: 0;
  margin: auto;
  width: 80%;
  height: 80%;
  color: white;
  transform: scale(0);
  transition: all 0.2s ease;
}

.check-path {
  stroke-dasharray: 40;
  stroke-dashoffset: 40;
  transition: stroke-dashoffset 0.3s ease 0.1s;
}

/* Checked State */
.ios-checkbox input:checked + .checkbox-wrapper .checkbox-bg {
  background: var(--checkbox-color);
  border-color: var(--checkbox-color);
}

.ios-checkbox input:checked + .checkbox-wrapper .checkbox-icon {
  transform: scale(1);
}

.ios-checkbox input:checked + .checkbox-wrapper .check-path {
  stroke-dashoffset: 0;
}

/* Hover Effects */
.ios-checkbox:hover .checkbox-wrapper {
  transform: scale(1.05);
}

/* Active Animation */
.ios-checkbox:active .checkbox-wrapper {
  transform: scale(0.95);
}

/* Focus Styles */
.ios-checkbox input:focus + .checkbox-wrapper .checkbox-bg {
  box-shadow: 0 0 0 4px var(--checkbox-bg);
  opacity: 1;
}

/* Animation */
@keyframes bounce {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
</style>
