import type { EditorOptions } from '@tiptap/core'

export interface EditorTiptapProps {
  editorOptions?: Pick<Partial<EditorOptions>, 'editable'>;
  hideToolBar?: boolean;
  fontSize?: number;
  placeholder?: string;
  editorContentMaxHeight?: string;
}

/**
 * 编辑器实例
 */
export interface EditorTiptapInstance {
  /**
   * 设置内容
   * @param content 内容 html
   */
  setContent(content: string): void;
  /**
   * 获取内容
   */
  getContent(): string;

  /**
   * 禁用编辑
   */
  disableEditor(): void;

  /**
   * 启用编辑
   */
  enableEditor(): void;

  /**
   * 切换编辑状态
   */
  switchEditor(): void;

  /**
   * 导出 html
   */
  exportHtml(): string;
}
