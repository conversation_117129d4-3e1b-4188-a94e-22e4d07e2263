<script setup lang="ts">
import { SvgIcon } from '@/components/common';
import { ref } from 'vue'

const props = defineProps<{
  content: string;
  disabled: boolean;
  icon: string;
}>();

const popupVisible = ref(false);

function handlePopupVisible(status: boolean) {
  popupVisible.value = status;
}
const emits = defineEmits<{
  click: [];
}>();
function handleClick() {
  popupVisible.value = false;
  emits('click');
}
</script>

<template>
  <a-tooltip :content="content" mini
             :popup-visible="popupVisible">
    <div
      @mouseenter="handlePopupVisible(true)"
      @mouseleave="handlePopupVisible(false)"
      class="button"
      :class="{ 'is-disabled': disabled }"
      @click.stop="handleClick"
    >
      <svg-icon :name="icon"></svg-icon>
    </div>
  </a-tooltip>
</template>

<style scoped lang="less">

</style>
