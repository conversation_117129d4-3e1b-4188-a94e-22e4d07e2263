import { Table, TableView, updateColumns } from '@tiptap/extension-table'
import { Node as ProseMirrorNode } from '@tiptap/pm/model'
import { Plugin, PluginKey } from '@tiptap/pm/state'
import type { Editor } from '@tiptap/core'
import { render, h } from 'vue'
import TableToolbar from './TableToolbar.vue'

// 继承原始TableView实现自定义表格视图
class CustomTableView extends TableView {
  editor: Editor
  toolbar: HTMLElement | null = null

  // 事件处理函数，保存引用以便移除监听器
  private showToolbar = () => {
    if (!this.editor.isEditable) {
      return
    }
    if (this.toolbar) {
      this.toolbar.style.display = 'block'
    }
  }

  private hideToolbar = () => {
    if (this.toolbar) {
      this.toolbar.style.display = 'none'
    }
  }

  constructor(node: ProseMirrorNode, cellMinWidth: number, editor: Editor) {
    super(node, cellMinWidth)
    this.editor = editor

    // 创建工具栏并初始化事件
    this.createTableToolbar()
    this.setupEvents()
  }

  setupEvents() {
    console.log('setupEvents')
    this.hideToolbar()
    // 使用 mouseenter/mouseleave，更精确
    this.dom.addEventListener('mouseenter', this.showToolbar)
    this.dom.addEventListener('mouseleave', this.hideToolbar)
  }

  // 创建表格顶部工具栏
  createTableToolbar() {
    // 如果已经有工具栏，直接返回
    if (this.toolbar) {
      return
    }

    // 创建工具栏容器
    const toolbar = document.createElement('div')
    render(h(TableToolbar, { editor: this.editor }), toolbar)
    toolbar.className = 'table-toolbar'
    // 设置工具栏样式

    // 将工具栏添加到表格上方
    this.dom.insertBefore(toolbar, this.dom.lastChild)
    this.toolbar = toolbar
  }

  stopEvent(e: Event) {
    if (e.target) {
      const target = e.target as HTMLDivElement
      if (target.closest('.table-toolbar')) {
        e.stopPropagation()
        e.preventDefault()
      }
    }
    return true
  }

  // // 在更新时重新定位工具栏,但不重新创建
  // update(node: ProseMirrorNode): boolean {
  //   console.log('在更新时重新定位工具栏,但不重新创建1')
  //   if (node.type !== this.node.type) return false
  //
  //   console.log('在更新时重新定位工具栏,但不重新创建2')
  //
  //   this.node = node
  //   updateColumns(node, this.colgroup, this.table, this.cellMinWidth)
  //   return false
  // }

  // 重写ignoreMutation方法，确保能正确处理DOM变化
  ignoreMutation(mutation: any): boolean {
    console.log('mutation', mutation)
    // 允许工具栏相关的变化
    console.log('mutation.type', mutation.type, mutation.type === 'childList')
    if (mutation.type === 'childList') {
      // 检查目标元素是否为我们添加的工具栏
      if (mutation.target instanceof HTMLElement) {
        if (
          mutation.target.classList.contains('table-toolbar') ||
          mutation.target.closest('.table-toolbar')
        ) {
          return true
        }
      }
    }

    // 允许样式变化，如display属性
    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
      if (mutation.target instanceof HTMLElement) {
        if (
          mutation.target.classList.contains('table-toolbar') ||
          mutation.target.closest('.table-toolbar')
        ) {
          return true
        }
      }
    }
    return true;
    // return super.ignoreMutation(mutation)
  }

  // 清理事件和DOM元素
  destroy() {
    // 移除事件监听
    this.dom.removeEventListener('mouseenter', this.showToolbar)
    this.dom.removeEventListener('mouseleave', this.hideToolbar)

    // 移除工具栏
    if (this.toolbar && this.toolbar.parentNode) {
      this.toolbar.parentNode.removeChild(this.toolbar)
      this.toolbar = null
    }
  }
}

// 注册自定义表格扩展
export const CustomTable = Table.extend({
  addOptions() {
    return {
      ...this.parent?.(),
      resizable: true,
      HTMLAttributes: {},
      handleWidth: 5,
      cellMinWidth: 25,
      allowTableNodeSelection: false,
    }
  },

  // 添加NodeView
  addNodeView() {
    return ({ node, editor }) => {
      const tableOptions = this.options

      // 使用自定义TableView类
      return new CustomTableView(node, tableOptions.cellMinWidth, editor)
    }
  },

  addProseMirrorPlugins() {
    const { resizable, lastColumnResizable, cellMinWidth, handleWidth } = this.options
    const plugins = [...(this.parent?.() || [])]
    if (resizable) {
      plugins.push(
        new Plugin({
          key: new PluginKey('tableInteractions'),
          props: {
            decorations(state) {
              // 为表格添加交互装饰
              return null
            },
          },
        }),
      )
    }

    return plugins
  },
})
