<template>
  <div class="slash-commands-list" :style="style">
    <div class="slash-commands-header">
      <div>AI</div>
    </div>
    <button
      class="slash-commands-item"
      v-for="item in AICommands"
      :key="item.title"
      @click="selectItem(item)"
    >
      <div class="slash-commands-item-icon">{{ item.icon }}</div>
      <div class="slash-commands-item-title">{{ item.title }}</div>
    </button>

    <div class="slash-commands-header">
      <div>FORMAT</div>
    </div>
    <button
      class="slash-commands-item"
      v-for="item in formatCommands"
      :key="item.title"
      @click="selectItem(item)"
    >
      <div class="slash-commands-item-icon">{{ item.icon }}</div>
      <div class="slash-commands-item-title">{{ item.title }}</div>
    </button>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { CSSProperties } from 'vue'
import { Editor } from '@tiptap/vue-3'
import type { Range } from '@tiptap/vue-3'

/**
 * 命令项接口定义
 * 包含图标、标题和命令函数
 */
interface CommandItem {
  icon: string
  title: string
  command: (props: { editor: Editor; range: Range }) => void
}

/**
 * 组件属性接口定义
 */
interface SlashCommandsProps {
  editor?: Editor
  items?: Array<any>
  command?: (fn: (props: { editor: Editor; range: Range }) => void) => void
  clientRect?: DOMRect
}

// 组件属性定义
const props = defineProps<SlashCommandsProps>()

/**
 * 根据客户端矩形计算弹出式菜单位置样式
 */
const style = computed<CSSProperties>(() => {
  if (!props.clientRect) {
    return {
      display: 'none',
    }
  }

  return {
    position: 'absolute',
    top: `${props.clientRect.top + window.scrollY}px`,
    left: `${props.clientRect.left + window.scrollX}px`,
  }
})

/**
 * AI相关命令列表
 * 包含AI写作和AI图像生成功能
 */
const AICommands: CommandItem[] = [
  {
    icon: '⭐',
    title: 'AI Writer',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).run()
    },
  },
  {
    icon: '⭐',
    title: 'AI Image',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).run()
    },
  },
]

/**
 * 格式化相关命令列表
 * 包含标题、列表、引用等各种文本格式化选项
 */
const formatCommands: CommandItem[] = [
  {
    icon: 'H₁',
    title: 'Heading 1',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('heading', { level: 1 }).run()
    },
  },
  {
    icon: 'H₂',
    title: 'Heading 2',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('heading', { level: 2 }).run()
    },
  },
  {
    icon: 'H₃',
    title: 'Heading 3',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('heading', { level: 3 }).run()
    },
  },
  {
    icon: '•',
    title: 'Bullet List',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleBulletList().run()
    },
  },
  {
    icon: '1.',
    title: 'Numbered List',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleOrderedList().run()
    },
  },
  {
    icon: '☐',
    title: 'Task List',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleTaskList().run()
    },
  },
  {
    icon: '▼',
    title: 'Toggle List',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).run()
    },
  },
  {
    icon: '❝',
    title: 'Blockquote',
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleBlockquote().run()
    },
  },
]

/**
 * 选择并执行命令项
 * @param item - 命令项
 */
function selectItem(item: CommandItem): void {
  if (props.command) {
    props.command(item.command)
  }
}
</script>

<style lang="less">
.slash-commands-list {
  background: white;
  border-radius: 0.5rem;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 0.05),
    0px 10px 20px rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.8);
  font-size: 0.9rem;
  overflow: hidden;
  padding: 0.2rem;
  position: absolute;
  z-index: 20;
  width: 240px;
  max-height: 400px;
  overflow-y: auto;
}

.slash-commands-header {
  color: #888;
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.4rem 0.6rem;
  margin-top: 0.4rem;
}

.slash-commands-item {
  background: transparent;
  border: 1px solid transparent;
  border-radius: 0.3rem;
  display: flex;
  align-items: center;
  font-weight: 500;
  width: 100%;
  padding: 0.4rem 0.6rem;
  text-align: left;

  &:hover {
    background-color: #f5f5f5;
  }

  &.is-selected {
    background-color: #eee;
  }
}

.slash-commands-item-icon {
  color: #888;
  margin-right: 0.5rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slash-commands-item-title {
  color: #333;
}
</style>
