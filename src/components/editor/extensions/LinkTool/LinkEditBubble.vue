<script setup lang="ts">

import { SvgIcon } from '@/components/common'
</script>

<template>
  <div class="link-tooltip">
    <div class="link-tooltip-actions">
      <button class="link-open-btn">
        <div>
          <svg-icon name="editor-share"></svg-icon>
        </div>
        <div>打开</div>
      </button>
      <a-divider style="margin: 0" direction="vertical"></a-divider>
      <button class="link-edit-btn">
        <svg-icon name="editor-edit"></svg-icon>
      </button>
      <button class="link-remove-btn">
        <svg-icon name="editor-delete"></svg-icon>
      </button>
    </div>
  </div>
</template>

<style scoped lang="less">
/* 链接悬浮提示框样式 */
.link-tooltip {
  padding: 2px;
  font-size: 12px;
  background: var(--utools-background);
  border-radius: var(--border-radius-small);
  .link-tooltip-actions {
    display: flex;
    align-items: center;
    gap: 6px;
    button {
      padding: 6px;
      border-radius: 4px;
      border: none;
      color: var(--color-text-1);
      cursor: pointer;
      font-size: 14px;
      background: transparent;
      display: flex;
      align-items: center;
      gap: 4px;
      &:hover {
        background: var(--color-fill-2);
      }
    }
  }
}
</style>
