<script setup lang="ts">
import {ref, computed, onMounted, onBeforeUnmount, h, render, useTemplateRef} from 'vue'
import type { Editor } from '@tiptap/core'
import tippy, { type Instance, type Props, type SingleTarget } from 'tippy.js'
import { LinkEditBubble } from './index.ts';
import type {InputInstance} from "@arco-design/web-vue";
import {noop} from "es-toolkit";
import type {Fn} from "@vueuse/core";
import {useEventListener} from "@vueuse/core";

const props = defineProps<{
  editor: Editor
}>()

// 链接编辑状态
const linkUrl = ref('')
const isEditing = ref(false)
const isActive = computed(() => props.editor.isActive('link'))
const linkMenuRef = ref<HTMLElement | null>(null)
const urlInputRef = useTemplateRef<InputInstance>('urlInputRef');
const tippyInstance = ref<Instance | null>(null)

// 获取当前选中文本的坐标
function getSelectionCoords() {
  const { view } = props.editor
  const { from, to } = view.state.selection

  if (from === to) {
    // 如果是普通的光标位置（没有选中文本）
    const start = view.coordsAtPos(from)
    return {
      left: start.left,
      top: start.bottom,
      right: start.right,
      bottom: start.bottom,
      width: 0,
      height: 0,
      x: start.left,
      y: start.bottom,
      toJSON: () => ({}),
    }
  }

  // 如果有选中文本，返回选区的坐标
  const start = view.coordsAtPos(from)
  const end = view.coordsAtPos(to)
  return {
    left: start.left,
    top: start.top,
    right: end.right,
    bottom: end.bottom,
    width: end.right - start.left,
    height: end.bottom - start.top,
    x: start.left,
    y: start.top,
    toJSON: () => ({}),
  }
}

const isDisabled = computed(() => {
  return props.editor.state.selection.empty;
})
// 创建链接
function createLink() {
  if (!props.editor.isActive('link')) {
    // 创建新链接
    openLinkEditor()
  } else {
    // 已有链接，打开编辑框
    const attrs = props.editor.getAttributes('link')
    linkUrl.value = attrs.href || ''
    openLinkEditor()
  }
}

// 打开链接编辑器
function openLinkEditor() {
  isEditing.value = true
  // 获取当前选区的位置信息
  const selectionCoords = getSelectionCoords()
  console.log('selectionCoords', selectionCoords)
  // 延迟执行，确保DOM已经更新
  setTimeout(() => {
    // 创建一个固定位置的tippy实例
    if (tippyInstance.value) {
      tippyInstance.value.destroy()
    }

    if (linkMenuRef.value) {
      // 使用tippy.js创建浮动编辑框
      tippyInstance.value = tippy(document.body, {
        getReferenceClientRect: () => selectionCoords,
        appendTo: () => document.body,
        content: linkMenuRef.value,
        showOnCreate: true,
        interactive: true,
        trigger: 'manual',
        placement: 'bottom-start',
        animation: 'scale',
        onShow() {
          // 当浮动框显示时，聚焦输入框
          setTimeout(() => {
            urlInputRef.value?.focus()
          }, 20)
        },
        onHide() {
          // 隐藏时关闭编辑状态
          isEditing.value = false
        },
      })

      // 自动聚焦输入框
      setTimeout(() => {
        urlInputRef.value?.focus()
      }, 20)
    }
  }, 0)
}

// 应用链接
function applyLink() {
  if (linkUrl.value) {
    // 设置链接
    props.editor
      .chain()
      .focus()
      .extendMarkRange('link')
      .setLink({ href: linkUrl.value, target: '_blank' })
      .run()
  } else {
    // 如果链接为空，则删除链接
    removeLink()
  }

  // 关闭编辑框
  closeLinkEditor()
}

// 移除链接
function removeLink() {
  props.editor.chain().focus().extendMarkRange('link').unsetLink().run()

  closeLinkEditor()
}

// 关闭链接编辑器
function closeLinkEditor() {
  if (tippyInstance.value) {
    tippyInstance.value.hide()
  }
  isEditing.value = false
  linkUrl.value = ''
}

// 处理键盘事件
function handleKeyDown(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    event.preventDefault()
    applyLink()
  } else if (event.key === 'Escape') {
    event.preventDefault()
    closeLinkEditor()
  }
}

// 创建链接悬浮编辑工具栏
let linkTooltipInstance: Instance | null = null
const showLinkTooltip = (event: MouseEvent, element: HTMLElement) => {
  if (!props.editor.isEditable) {
    return;
  }
  try {
    // 如果已经存在实例，先销毁
    if (linkTooltipInstance) {
      return;
      // linkTooltipInstance.destroy()
      // linkTooltipInstance = null
    }

    // 提取链接URL，便于显示
    const href = element.getAttribute('href') || ''
    console.log('显示链接悬浮提示:', href)
    const div = document.createElement('div');
    div.classList.add('u-link')
    render(h(LinkEditBubble, { editor: props.editor }), div)
    linkTooltipInstance = tippy(element as Element, {
      content: div,
      allowHTML: true,
      interactive: true,
      appendTo: document.body, // 确保添加到body以避免定位问题
      trigger: 'manual',
      placement: 'top',
      animation: 'scale',
      theme: 'light',
      zIndex: 9999,
      onShow(instance) {
        // 添加事件监听
        setTimeout(() => {
          const tooltip = instance.popper.querySelector('.link-tooltip')
          if (tooltip) {
            const editBtn = tooltip.querySelector('.link-edit-btn')
            const removeBtn = tooltip.querySelector('.link-remove-btn')
            const openBtn = tooltip.querySelector('.link-open-btn')

            // 编辑链接
            editBtn?.addEventListener('click', () => {
              const posAtCoords = props.editor.view.posAtCoords({
                left: event.clientX,
                top: event.clientY,
              });
              posAtCoords && props.editor.commands.setTextSelection(posAtCoords.pos);
              const attrs = props.editor.getAttributes('link')
              linkUrl.value = attrs.href || ''
              openLinkEditor()
              instance.hide()
            })

            // 删除链接
            removeBtn?.addEventListener('click', () => {
              props.editor.chain().focus().extendMarkRange('link').unsetLink().run()
              instance.hide()
            })

            // 打开链接
            openBtn?.addEventListener('click', () => {
              console.log('href',href)
              if (href) {
                utools.shellOpenExternal(href)
              }
              instance.hide()
            })
          }
        }, 0)
      },
      onHide() {
        setTimeout(() => {
          linkTooltipInstance?.destroy();
          linkTooltipInstance = null;
        })
      }
    })

    // 显示提示框
    if (linkTooltipInstance) {
      linkTooltipInstance.show()
    }
  } catch (error) {
    console.error('显示链接悬浮提示出错:', error)
  }
}

// 定义处理鼠标进入链接的函数
function handleLinkMouseEnter(event: MouseEvent) {
  const linkElement = event.target as HTMLElement
  if (linkElement?.tagName === 'A') {
    console.log('鼠标进入链接元素:', linkElement)
    showLinkTooltip(event, linkElement)
  }
}

// 定义处理鼠标离开链接的函数
function handleLinkMouseLeave(event: MouseEvent) {
  // 如果鼠标不是移动到 tippy 提示框，则隐藏提示框
  const relatedTarget = event.relatedTarget as HTMLElement
  if (!relatedTarget || !relatedTarget.closest('.tippy-box')) {
    setTimeout(() => {
      const tippyHovered = document.querySelector('.tippy-box:hover');
      if (!tippyHovered) {
        linkTooltipInstance?.hide();
      }
    }, 100)
  }
}

function handleLinkClick(event: MouseEvent) {
/*  if (props.editor.isEditable) {
    return;
  }*/
  const linkElement = event.target as HTMLElement
  if (linkElement?.tagName === 'A') {
    event.preventDefault()
    utools.shellOpenExternal(linkElement.getAttribute('href') as string)
  }
}
// 监听编辑器中的链接元素

let showLinkEvent: Fn = noop;
let clickLinkEvent: Fn = noop;
const initLinkTooltips = () => {
  if (!props.editor) return



  // 为编辑器添加事件监听
  const editorElement = props.editor.view.dom

  // 使用事件委托处理函数
  const handleEditorMouseOver = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    if (target && target.tagName === 'A' && target.closest('.u-tiptap-editor')) {
      handleLinkMouseEnter(event)
    }
  }

  const handleEditorMouseOut = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    console.log('handleEditorMouseOut', target)
    if (target && target.closest('.u-tiptap-editor')) {
      handleLinkMouseLeave(event)
    }
  }

  const handleEditorClick = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    if (target && target.tagName === 'A') {
      handleLinkClick(event)
    }
  }

  // 添加事件监听
  showLinkEvent = useEventListener('mouseover', handleEditorMouseOver)
  clickLinkEvent = useEventListener('click', handleEditorClick)
  // editorElement.addEventListener('mouseout', handleEditorMouseOut)
}

onBeforeUnmount(() => {
  showLinkEvent();
  clickLinkEvent();
  linkTooltipInstance?.destroy()
  linkTooltipInstance = null;
})
// 监听编辑器实例加载完成
onMounted(() => {
  if (props.editor) {
    console.log('编辑器加载完成，初始化链接悬浮效果')
    setTimeout(() => {
      initLinkTooltips()
    }, 500) // 给编辑器完全初始化一些时间
  }
});

</script>

<template>
  <div class="link-button-container">
    <!-- 链接按钮 -->
    <div class="button link-button"
         :class="{
          'is-active': isActive,
          'is-disabled': isDisabled
         }"
         @click="createLink">
      <iconpark-icon name="link-three"></iconpark-icon>
    </div>
    <!-- 链接编辑菜单 -->
    <div ref="linkMenuRef"
         class="link-menu"
         v-show="isEditing">
      <div class="link-menu-inner">
        <a-input ref="urlInputRef"
                 size="mini"
                 style="width: 200px"
                 v-model:model-value="linkUrl"
                 placeholder="回车创建"
                 @pressEnter="handleKeyDown" />
        <div class="link-menu-actions">
          <button class="link-menu-btn apply" @click="applyLink">
            <iconpark-icon name="check"></iconpark-icon>
          </button>
          <button class="link-menu-btn remove"
                  v-if="isActive"
                  @click="removeLink">
            <iconpark-icon name="delete"></iconpark-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
</style>

<style scoped lang="less">
.link-button-container {
  position: relative;
  display: inline-block;
}

/* 编辑菜单样式 */
.link-menu {
  position: absolute;
  z-index: 100;
  left: 0;
  top: 100%;
  padding: 2px;
  font-size: 12px;
  .link-menu-inner {
    display: flex;
    gap: 4px;
    background: var(--color-bg-2);
    padding: 6px;
    border-radius: 6px;
    .link-menu-actions {
      display: flex;
      gap: 2px;
      .link-menu-btn {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        background: none;
        border-radius: 4px;
        cursor: pointer;
        color: var(--color-text-1);
        &:hover {
          background: var(--color-fill-2);
        }
      }
    }
  }
}
</style>
