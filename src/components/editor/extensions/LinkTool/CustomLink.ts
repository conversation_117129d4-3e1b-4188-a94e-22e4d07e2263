import { Link } from '@tiptap/extension-link'
import type { Editor } from '@tiptap/core'

// 自定义链接扩展，添加高亮背景和自定义样式
export const CustomLink = Link.extend({
  addOptions() {
    return {
      ...this.parent?.(),
      openOnClick: false,
      protocols: ['utools', 'obsidian'],
      defaultProtocol: 'https',
      HTMLAttributes: {
        class: 'custom-link',
        rel: 'noopener noreferrer nofollow',
        target: '_blank',
      },
    }
  },
})
