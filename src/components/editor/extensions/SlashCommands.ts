import { Editor, Extension } from '@tiptap/core'
import type { Range } from '@tiptap/core'
import Suggestion from '@tiptap/suggestion'
import type { SuggestionOptions, SuggestionProps } from '@tiptap/suggestion'

/**
 * SlashCommands 扩展
 * 基于 Tiptap 的命令菜单扩展，允许用户输入 '/' 触发命令菜单
 * 参考文档: https://tiptap.dev/docs
 */
export const SlashCommands = Extension.create({
  name: 'slashCommands',
  addOptions() {
    return {
      suggestion: {
        char: '/',
        /**
         * 当命令被选择时执行的回调函数
         * @param editor - Tiptap 编辑器实例
         * @param range - 文本范围，表示命令应该替换的文本位置
         * @param props - 包含命令数据和实用方法的对象
         */
        command: ({
          editor,
          range,
          props,
        }: {
          editor: Editor
          range: Range
          props: SuggestionProps
        }) => {
          props.command({ editor, range })
        },
      } as Partial<SuggestionOptions>,
    }
  },

  /**
   * 添加 ProseMirror 插件以启用斜杠命令功能
   * 这将注册 '/' 字符作为触发器
   */
  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        ...this.options.suggestion,
      }),
    ]
  },
})
