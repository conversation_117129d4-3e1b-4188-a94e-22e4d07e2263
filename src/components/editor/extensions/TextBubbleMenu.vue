<script setup lang="ts">
// import {BubbleMenu} from "@tiptap/vue-3";
import {Editor} from "@tiptap/core";
import {EditorState, TextSelection} from "prosemirror-state";

defineProps<{
  editor: Editor
}>();

function textBubbleMenuShouldShow({ editor, state }: { editor: Editor; state: EditorState }) {
  if (!editor.isEditable) {
    // 编辑器不可编辑时，不显示气泡菜单
    return false;
  }
  // 检查是否是文本选择
  if (state.selection instanceof TextSelection) {
    // 检查选择范围是否有效（不是空选择）
    const { from, to } = state.selection
    // 只有当选择范围不是同一个位置时才返回 true
    return from !== to
  }
  return false
}
</script>

<template>
  <bubble-menu
    class="bubble-menu"
    :tippy-options="{ duration: 300 }"
    :shouldShow="textBubbleMenuShouldShow"
    :editor="editor"
  >
    <div
      class="button"
      :class="{ 'is-active': editor.isActive('bold') }"
      @click="editor.chain().focus().toggleBold().run()"
    >
      <iconpark-icon name="text-bold"></iconpark-icon>
    </div>

    <div
      class="button"
      @click="editor.chain().focus().toggleHighlight().run()"
      :class="{ 'is-active': editor.isActive('highlight') }"
    >
      <iconpark-icon name="high-light"></iconpark-icon>
    </div>
  </bubble-menu>
</template>

<style scoped lang="less">
/* Bubble menu */
body[arco-theme="dark"] {
  .bubble-menu {
    background-color: #626263;
  }
}
.bubble-menu {
  padding: 5px;
  background-color: var(--color-neutral-2);
  display: flex;
  gap: 5px;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
  color: var(--color-neutral-8);
  .button {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-small);
    padding: 4px;
    transition: all 250ms linear;
    cursor: pointer;
    &:hover {
      background-color: var(--color-neutral-3);
    }
    &.is-active {
      background-color: var(--color-neutral-3);
    }
  }
}
</style>
