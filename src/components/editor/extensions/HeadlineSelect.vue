<script setup lang="ts">
import type { Editor } from '@tiptap/core'
import { ref, watch } from 'vue'

const props = defineProps<{
  editor: Editor
}>()

const modelValue = ref('paragraph')

props.editor.on('selectionUpdate', () => {
  const { $from } = props.editor.state.selection
  const node = $from.parent
  if (node?.type.name === 'heading') {
    modelValue.value = node.attrs.level.toString()
  } else {
    modelValue.value = 'paragraph'
  }
});
// 处理标题选择
const handleSelect = (value: any) => {
  if (value === 'paragraph') {
    props.editor.chain().focus().setParagraph().run()
  } else {
    props.editor
      .chain()
      .focus()
      .toggleHeading({ level: Number(value) as 1 | 2 | 3 | 4 | 5 | 6 })
      .run()
  }
}
</script>

<template>
  <a-select
    :model-value="modelValue"
    size="mini"
    style="width: 95px; border-radius: 4px"
    :trigger-props="{ contentClass: 'u-select-options min' }"
    @update:model-value="handleSelect"
  >
    <a-option value="paragraph">正文</a-option>
    <a-option value="1">
      <span style="font-size: 22px">标题1</span>
    </a-option>
    <a-option value="2">
      <span style="font-size: 20px">标题2</span>
    </a-option>
    <a-option value="3">
      <span style="font-size: 18px">标题3</span>
    </a-option>
    <a-option value="4">
      <span style="font-size: 16px">标题4</span>
    </a-option>
    <a-option value="5">
      <span style="font-size: 14px">标题5</span>
    </a-option>
    <a-option value="6">
      <span style="font-size: 12px">标题6</span>
    </a-option>
  </a-select>
</template>

<style scoped lang="less"></style>
