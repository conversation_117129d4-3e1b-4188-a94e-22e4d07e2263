<script setup lang="ts">
import type { Editor } from '@tiptap/core'
import { SvgIcon } from '@/components/common'
import { ref, useTemplateRef } from 'vue'
import TableToolButton from '@/components/editor/extensions/TableToolButton.vue'
const props = defineProps<{
  editor: Editor
}>();
</script>

<template>
  <div ref="toolbar" class="toolbar" @click.stop>
    <TableToolButton content="向上加一行"
                     :disabled="!editor.can().addRowBefore()"
                     icon="editor-upLine"
                     @click="editor.chain().focus().addRowBefore().run()">
    </TableToolButton>
    <TableToolButton content="向下加一行"
                     :disabled="!editor.can().addRowAfter()"
                     icon="editor-bottomLine"
                     @click="editor.chain().focus().addRowAfter().run()">
    </TableToolButton>
    <TableToolButton content="删除行"
                     :disabled="!editor.can().deleteRow()"
                     icon="editor-deleteLine"
                     @click="editor.chain().focus().deleteRow().run()">
    </TableToolButton>
    <a-divider direction="vertical" style="margin: 0 4px"></a-divider>
    <TableToolButton content="向左加一列"
                     :disabled="!editor.can().addColumnBefore()"
                     icon="editor-leftLine"
                     @click="editor.chain().focus().addColumnBefore().run()">
    </TableToolButton>
    <TableToolButton content="向右加一列"
                     :disabled="!editor.can().addColumnAfter()"
                     icon="editor-rightLine"
                     @click="editor.chain().focus().addColumnAfter().run()">
    </TableToolButton>
    <TableToolButton content="删除列"
                     :disabled="!editor.can().deleteColumn()"
                     icon="editor-deleteCol"
                     @click="editor.chain().focus().deleteColumn().run()">
    </TableToolButton>
    <a-divider direction="vertical" style="margin: 0 4px"></a-divider>
    <TableToolButton content="合并单元格"
                     :disabled="!editor.can().mergeCells()"
                     icon="editor-mergeCell"
                     @click="editor.chain().focus().mergeCells().run()">
    </TableToolButton>
    <TableToolButton content="分割单元格"
                     :disabled="!editor.can().splitCell()"
                     icon="editor-splitCell"
                     @click="editor.chain().focus().splitCell().run()">
    </TableToolButton>
    <a-divider direction="vertical" style="margin: 0 4px"></a-divider>
    <TableToolButton content="切换标题行"
                     :disabled="false"
                     icon="editor-heading"
                     @click="editor.chain().focus().toggleHeaderRow().run()">
    </TableToolButton>
    <TableToolButton content="切换标题单元格"
                     :disabled="false"
                     icon="editor-headingSquare"
                     @click="editor.chain().focus().toggleHeaderCell().run()">
    </TableToolButton>
    <a-divider direction="vertical" style="margin: 0 4px"></a-divider>
    <TableToolButton content="修复表格"
                     :disabled="false"
                     icon="editor-flx"
                     @click="editor.chain().focus().fixTables().run()">
    </TableToolButton>
    <TableToolButton content="删除表格"
                     :disabled="false"
                     icon="editor-delete"
                     @click="editor.chain().focus().deleteTable().run()">
    </TableToolButton>
  </div>
</template>

<style scoped lang="less">
.toolbar {
  padding: 6px 12px 2px;
  display: flex;
  gap: 12px;
  background: var(--color-neutral-2);
  height: 25px;
  z-index: 9999;
  cursor: pointer;
}
.button {
  cursor: pointer;
  width: 16px;
  &.is-disabled {
    cursor: not-allowed !important;
    svg {
      fill: red;
      stroke: red !important;
    }
  }
}
</style>
