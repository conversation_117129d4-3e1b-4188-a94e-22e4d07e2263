/**
 * 获取 utools 用户的 openId
 */
async function getUserOpenId() {
  const userToken = await utools.fetchUserServerTemporaryToken();
  return await fetch(`${import.meta.env.VITE_API_BASE_URL}/app/utools/getUserId?accessToken=${userToken.token}`, {
   headers: {
     'x-app-id': import.meta.env.VITE_APP_ID,
   }
  })
    .then(res => res.json())
    .then(res => {
      if (res.code !== 0) {
        throw new Error(res.msg);
      }
      return res.data;
    });
}
export default {
  getUserOpenId
}
