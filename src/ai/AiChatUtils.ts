import type { ChatMessageParam } from './AiChat.ts';
import { type AiService, AiServiceType } from '@/ai/AiService.ts';

export interface AskToOpenAiAbort {
  abort: (reason?: string) => void;
}

interface Assistant {
  // 使用的默认模型
  model: string;
  // 模型温度，0-2，一位小数
  temperature?: number;
  // Top-P，0-1，二位小数
  topP?: number;
  // 上下文数
  maxChats?: number;
}

interface AskToOpenAiProps {
  messages: ChatMessageParam[];
  service: AiService;
  assistant: Assistant;
  // 流式处理回调开始
  onStart?: () => void;
  // 流式结束回调开始
  onEnd?: () => void;
  // 流式处理回调
  onAppend: (data: string, t?: boolean) => void;
  // 流式处理回调结束
  onAborted: (a: AskToOpenAiAbort) => void;
}


async function askToUTools(props: AskToOpenAiProps): Promise<void> {
  const {messages, service, assistant, onStart, onAppend, onAborted} = props;
  if (!service.models.map(e => typeof e === 'string' ? e : e.id).find(e => e === assistant.model)) {
    return Promise.reject(new Error("AI助手选择的模型已不支持"));
  }

  onStart?.();
  // 适配新版 uTools AI 接口
  const abortPromise = utools.ai({model: assistant.model, messages}, (delta) => {
    console.log('reasoning_content', delta)
    const msg = delta.reasoning_content || delta.content;
    if (msg) {
      console.log('delta.reasoning_content', delta.reasoning_content)
      onAppend(msg, !!delta.reasoning_content);
    }
  })
  onAborted({
    abort() {
      abortPromise.abort();
    },
  });
  await abortPromise;
}


export async function askToAi(props: AskToOpenAiProps) {
  const {service} = props;
  if (service.type === AiServiceType.OPENAI) {
  } else if (service.type === AiServiceType.UTOOLS) {
    await askToUTools(props);
  } else if (service.type === AiServiceType.OLLAMA) {
  } else {
    return Promise.reject(new Error("AI类型未知"));
  }
}

const DEFAULT_UTOOLS_AI_SERVICE: AiService = {
  id: '1',
  createBy: 0,
  updateBy: 0,
  name: 'uTools 内置模型',
  type: AiServiceType.UTOOLS,
  url: '',
  key: '',
  modelVersion: '',
  models: [],
}

const DEFAULT_UTOOLS_CUSTOM_AI_SERVICE: AiService = {
  id: '2',
  createBy: 0,
  updateBy: 0,
  name: 'uTools 自定义模型',
  type: AiServiceType.UTOOLS,
  url: '',
  key: '',
  modelVersion: '',
  models: [],
}

export async function getAiServiceList(): Promise<AiService[]> {
  const result: AiService[] = [];
  const customModels = (await utools.allAiModels())
    .filter(item => !item.cost);

  if (customModels.length) {
    result.push({
      ...DEFAULT_UTOOLS_CUSTOM_AI_SERVICE,
      updateBy: Date.now(),
      models: customModels
    })
  }

  const models = (await utools.allAiModels())
    .filter(item => item.label !== '文心一言 Speed')
    .filter(item => item.cost);
  if (models.length) {
    result.push({
      ...DEFAULT_UTOOLS_AI_SERVICE,
      updateBy: Date.now(),
      models
    })
  }

  return result;
}
