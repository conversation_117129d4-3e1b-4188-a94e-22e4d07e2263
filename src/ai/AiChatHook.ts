import { onMounted, ref } from 'vue';
import { type AiService, isAiServiceModel } from './AiService.ts';
import type { ChatMessageParam } from './AiChat.ts';
import { askToAi, getAiServiceList } from './AiChatUtils.ts';
import type { AskToOpenAiAbort } from './AiChatUtils.ts';
import { AI_SERVICE_MODEL_SEPARATOR } from './AiConstant.ts';
import { Message } from '@arco-design/web-vue';



export interface AiChatEvent {
  /**
   * 流式开始回调
   */
  onStart?: () => void;
  /**
   * 流式结束回调
   */
  onEnd?: () => void;
  /**
   * 流式处理回调
   * @param data 流式数据
   * @param reasoningContent 是否推测
   */
  onAppend: (data: string,  reasoningContent?: boolean) => void;
  /**
   * 流式取消回调
   * @param a
   */
  onAborted?: () => void;
}
/**
 * ai 聊天服务 hook
 * @param appendMode 追加模式 append 追加 all 全部返回
 * @param event AI 回调事件
 */
export function useAiChat(appendMode: 'append' | 'all' |'content', event: AiChatEvent) {
  // 所有 AI 服务
  const aiServiceList = ref<AiService[]>([]);
  // 当前 AI 模型选择
  const aiCurrentAiModel = ref<string>('');
  // AI 生成状态
  const aiAskStatus = ref<boolean>(false);

  let aiAskAbortObj: AskToOpenAiAbort | null = null;

  onMounted(async () => {
    aiServiceList.value = await getAiServiceList();
  });

  function aiAskChat(messages: ChatMessageParam[]) {
    debugger
    if (!aiCurrentAiModel.value
      || aiCurrentAiModel.value.split(AI_SERVICE_MODEL_SEPARATOR).length !== 2) {
      Message.warning('请先选择模型')
      return;
    }
    const [aiServiceId, modelId] = aiCurrentAiModel.value.split(AI_SERVICE_MODEL_SEPARATOR);
    const aiService = aiServiceList.value.find((item) => item.id === aiServiceId);
    if (!aiService) {
      Message.warning('ai 服务不存在');
      return;
    }

    const model = aiService.models.find((item) =>
      isAiServiceModel(item) ? item.id === modelId : item === modelId,
    );

    if (!model) {
      Message.warning('模型不存在');
      return;
    }
    aiAskStatus.value = true;
    let context = '';
    let reasoningContent = '';
    askToAi({
      messages,
      service: aiService,
      assistant: {
        model: isAiServiceModel(model) ? model.id : model,
      },
      onStart() {
        event.onStart?.();
      },
      onAppend(data, t) {
        if (appendMode === 'all') {
          if (t) {
            reasoningContent += data;
          } else {
            context += data;
          }
          event.onAppend(t ? reasoningContent : context, t);
        } else {
          event.onAppend(data, t);
        }
      },
      onAborted(a) {
        aiAskAbortObj = a;
      },
      onEnd() {
        aiAskStatus.value = false;
        event.onEnd?.();
      },
    }).then(() => {
      aiAskStatus.value = false;
      event.onEnd?.();
    })
  }

  /**
   * 取消 ai 生成
   * @param reason 理由
   */
  function aiAskAbort(reason?: string) {
    if (aiAskAbortObj) {
      aiAskStatus.value = false;
      event.onEnd?.();
      aiAskAbortObj?.abort(reason);
      aiAskAbortObj = null;
    }
  }

  return {
    aiServiceList,
    aiCurrentAiModel,
    aiAskChat,
    aiAskStatus,
    aiAskAbort,
  }
}
