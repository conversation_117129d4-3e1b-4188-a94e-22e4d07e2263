export enum TaskStatus2 {
  /**
   * 等待中
   */
  WAIT ="WAIT",
  /**
   * 进行中
   */
  IN_PROGRESS="IN_PROGRESS",
  /**
   * 暂停
   */
  PAUSE="PAUSE",
  /**
   * 完成
   */
  FINISH="FINISH",
}

/**
 * 提供给 AI 使用
 */
export const TASK_ITEM_DESC: Readonly<Record<string, string>> = {
  taskTitle: '任务标题',
  content: '任务内容',
  taskDateType: '任务日期类型 date: 单日 dateSegment: 时间段',
  taskStartDate: '任务开始日期格式是YYYY-MM-DD',
  taskStartTime: '任务开始时间格式是HH:mm',
  taskEndDate: '任务结束日期格式是YYYY-MM-DD',
  taskEndTime: '任务结束时间格式是HH:mm',
  taskLevel: '任务等级: 0: 重要且紧急 1: 重要不紧急 2: 不重要但紧急 3: 不重要不紧急',
  finish: '是否完成',
  finishTime: '完成时间',
  tagNameList: '标签名称',
  createdAt: '任务创建时间'
}

/**
 * 任务项
 */
export interface ITaskItem {
  /**
   * 任务 id
   */
  taskId: string;
  /**
   * 任务标题
   */
  taskTitle: string;

  /**
   * 任务日期类型 <br/>
   * date: 单日
   * dateSegment: 时间段
   */
  taskDateType: 'date' | 'dateSegment';

  /**
   * 任务日期
   */
  taskStartDate?: string;

  /**
   * 任务时间
   */
  taskStartTime?: string;

  /**
   * 任务日期
   */
  taskEndDate?: string;

  /**
   * 任务时间
   */
  taskEndTime?: string;

  /**
   * 任务提醒时间
   */
  taskRemindTime?: string;

  /**
   * 任务等级 <br/>
   * 0: 重要且紧急
   * 1: 重要不紧急
   * 2: 不重要但紧急
   * 3: 不重要不紧急
   */
  taskLevel: string;

  /**
   * 任务状态
   */
  taskStatus: number;

  /**
   * 是否完结
   */
  finish?: boolean;

  /**
   * 完结时间
   */
  finishTime?: string;

  /**
   * 任务分组名称
   */
  taskGroupId: string;

  /**
   * 任务类型
   */
  taskType: string;

  /**
   * 标签 id
   */
  tagNameList?: string[];

  /**
   * 排序字段
   */
  sort: number;

  /**
   * 任务来源
   */
  taskSource: string;

  /**
   * 任务自定义分组名称
   */
  taskCustomGroupName?: string;

  /**
   * 是否删除
   */
  deleted: boolean;

  /**
   * 删除时间
   */
  deleteTime: string;

  /**
   * 子任务
   */
  children: string[];

  /**
   * 创建时间
   */
  createdAt: number;

  /**
   * 更新时间
   */
  updateAt: number;

  /**
   * 其他字段
   */
  [key: string]: any;
}

/**
 * 任务状态
 */
export interface ITaskStatus {
  id: number;
  label: string;
  color: string;
  finish?: boolean;
  start?: boolean;
  createAt: number;
  updateAt: number;
}
