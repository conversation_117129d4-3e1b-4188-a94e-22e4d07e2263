export type ViewGroupType =  'taskLevel' | 'taskGroupId' | 'taskStartDate' | 'finishTime' | 'deleteTime'

export interface IViewConfig {
  groupType: ViewGroupType;
  sortType: string;
  /**
   * 是否显示完成
   */
  showFinish?: boolean;
}


/**
 * 分组信息项
 */
export interface IGroupInfo {

  /**
   * 分组 id
   */
  groupId: string;

  /**
   * 分组名称
   */
  groupName: string;

  /**
   * 封面
   */
  cover?: string;
  /**
   * 排序
   */
  sort: number;

  /**
   * 全局指令
   */
  utoolsKey?: boolean;

  /**
   * 创建时间
   */
  createAt: number;

  /**
   * 更新时间
   */
  updateAt: number;

  /**
   * 总数
   */
  total: number;
  /**
   * 完结数
   */
  finish: number;

  /**
   * 任务状态类型 id
   */
  taskStatusTypeId?: string;

  /**
   * 视图配置
   */
  viewConfig?: IViewConfig;
}
