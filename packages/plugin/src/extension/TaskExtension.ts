import { BaseExtension, PageParams, PageResult } from './BaseExtension'
import { ITaskItem } from '../entity'

export interface TaskSaveParams {
  /**
   * 任务 id
   */
  taskId?: string;

  /**
   * 父级
   */
  parentId?: string;

  /**
   * 任务标题
   */
  taskTitle?: string;

  /**
   * 任务日期类型 <br/>
   * date: 单日
   * dateSegment: 时间段
   */
  taskDateType?: 'date' | 'dateSegment';

  /**
   * 任务日期
   */
  taskStartDate?: string;

  /**
   * 任务时间
   */
  taskStartTime?: string;

  /**
   * 任务日期
   */
  taskEndDate?: string;

  /**
   * 任务时间
   */
  taskEndTime?: string;

  /**
   * 日期段开始时间
   */
  dateSegmentStartTime?: string;
  /**
   * 日期段结束时间
   */
  dateSegmentEndTime?: string;

  /**
   * 任务提醒时间
   */
  taskRemindTime?: string;


  /**
   * 任务等级 <br/>
   * 0: 未表示
   * 1: 重要且紧急
   * 2: 重要不紧急
   * 3: 不重要但紧急
   * 4: 不重要不紧急
   */
  taskLevel?: string;

  /**
   * 任务状态
   */
  taskStatus?: number;

  /**
   * 任务分组名称
   */
  taskGroupId?: string;

  /**
   * 任务自定义分组名称
   */
  taskCustomGroupName?: string;

  /**
   * 标签 id
   */
  tagNameList?: string[];
  /**
   * 是否删除
   */
  deleted?: boolean;

  /**
   * 其他字段
   */
  [key: string]: any;
}


export interface TaskListParams {
  /**
   * 分组 id
   */
  groupId?: string;

  /**
   * 关键字检索
   */
  keyword?: string;

  /**
   * 任务等级
   */
  taskLevel?: string;

  /**
   * 任务状态
   */
  taskStatus?: string;

  /**
   * 任务类型
   */
  taskType?: string;

  /**
   * 查询类型
   * finish: 完结任务
   * deleted: 已删除任务
   */
  searchType?: 'finish' | 'deleted';

  /**
   * 任务时间范围
   */
  taskTimeRange?: number[];

  /**
   * 任务时间开始时间
   */
  taskStartTime?: number;

  /**
   * 任务时间结束时间
   */
  taskEndTime?: number;

  /**
   * 排序字段
   */
  orderBy?: string;
  /**
   * 标签名称
   */
  tagNameList?: string[];

  /**
   * 已过期
   */
  expired?: boolean;

  /**
   * 无时间
   */
  unsetTime?: boolean;

  /**
   * 动态时间
   */
  dynamicTimeTypeList?: string[];

  /**
   * 自定义差额时间
   */
  customDiffTime?: number[];


  /**
   * 分组
   */
  taskGroupIdList?: string[];

  /**
   * 等级
   */
  taskLevelList?: string[];
}

export interface TaskListBatchParams extends TaskListParams {
  /**
   * 查询类型
   * finish: 完结任务
   * deleted: 已删除任务
   */
  searchTypeList?: ('finish' | 'deleted' | 'default')[];
}

export interface ITaskDesc {
  content: string;
  updateAt: number;
}
export type TaskListItem = ITaskItem

/**
 * 任务扩展点
 */
export abstract class TaskExtension extends BaseExtension {

  constructor() {
    super('task')
  }

  /**
   * 保存任务项
   */
  public abstract saveTask(groupId: string, params: TaskSaveParams): Promise<ITaskItem>;

  /**
   * 恢复任务项
   * @param task 任务项
   */
  public abstract restartTask(task: ITaskItem): Promise<void>;

  /**
   * 保存任务项描述
   * @param taskId 任务 id
   * @param content 内容
   */
  public abstract saveTaskDesc(taskId: string, content: string): Promise<void>;

  /**
   * 获取任务项描述
   * @param taskId 任务 id
   */
  public abstract getTaskDesc(taskId: string): Promise<string>;

  /**
   * 清空所有删除的任务
   */
  public abstract cleanAllDeleteTask(): Promise<void>;

  /**
   * 彻底删除任务项
   * @param task
   */
  public abstract completelyDeleteTask(task: ITaskItem): Promise<void>;

  /**
   * 获取任务列表
   * @param params 请求参数
   */
  public abstract listTask(params: TaskListParams): Promise<PageResult<TaskListItem>>;

  /**
   * 批量查询任务列表
   * @param params
   */
  public abstract listTaskBatch(params: TaskListBatchParams): Promise<TaskListItem[]>;

  /**
   * 精确查询任务
   * @param groupId
   * @param taskId
   */
  public abstract getTask(groupId: string, taskId: string): Promise<ITaskItem | null>;

  public abstract rebuildSort(): void;
}
