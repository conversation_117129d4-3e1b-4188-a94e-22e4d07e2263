import { BaseExtension } from './BaseExtension'
import { IGroupInfo, ITaskStatus, ITaskTag } from '../entity'

export interface IGroupCreateParams {
  /**
   * 分组名称
   */
  groupName: string;

  /**
   * 封面 base64
   */
  cover: string;

  /**
   * utools 全局指令
   */
  utoolsKey?: boolean;
}

export interface IGroupListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
}

export interface IGroupListResult {
  total: number;
  list: IGroupInfo[];
}


export type SaveGroupParams = Pick<IGroupInfo, 'groupId'> & Partial<Omit<IGroupInfo, 'groupId'>>;
export abstract class GroupExtension extends BaseExtension {

  constructor() {
    super('group');
  }

  /**
   * 创建分组
   */
  public abstract createGroup(params: IGroupCreateParams): Promise<string>;

  /**
   * 保存分组
   * @param data
   */
  public abstract saveGroup(data: SaveGroupParams): Promise<void>;

  public abstract getGroup(groupId: string): Promise<IGroupInfo>;

  public abstract listGroup(params?: IGroupListParams): Promise<IGroupListResult>;

  /**
   * 根据 groupId 获取这个分组全部状态标签
   * @param groupId 分组 id
   */
  public abstract getTaskStatusList(groupId: string): Promise<ITaskStatus[]>;

  /**
   * 获取指定组的完成任务状态
   *
   * @param groupId 组ID
   * @returns 返回包含完成任务状态的ITaskStatus对象
   */
  public abstract getFinishTaskStatus(groupId: string): Promise<ITaskStatus>;

  /**
   * 删除分组
   * @param groupId
   */
  public abstract deleteGroup(groupId: string): Promise<void>;
}
