import { BaseExtension } from './BaseExtension';
import { ITaskTag } from '../entity';

export interface ITaskTagCreateParam {
  name: string;
  color?: string;
}

export interface ITaskTagMergeParam {
  tagName: string;
  toTagName: string;
}


/**
 * 任务标签扩展点
 */
export abstract class TaskTagExtension extends BaseExtension {

  constructor() {
    super('taskTag');
  }
  /**
   * 新建标签项
   */
  public abstract createTag(tag: ITaskTagCreateParam): Promise<ITaskTag>;

  /**
   * 保存标签项
   */
  public abstract saveTag(tag: ITaskTag): Promise<string>;

  /**
   * 获取标签列表
   */
  public abstract listTag(): Promise<ITaskTag[]>;

  public abstract getTag(tagName: string): Promise<ITaskTag>;

  /**
   * 合并标签项
   */
  public abstract mergeTag(tag: ITaskTagMergeParam): Promise<string>;

  /**
   * 删除标签项
   */
  public abstract deleteTag(tagName: string): Promise<string>;
}
