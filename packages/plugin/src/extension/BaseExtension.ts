

export type extensionType = 'group' | 'task' | 'localStorage' | 'taskTag' | 'ai' | 'taskFilter' | 'flow';

/**
 * 基本扩展点
 */
export abstract class BaseExtension {
  private readonly extensionType: extensionType;

  constructor(type: extensionType) {
    this.extensionType = type;
  }

  get type(): extensionType {
    return this.extensionType;
  }
}
export interface PageParams {
  page: number;
  pageSize: number;
}
export interface PageResult<T> {
  total: number;
  list: T[];
}
