import {ITaskFilter} from "../entity";
import { BaseExtension } from "./BaseExtension";

export type SaveTaskFilterType = Omit<ITaskFilter, 'id' | 'updateAt' | 'createAt' | 'sort'> & {
  id?: string;
  sort?: number;
  updateAt?: number;
  createAt?: number;
};

export abstract class TaskFilterExtension extends BaseExtension {

  constructor() {
    super('taskFilter')
  }

  /**
   * 保存任务过滤器
   * @param taskFilter
   */
  public abstract saveTaskFilter(taskFilter: SaveTaskFilterType): Promise<string>;

  /**
   * 获得任务过滤器列表
   */
  public abstract getTaskFilterList(): Promise<ITaskFilter[]>;

  /**
   * 删除任务过滤器
   * @param id
   */
  public abstract deleteTaskFilter(id: string): Promise<void>;
}
