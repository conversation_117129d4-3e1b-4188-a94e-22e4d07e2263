import { BaseExtension } from './BaseExtension';
import { IAiPrompt } from '../entity';

export type SavePromptParams = Omit<IAiPrompt, 'updateAt' | 'id' | 'sort'> & {
  updateAt?: number;
  id?: string;
  sort?: number;
};

export abstract class AiExtension extends BaseExtension {
  constructor() {
    super('ai');
  }

  /**
   * 获取提示词列表
   */
  abstract getPromptList(): Promise<IAiPrompt[]>;

  /**
   * 保存提示词
   */
  abstract savePrompt(params: SavePromptParams): Promise<void>;

  /**
   * 删除提示词
   */
  abstract deletePrompt(id: string): Promise<void>;
}
