import { BaseExtension } from './BaseExtension';
import type { IFlowItem } from '../entity';


export type SaveFlowItem = Omit<IFlowItem, 'createAt' | 'updateAt' | 'id' | 'sort'> & {
  id?: string;
  createAt?: number;
  updateAt?: number;
  sort?: number;
}
export abstract class FlowExtension extends BaseExtension {

  constructor() {
    super('flow');
  }

  abstract saveFlow(flowItem: SaveFlowItem): Promise<IFlowItem>;

  abstract getFlow(flowId: string): Promise<IFlowItem>;

  abstract getFlowList(): Promise<IFlowItem[]>;

  abstract deleteFlow(flowId: string): Promise<void>;

  abstract importFlow(flowItem: IFlowItem): Promise<IFlowItem>;
}
