{"name": "@xiaou66/todo-plugin", "version": "0.0.1", "description": "Picture bed plugin system", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "types": "./dist/index.d.ts", "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite", "prepare": "npm run build", "publish": "npm publish"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"typescript": "^5.8.2", "vite": "^6.2.1", "vite-plugin-dts": "^4.5.3"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}}}