{"name": "优秀待办", "version": "1.0.0", "pluginName": "优秀待办", "description": "", "author": "<PERSON><PERSON><PERSON>", "homepage": "111", "main": "dist/index.html", "logo": "dist/logo.png", "preload": "src/preload.js", "mainPush": true, "features": [{"code": "function.todo.search", "explain": "", "mainPush": true, "cmds": [{"type": "over", "label": "优秀待办搜索", "exclude": "\n/[\\/\t\n]/", "minLength": 1, "maxLength": 500}]}, {"code": "ui.router?router=taskList", "explain": "优秀待办", "cmds": ["todo", "优秀待办"]}, {"code": "ui.router?router=calendarView", "explain": "优秀待办日历", "cmds": ["日历"]}, {"code": "ui.router?router=fourQuadrant", "explain": "优秀待办四象限", "cmds": ["四象限"]}], "development": {"main": "http://localhost:3001/index.html"}}